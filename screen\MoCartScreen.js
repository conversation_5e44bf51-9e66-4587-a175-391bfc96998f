import { Text } from "react-native-fast-text";
import React, { Component, useReducer, useState, useEffect, useCallback, useRef } from 'react';
import {
  StyleSheet, Image, // View,
  TouchableOpacity, TextInput, RefreshControl, Alert, Modal as ModalComponent,
  Dimensions, ActivityIndicator, Platform, useWindowDimensions, InteractionManager
} from 'react-native';
import { ModalView as ModalViewComponent } from 'react-native-multiple-modals';
import { ScrollView, FlatList } from "react-native-gesture-handler";
import 'react-native-get-random-values';
import { v4 as uuidv4 } from 'uuid';
import Colors from '../constant/Colors';
import * as Cart from '../util/Cart';
import * as User from '../util/User';
import Styles from '../constant/Styles';
import ApiClient from '../util/ApiClient';
import API from '../constant/API';
import SideBar from './SideBar';
import Font<PERSON>wesome from 'react-native-vector-icons/FontAwesome';
import Ionicons from 'react-native-vector-icons/Ionicons';
import Icons from 'react-native-vector-icons/Feather';
import Entypo from 'react-native-vector-icons/Entypo';
import Close from 'react-native-vector-icons/AntDesign';
import AntDesign from 'react-native-vector-icons/AntDesign';
import AIcon from 'react-native-vector-icons/AntDesign';
import Feather from 'react-native-vector-icons/Feather';
// import NumericInput from 'react-native-numeric-input';
import Back from 'react-native-vector-icons/EvilIcons';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import {
  checkIsAllowPromotionVoucherToApply,
  isTablet,
  checkQualifiedItemsQuantityAndAmountForPromotion,
  checkRPQtyStatus,
  checkToApplyTaxOrNot,
  checkToApplyScOrNot,
  logToFile,
  excludeSkipScItems,
  getTransformForModalInsideNavigation,
} from '../util/common';
import { CommonStore } from '../store/commonStore';
import { PRIVILEGES_NAME, CHARGES_TYPE, ORDER_TYPE, EXPAND_TAB_TYPE, OUTLET_SHIFT_STATUS, APP_TYPE, ORDER_TYPE_SUB, PRODUCT_PRICE_TYPE, UNIT_TYPE_SHORT, UNIT_TYPE, ORDER_TYPE_DETAILS, OTHER_DELIVERY_PARTNER_TYPES, OTHER_DELIVERY_PARTNER_TYPES_DROPDOWN_LIST, DISCOUNT_SEQUENCE_TYPES } from '../constant/common';
import firestore from '@react-native-firebase/firestore';
import { Collections } from '../constant/firebase';
import { UserStore } from '../store/userStore';
import { MerchantStore } from '../store/merchantStore';
import AsyncStorage from '@react-native-async-storage/async-storage';
import moment from 'moment';
import AsyncImage from '../components/asyncImage';
import { OutletStore } from '../store/outletStore';
import { APPLY_BEFORE, APPLY_DISCOUNT_PER, APPLY_DISCOUNT_TYPE, PROMOTION_TYPE_VARIATION } from '../constant/promotions';
import { useNetInfo } from "@react-native-community/netinfo";
import APILocal from '../util/apiLocalReplacers';
import { PRINTER_USAGE_TYPE } from '../constant/printer';
import { printUserOrder } from '../util/printer';
import DropDownPicker from 'react-native-dropdown-picker';
import { checkApplyDiscountPerValidity, parseValidIntegerText } from '../util/common';
import { useFocusEffect } from '@react-navigation/native';
import UserIdleWrapper from '../components/userIdleWrapper';
import BigNumber from 'bignumber.js';
import RNPickerSelect from 'react-native-picker-select';
import { TableStore } from "../store/tableStore";
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import CheckBox from '@react-native-community/checkbox';
import { awsBucket } from "../constant/env";
// import Hashids from 'hashids';

// const hashids = new Hashids(TABLE_QR_SALT);
const ModalView = Platform.OS === 'android' ? ModalComponent : ModalViewComponent;

const View = require(
  'react-native/Libraries/Components/View/ViewNativeComponent'
).default;

const MoCartScreen = React.memo((props) => {
  const { navigation, route } = props;

  ///////////////////////////////////////////////////////////

  const [isMounted, setIsMounted] = useState(true);

  useFocusEffect(
    useCallback(() => {
      setIsMounted(true);
      return () => {
        setIsMounted(false);
      };
    }, [])
  );

  ///////////////////////////////////////////////////////////

  const { width: windowWidth, height: windowHeight } = useWindowDimensions();

  const netInfo = useNetInfo();

  const testParam = route.params.test;
  const test1Param = route.params.test1;
  const test2Param = route.params.test2;
  const paymentMethodParam = route.params.paymentMethod;
  const outletDataParam = route.params.outletData;
  const navFromParam = route.params.navFrom;

  const [temp, setTemp] = useState('');

  const [switchMerchant, setSwitchMerchant] = useState(isTablet() ? false : true);
  const [paymentMethod, setPaymentMethod] = useState(paymentMethodParam);
  const [cartItem, setCartItem] = useState([]);
  const [refreshing, setRefreshing] = useState(false);
  const [visible, setVisible] = useState(false);
  const [visible1, setVisible1] = useState(false);
  const [value, setValue] = useState('');
  const [editingItemId, setEditingItemId] = useState(null);
  const [outletData, setOutletData] = useState({});
  const [menu, setMenu] = useState([]);
  const [menuItem, setMenuItem] = useState([]);
  const [category, setCategory] = useState([]);
  const [outletMenu, setOutletMenu] = useState([]);
  const [menuItemDetails, setMenuItemDetails] = useState([]);
  const [qty, setQty] = useState([]);
  const [test, setTest] = useState(testParam);
  const [test1, setTest1] = useState(test1Param);
  const [test2, setTest2] = useState(test2Param);
  const [popularOutlet, setPopularOutlet] = useState([]);
  const [popular, setPopular] = useState([]);
  const [showDateTimePicker, setShowDateTimePicker] = useState(false);
  const [pickerMode, setPickerMode] = useState('datetime');
  const [currentMenu, setCurrentMenu] = useState([]);
  const [type, setType] = useState(Cart.getOrderType()); // 0 "Dine In";
  const [deliveryAddress, setDeliveryAddress] = useState(null);
  const [totalFloat, setTotalFloat] = useState(0);
  const [taxFloat, setTaxFloat] = useState(0);

  const [promotionIdAppliedList, setPromotionIdAppliedList] = useState([]);

  const [totalPrice, setTotalPrice] = useState(0);
  const [totalTax, setTotalTax] = useState(0);
  const [totalSc, setTotalSc] = useState(0);

  const [scOtherDApplied, setScOtherDApplied] = useState(false);

  const [totalDiscount, setTotalDiscount] = useState(0);
  const [discountPromotionsTotal, setDiscountPromotionsTotal] = useState(0);

  const [totalPrepareTime, setTotalPrepareTime] = useState(0);

  const [isCartLoading, setIsCartLoading] = useState(false);

  const [promoCodePromotionDropdownList, setPromoCodePromotionDropdownList] = useState([]);
  const [selectedPromoCodePromotionId, setSelectedPromoCodePromotionId] = useState('');

  const selectedOutletItem = CommonStore.useState((s) => s.selectedOutletItem);

  const selectedPromoCodePromotion = CommonStore.useState(s => s.selectedPromoCodePromotion);

  const outletCustomTaxList = CommonStore.useState(s => s.outletCustomTaxList);
  const totalPriceTaxList = CommonStore.useState(s => s.totalPriceTaxList);

  const allOutletsItemAddOnIdDict = CommonStore.useState((s) => s.allOutletsItemAddOnIdDict);
  const allOutletsItemAddOnChoiceIdDict = CommonStore.useState((s) => s.allOutletsItemAddOnChoiceIdDict);

  const currOutlet = MerchantStore.useState((s) => s.currOutlet);

  // const cartItems = CommonStore.useState((s) => s.cartItems);
  const cartItems = CommonStore.useState((s) => s.cartItemsMo);
  const cartOutletItemsDict = CommonStore.useState(
    (s) => s.cartOutletItemsDict,
  );
  const cartOutletItemAddOnDict = CommonStore.useState(
    (s) => s.cartOutletItemAddOnDict,
  );
  const cartOutletItemAddOnChoiceDict = CommonStore.useState(
    (s) => s.cartOutletItemAddOnChoiceDict,
  );

  const selectedOutletTableMo = CommonStore.useState(
    (s) => s.selectedOutletTableMo,
  );
  const userCart = CommonStore.useState((s) => s.userCart);

  // const cartItemsProcessed = CommonStore.useState((s) => s.cartItemsProcessed);
  const cartItemsProcessed = CommonStore.useState((s) => s.cartItemsMoProcessed);

  const outletsTaxDict = CommonStore.useState((s) => s.outletsTaxDict);

  const orderTypeMo = CommonStore.useState((s) => s.orderTypeMo);
  const orderTypeSubMo = CommonStore.useState((s) => s.orderTypeSubMo);

  const selectedUserAddress = UserStore.useState((s) => s.selectedUserAddress);
  const firebaseUid = UserStore.useState((s) => s.firebaseUid);
  const role = UserStore.useState((s) => s.role)

  const isLoading = CommonStore.useState((s) => s.isLoading);

  const name = UserStore.useState((s) => s.name);

  // const name = MerchantStore.useState((s) => s.merchantId);

  // const userName = UserStore.useState(s => s.name);
  const merchantId = UserStore.useState((s) => s.merchantId);
  const merchantName = MerchantStore.useState((s) => s.name);
  const merchantLogo = MerchantStore.useState((s) => s.logo);

  const availablePromoCodePromotions = CommonStore.useState((s) => s.availablePromoCodePromotions);

  // const promotions = OutletStore.useState((s) => s.promotions);
  const promotionsDict = OutletStore.useState((s) => s.promotionsDict);

  const outletItemsDict = OutletStore.useState((s) => s.outletItemsDict);

  const outletItems = OutletStore.useState((s) => s.outletItems);

  const currTableQRUrl = OutletStore.useState((s) => s.currTableQRUrl);

  const outletPaymentMethods = OutletStore.useState((s) => s.outletPaymentMethods);

  const [showSuccess, setShowSuccess] = useState(false);

  const outletSelectDropdownView = CommonStore.useState(
    (s) => s.outletSelectDropdownView,
  );

  /////////////////////////////////////////////////////////

  const overrideItemPriceSkuDict = CommonStore.useState(
    (s) => s.overrideItemPriceSkuDict,
  );
  const amountOffItemSkuDict = CommonStore.useState(
    (s) => s.amountOffItemSkuDict,
  );
  const percentageOffItemSkuDict = CommonStore.useState(
    (s) => s.percentageOffItemSkuDict,
  );
  const buy1Free1ItemSkuDict = CommonStore.useState(
    (s) => s.buy1Free1ItemSkuDict,
  );
  const deliveryItemSkuDict = CommonStore.useState(
    (s) => s.deliveryItemSkuDict,
  );
  const takeawayItemSkuDict = CommonStore.useState(
    (s) => s.takeawayItemSkuDict,
  );

  const overrideCategoryPriceNameDict = CommonStore.useState(
    (s) => s.overrideCategoryPriceNameDict,
  );
  const amountOffCategoryNameDict = CommonStore.useState(
    (s) => s.amountOffCategoryNameDict,
  );
  const percentageOffCategoryNameDict = CommonStore.useState(
    (s) => s.percentageOffCategoryNameDict,
  );
  const buy1Free1CategoryNameDict = CommonStore.useState(
    (s) => s.buy1Free1CategoryNameDict,
  );
  const deliveryCategoryNameDict = CommonStore.useState(
    (s) => s.deliveryCategoryNameDict,
  );
  const takeawayCategoryNameDict = CommonStore.useState(
    (s) => s.takeawayCategoryNameDict,
  );

  const selectedOutletItemCategoriesDict = OutletStore.useState(s => s.outletCategoriesDict);

  const selectedOutletItemsSkuDict = OutletStore.useState(s => s.outletItemsSkuDict);

  const isCounterOrdering = CommonStore.useState(s => s.isCounterOrdering);

  const [pax, setPax] = useState(1);
  const [editPax, setEditPax] = useState(false);
  const [method, setMethod] = useState('dinein');
  const currOutletId = MerchantStore.useState((s) => s.currOutletId);
  const isOnMenu = CommonStore.useState((s) => s.isOnMenu);
  const [isSelectinfo, setIsSelectinfo] = useState(true);

  const moPax = CommonStore.useState(s => s.moPax);
  const moMethod = CommonStore.useState(s => s.moMethod);

  const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
  const expandTab = CommonStore.useState((s) => s.expandTab);
  const currPageStack = CommonStore.useState((s) => s.currPageStack);

  const currOutletShiftStatus = OutletStore.useState(
    (s) => s.currOutletShiftStatus,
  );

  /////////////////////////////////////////////////////////

  const [isLater, setIsLater] = useState('NOW');
  const [takeawayDateModal, setTakeawayDateModal] = useState(false);
  const [takeawayTimeModal, setTakeawayTimeModal] = useState(false);
  const [scheduleDateTime, setScheduleDateTime] = useState(null)
  const [takeawayDate, setTakeawayDate] = useState(moment());
  const [takeawayTime, setTakeawayTime] = useState(moment());
  const takeawayTimeOption = [
    {
      label: 'Now',
      value: 'NOW',
    },
    {
      label: 'Later',
      value: 'LATER'
    },];

  // const [odpt, setOdpt] = useState(OTHER_DELIVERY_PARTNER_TYPES_DROPDOWN_LIST[0].value);
  // const [odpoi, setOdpoi] = useState('');

  const odpt = CommonStore.useState((s) => s.odpt);
  const odpoi = CommonStore.useState((s) => s.odpoi);

  const selectedOutletItemAddOnOi = CommonStore.useState((s) => s.selectedOutletItemAddOnOi);
  const flatListRef = useRef(null);

  const crmUsers = OutletStore.useState((s) => s.crmUsers);

  const showCustomerList = TableStore.useState(s => s.showCustomerList);
  const searchCustomer = TableStore.useState(s => s.searchCustomer);
  const [searchList, setSearchList] = useState([]);
  const registeredCRMUsersDropdownList = TableStore.useState(s => s.registeredCRMUsersDropdownList);
  const registeredCRMUsersNoLimit = TableStore.useState(s => s.registeredCRMUsersNoLimit);
  const selectedRegisteredCRMUserId = TableStore.useState(s => s.selectedRegisteredCRMUserId);

  const selectedOutletTable = CommonStore.useState((s) => s.selectedOutletTable);
  const isCheckingOutTakeaway = CommonStore.useState((s) => s.isCheckingOutTakeaway);
  const isCustomer = TableStore.useState(s => s.isCustomer);
  const showAddCustomer = TableStore.useState(s => s.showAddCustomer);
  const customerName = TableStore.useState(s => s.customerName);
  const customerUsername = TableStore.useState(s => s.customerUsername);
  const customerUniqueId = TableStore.useState(s => s.customerUniqueId);
  const customerPhone = TableStore.useState(s => s.customerPhone);
  const customerGender = TableStore.useState(s => s.customerGender);
  const customerDob = TableStore.useState(s => s.customerDob);
  const customerEmail = TableStore.useState(s => s.customerEmail);
  const image = TableStore.useState(s => s.image);
  const imageType = TableStore.useState(s => s.imageType);
  const isImageChanged = TableStore.useState(s => s.isImageChanged);

  const customerAddress = TableStore.useState(s => s.customerAddress);
  const customerAddressLat = TableStore.useState(s => s.customerAddressLat);
  const customerAddressLng = TableStore.useState(s => s.customerAddressLng);

  const customerPhoneSecond = TableStore.useState(s => s.customerPhoneSecond);
  const customerAddressSecond = TableStore.useState(s => s.customerAddressSecond);
  const customerAddressLatSecond = TableStore.useState(s => s.customerAddressLatSecond);
  const customerAddressLngSecond = TableStore.useState(s => s.customerAddressLngSecond);

  const [customerIDNum, setCustomerIDNum] = useState('');
  const [customerTIN, setCustomerTIN] = useState('');
  const [customerIdType, setCustomerIdType] = useState('NRIC');
  const [customerEINStreet, setCustomerEINStreet] = useState('');
  const [customerEINCity, setCustomerEINCity] = useState('');
  const [customerEINState, setCustomerEINState] = useState('sgr');
  const [customerEINPostcode, setCustomerEINPostcode] = useState('');
  const [isEdit, setIsEdit] = useState(false);
  const [eInvoiceInfo, setEInvoiceInfo] = useState(false);

  /////////////////////////////////////////////////////////
  useEffect(() => {
    if (flatListRef.current && cartItemsProcessed.length > 0) {
      flatListRef.current.scrollToEnd({ animated: true });
    }
  }, [cartItemsProcessed]);

  useEffect(() => {
    console.log('scheduleDateTime has changed:', scheduleDateTime, ' fdssssssssssssssssssssssssssdsssfdffsfsfdsfs');
  }, [scheduleDateTime]);

  useEffect(() => {
    var cartItemsTemp = [];
    var existedCartItemDict = {};

    for (var i = 0; i < cartItems.length; i++) {
      var cartItem = cartItems[i];
      if (existedCartItemDict[cartItem.itemId + cartItem.cartItemDate]) {
        // skip
      }
      else {
        existedCartItemDict[cartItem.itemId + cartItem.cartItemDate] = true;

        cartItemsTemp.push(cartItem);
      }
    }

    CommonStore.update(s => {
      s.cartItemsMo = cartItemsTemp;
    });
  }, [cartItems]);

  useEffect(() => {
    InteractionManager.runAfterInteractions(() => {
      var promoCodePromotionDropdownListTemp = [{
        label: 'N/A',
        value: '',
      }].concat(availablePromoCodePromotions.map(item => ({
        label: `${item.campaignName} (${item.promoCodeUsageLimit} left)`,
        value: item.uniqueId,
      })));

      if (selectedPromoCodePromotionId === '' && promoCodePromotionDropdownListTemp.length > 0) {
        setSelectedPromoCodePromotionId(promoCodePromotionDropdownListTemp[0].value);

        CommonStore.update(s => {
          s.selectedPromoCodePromotion = availablePromoCodePromotions.find(promotion => promotion.uniqueId === promoCodePromotionDropdownListTemp[0].value) || {};
        });
      }

      setPromoCodePromotionDropdownList(promoCodePromotionDropdownListTemp);
    });
  }, [
    // availableLoyaltyCampaigns,
    // selectedTaggableVoucherId
    availablePromoCodePromotions,
  ]);

  // useEffect(() => {
  //   CommonStore.update((s) => {
  //     s.orderTypeMo = ORDER_TYPE.DINEIN;
  //     s.orderTypeSubMo = ORDER_TYPE_SUB.NORMAL;
  //     s.moMethod = 'dinein';
  //   });
  //   setIsLater('NOW');
  //   setScheduleDateTime(null);
  // }, [])


  useEffect(() => {
    // if (cartItems.length > 0) {
    //   updateCartItemsDict();
    // }

    // InteractionManager.runAfterInteractions(() => {
    //   updateCartItemsDict();
    // });    

    if (isMounted) {
      updateCartItemsDict();
    }
  }, [
    isMounted,

    cartItems,

    currOutlet,

    overrideItemPriceSkuDict,
    amountOffItemSkuDict,
    percentageOffItemSkuDict,
    buy1Free1ItemSkuDict,
    deliveryItemSkuDict,
    takeawayItemSkuDict,

    overrideCategoryPriceNameDict,
    amountOffCategoryNameDict,
    percentageOffCategoryNameDict,
    buy1Free1CategoryNameDict,
    deliveryCategoryNameDict,
    takeawayCategoryNameDict,

    allOutletsItemAddOnIdDict,
    allOutletsItemAddOnChoiceIdDict,
  ]);

  useEffect(() => {
    console.log('useEffect - Table - 16');

    if (
      global.crmUsersDtUse !== global.crmUsersDt &&
      crmUsers.length > 0 &&
      showCustomerList
    ) {
      global.crmUsersDtUse = global.crmUsersDt;

      InteractionManager.runAfterInteractions(() => {
        const startTime = performance.now();

        const updatedDropdownList = crmUsers.map((item) => ({
          label: item.name ? item.name.trim() : 'N/A',
          value: item.userId || item.email,
          uniqueId: item.uniqueId || '',
          phoneNumber: item.number || '',
        }));

        TableStore.update(s => {
          s.registeredCRMUsersDropdownList = updatedDropdownList;
          if (updatedDropdownList.length > 0 && !s.selectedRegisteredCRMUserId) {
            s.selectedRegisteredCRMUserId = updatedDropdownList[0].value;
          }
        });

        console.log(`useEffect 12: took ${performance.now() - startTime} milliseconds.`);
      });
    }
  }, [crmUsers, showCustomerList]);

  useEffect(() => {
    console.log('useEffect - Table - 16-no limit');

    if (
      searchList.length > 0 &&
      showCustomerList &&
      searchCustomer.length > 0
    ) {
      global.crmUsersDtUse = global.crmUsersDt;

      InteractionManager.runAfterInteractions(() => {
        const startTime = performance.now();

        global.searchCrmUserList = searchList;

        const updatedDropdownList = searchList.map((item) => ({
          label: item.name ? item.name.trim() : 'N/A',
          value: item.userId || item.email,
          uniqueId: item.uniqueId || '',
          phoneNumber: item.number || '',
        }));

        TableStore.update(s => {
          s.registeredCRMUsersNoLimit = updatedDropdownList;
          if (updatedDropdownList.length > 0 && !s.selectedRegisteredCRMUserId) {
            s.selectedRegisteredCRMUserId = updatedDropdownList[0].value;
          }
        });

        console.log(`useEffect 12: took ${performance.now() - startTime} milliseconds.`);
      });
    }
  }, [searchList, showCustomerList, searchCustomer]);

  const searchUsers = async (searchTerm) => {
    try {
      if (awsBucket === 'koodooprod') {

        const body = {
          query: searchTerm,
          outletId: currOutletId,
        };

        ApiClient.POST(API.msGetCrmUser, body)
          .then((result) => {
            if (result && result.status === 'success' &&
              result.data && result.data.length > 0
            ) {

              setSearchList(result.data);
            } else {
              Alert.alert('Info', 'No customers found')
              setSearchList([]);
            }

            CommonStore.update((s) => {
              s.isLoading = false;
            });
          })
          .catch((err) => {

            Alert.alert('Info', 'No customers found')
            setSearchList([]);

            CommonStore.update((s) => {
              s.isLoading = false;
            });
          });
      }
      else {
        const userSnapshot = await firebase.firestore()
          .collection(Collections.CRMUser)
          .where('ngram', 'array-contains', searchTerm)
          .where('outletId', '==', currOutletId)
          .where('deletedAt', '==', null)
          .get();

        if (userSnapshot.size > 0) {

          const users = userSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
          console.log('search users result:', users);

          setSearchList(users);

          CommonStore.update((s) => {
            s.isLoading = false;
          })
        } else {
          Alert.alert('Info', 'No customers found')
          console.log('No customers found');

          setSearchList([]);

          CommonStore.update((s) => {
            s.isLoading = false;
          })
        }
      }
    } catch (error) {
      console.error('Error fetching users:', error);

      setSearchList([]);

      CommonStore.update((s) => {
        s.isLoading = false;
      })
    }
  };

  const PhoneonNumPadBtn = (key) => {
    var plus = customerPhone.split('+')[1];
    if (key >= 0 || key == '+') {
      var phoneLength = 12;
      if (customerPhone.startsWith('+6011')) {
        phoneLength = 13;
      }

      if (customerPhone.includes('+')) {
        if (customerPhone.length < phoneLength && plus.length < phoneLength) {
          TableStore.update(s => {
            s.customerPhone = customerPhone + key;
          });
        }
      }
      if (!customerPhone.includes('+')) {

        TableStore.update(s => {
          s.customerPhone = customerPhone + key;
        });
      }
    } else {
      if (customerPhone.length > 0) {

        TableStore.update(s => {
          s.customerPhone = customerPhone.slice(0, key);
        });
      }

      TableStore.update(s => {
        s.customerPhone = customerPhone.slice(0, key);
      });
    }
  };

  const renderCrmUserHandler = (item) => {
    TableStore.update(s => {
      s.selectedRegisteredCRMUserId = item.item.value;
      // s.customerName = item.item.label;
      // s.customerPhone = item.item.phoneNumber;
      // s.customerUniqueId = item.item.uniqueId;
      s.isCustomer = true;
    });

    // var crmUser = crmUsers.find(
    //   (user) =>
    //     user.userId === selectedRegisteredCRMUserId ||
    //     user.email === selectedRegisteredCRMUserId,
    // );
    // if (crmUser) {
    //   CommonStore.update(s => {
    //     s.selectedCustomerEdit = crmUser;
    //   });
    // }
  };

  const renderCrmUser = (item, index) => {
    return (
      <TouchableOpacity
        style={{
          backgroundColor:
            selectedRegisteredCRMUserId === item.item.value
              ? Colors.fieldtBgColor
              : Colors.whiteColor,
          width: '100%',
          borderTopWidth: 1,
          borderColor: Colors.fieldtBgColor2,
          paddingVertical: 10,
          paddingHorizontal: 15,
          flexDirection: 'row',
        }}
        onPress={() => {
          renderCrmUserHandler(item);
        }}>
        <Text
          numberOfLines={1}
          style={[
            styles.modalDescText,
            switchMerchant
              ? {
                fontSize: 10,
                flex: 1,
              }
              : {
                flex: 1,
              },
          ]}>
          {item.item.label}
        </Text>
        <Text
          numberOfLines={1}
          style={[
            styles.modalDescText,
            switchMerchant
              ? {
                fontSize: 10,
                flex: 1,
              }
              : { flex: 1 },
          ]}>
          {item.item.phoneNumber || ''}
        </Text>
      </TouchableOpacity>
    );
  };

  const createCRMUser = async (isAutoPush = false) => {
    if (

      !customerPhone ||
      !customerName

    ) {
      Alert.alert(
        'Error',
        'Please fill in all required information:\nName\nContact number',
        [{ text: 'OK', onPress: () => { } }],
        { cancelable: false },
      );

    } else {

      var parsedPhone = customerPhone.startsWith('+') ? customerPhone.slice(1) : customerPhone;
      if (parsedPhone.length > 0 && !parsedPhone.startsWith('6')) {
        parsedPhone = `6${parsedPhone}`;
      }

      var sameUser = crmUsers.find(crmUser => {
        if (customerEmail && crmUser.email === customerEmail) {
          return true;
        }

        if (parsedPhone && crmUser.number === parsedPhone) {
          return true;
        }
      });

      if (sameUser && isEdit === false) {
        Alert.alert(
          'Info',
          'Existing email and/or phone number found, please try another one.',
          [{ text: 'OK', onPress: () => { } }],
          { cancelable: false },
        );
        return;
      }

      if (parsedPhone.length !== 11 && parsedPhone.length !== 12) {
        Alert.alert(
          'Info',
          'Please enter valid phone number.'
        );
        return;
      }

      var profileImageImagePath = '';
      var profileImageCommonIdLocal = uuidv4();

      if (image && imageType) {

        profileImageImagePath = await uploadImageToFirebaseStorage(
          {
            uri: image,
            type: imageType,
          },
          `/merchant/${merchantId}/crm/user/${profileImageCommonIdLocal}/image${imageType}`,
        );
      }

      try {
        CommonStore.update((s) => {
          s.isLoading = true;
        });

        var body = {
          merchantId,
          merchantName,
          outletId: currOutletId,

          avatar: `https:
              customerName
            ).replace(/%20/g, '+')}`,
          isImageChanged,
          dob: customerDob,
          email: customerEmail || '',
          gender: customerGender,
          name: customerName,
          number: parsedPhone,
          uniqueName: customerUsername || '',

          epNameTo: customerName || '',
          epPhoneTo: customerPhone || '',

          epAddr1To: customerEINStreet || '',
          epCityTo: customerEINCity || '',
          epCodeTo: customerEINPostcode || '',
          epStateTo: customerEINState || '',

          emailSecond: customerEmail || '',
          tin: customerTIN || '',
          eiIdType: customerIdType || '',
          eiId: customerIDNum || '',

          address: customerAddress,
          lat: parseFloat(customerAddressLat),
          lng: parseFloat(customerAddressLng),

          numberSecond: customerPhoneSecond,
          addressSecond: customerAddressSecond,
          latSecond: parseFloat(customerAddressLatSecond),
          lngSecond: parseFloat(customerAddressLngSecond),

          timeline: {},

          commonId: profileImageCommonIdLocal,
        };

        const result = await ApiClient.POST(API.createCRMUser, body, false);

        if (result && result.status === 'success') {
          Alert.alert(
            'Success',
            'Customer has been saved',
            [
              {
                text: 'OK',
                onPress: () => {

                  TableStore.update(s => {
                    s.searchCustomer = customerName;
                    s.showAddCustomer = false;
                  });
                },
              },
            ],
            { cancelable: false },
          );
        }
        else {
          Alert.alert('Error', 'Failed to create customer')
        }
      }
      catch (err) {
        console.log('createCRMUser err', err)
        Alert.alert('Error', "Failed to create customer")
      }
      finally {
        CommonStore.update((s) => {
          s.isLoading = false;
        });
      }
    }
  };

  const updateCartItemsDict = async () => {
    setIsCartLoading(true);

    var tempCartOutletItemsDict = {
      ...cartOutletItemsDict,
    };

    var tempCartOutletItemAddOnDict = {
      ...cartOutletItemAddOnDict,
    };

    var tempCartOutletItemAddOnChoiceDict = {
      ...cartOutletItemAddOnChoiceDict,
    };

    var tempOutletsTaxDict = {
      ...outletsTaxDict,
    };

    var promotionIdAppliedListTemp = [];

    var tempCartItemsProcessed = [];

    var tempTotalPrice = 0;
    var tempTotalPriceSp = 0;
    var tempTotalPrepareTime = 0;

    var tempTotalPriceTaxDict = {};

    var currOutletId = '';

    ////////////////////////////////////////

    var cartItemQuantitySummaries = [];

    var discountPromotionsTotalTemp = 0;

    var takeawayDiscountAmount = 0;

    let rpQtyByPromotionIdDict = {};

    global.rpCategoryIdUsedPromoDict = {};

    ////////////////////////////////////////

    let rpCategoryIdUsedPromoDict = {};

    const cartItemsClone = [...cartItems];

    ////////////////////////////////////////

    //store the original sorting index first
    let cartItemIdOrderIndexDict = cartItems.reduce((dict, cartItem, cartItemIndex) => {
      dict[cartItem.itemId + cartItem.cartItemDate] = cartItemIndex;
      return dict;
    }, {});

    //here can sort based on low to high prices, or high to low prices, or didn't do anything
    //rmb to replace the bottom 'cartItems' with cartItemsSorted also
    let cartItemsSorted = [...cartItems]
    // if (/* (selectedTaggableVoucher && selectedTaggableVoucher.discSequence === DISCOUNT_SEQUENCE_TYPES.LOWEST_PRICE) || */
    //   (selectedPromoCodePromotion && selectedPromoCodePromotion.discSequence === DISCOUNT_SEQUENCE_TYPES.LOWEST_PRICE)) {
    //   cartItemsSorted.sort((a, b) => a.priceTemp - b.priceTemp);
    // } else if (/* (selectedTaggableVoucher && selectedTaggableVoucher.discSequence === DISCOUNT_SEQUENCE_TYPES.HIGHEST_PRICE) || */
    //   (selectedPromoCodePromotion && selectedPromoCodePromotion.discSequence === DISCOUNT_SEQUENCE_TYPES.HIGHEST_PRICE)) {
    //   cartItemsSorted.sort((a, b) => b.priceTemp - a.priceTemp);
    // } else if (/* (selectedTaggableVoucher && selectedTaggableVoucher.discSequence === DISCOUNT_SEQUENCE_TYPES.FIRST) || */
    //   (selectedPromoCodePromotion && selectedPromoCodePromotion.discSequence === DISCOUNT_SEQUENCE_TYPES.FIRST)) {
    //   // Already in the original order, no need to sort
    //   cartItemsSorted = [...cartItems];
    // }

    cartItemsSorted.sort((a, b) => (a.priceTemp / a.quantity) - (b.priceTemp / b.quantity));

    ///////////////////////////////////////////////////////////
    ///////////////////////////////////////////////////////////
    ///////////////////////////////////////////////////////////

    // 2025-01-13 - calculate tempTotalPriceSp

    for (var i = 0; i < cartItemsSorted.length; i++) {
      const tempCartItem = cartItemsSorted[i];

      if (tempCartOutletItemsDict[tempCartItem.itemId] === undefined) {
        // need retrive the actual item, to show price, pic, etc

        let foundItem = outletItems.find(findItem => findItem.uniqueId === tempCartItem.itemId);

        if (foundItem) {
          tempCartOutletItemsDict[tempCartItem.itemId] =
            foundItem;
        }
      }

      var extraPrice = 0;
      if (
        orderTypeMo === ORDER_TYPE.DELIVERY &&
        currOutlet &&
        currOutlet.deliveryPrice
      ) {
        extraPrice = currOutlet.deliveryPrice;
      } else if (
        orderTypeMo === ORDER_TYPE.PICKUP &&
        currOutlet &&
        currOutlet.pickUpPrice
      ) {
        extraPrice = currOutlet.pickUpPrice;
      }

      if (orderTypeMo === ORDER_TYPE.DELIVERY) {
        extraPrice = tempCartOutletItemsDict[tempCartItem.itemId].deliveryCharges || 0;

        if (extraPrice && tempCartOutletItemsDict[tempCartItem.itemId].deliveryChargesType === CHARGES_TYPE.PERCENTAGE_BASED) {
          // extraPrice = tempCartOutletItemsDict[tempCartItem.itemId].price * extraPrice / 100;
          extraPrice = BigNumber(tempCartOutletItemsDict[tempCartItem.itemId].price).multipliedBy(extraPrice).dividedBy(100).toNumber();
        }

        if (!tempCartOutletItemsDict[tempCartItem.itemId].deliveryChargesActive) {
          extraPrice = 0;
        }
      }

      if (orderTypeMo === ORDER_TYPE.PICKUP) {
        extraPrice = tempCartOutletItemsDict[tempCartItem.itemId].pickUpCharges || 0;

        if (extraPrice && tempCartOutletItemsDict[tempCartItem.itemId].pickUpChargesType === CHARGES_TYPE.PERCENTAGE_BASED) {
          // extraPrice = tempCartOutletItemsDict[tempCartItem.itemId].price * extraPrice / 100;
          extraPrice = BigNumber(tempCartOutletItemsDict[tempCartItem.itemId].price).multipliedBy(extraPrice).dividedBy(100).toNumber();
        }

        if (!tempCartOutletItemsDict[tempCartItem.itemId].pickUpChargesActive) {
          extraPrice = 0;
        }
      }

      // var tempCartItemPriceOriginal =
      //   tempCartOutletItemsDict[tempCartItem.itemId].price + extraPrice;

      var tempCartItemPriceOriginal =
        BigNumber(tempCartOutletItemsDict[tempCartItem.itemId].price).plus(extraPrice).toNumber();

      var tempCartItemPrice =
        BigNumber(tempCartOutletItemsDict[tempCartItem.itemId].price).plus(extraPrice).toNumber();

      // tempCartItemPrice = tempCartItem.quantity * tempCartOutletItemsDict[tempCartItem.itemId].price;
      // tempCartItemPrice = overrideItemPriceSkuDict[tempCartItem.itemSku] !== undefined ? overrideItemPriceSkuDict[tempCartItem.itemSku] : tempCartOutletItemsDict[tempCartItem.itemId].price;

      //////////////////////////////////////////////////////////

      if (tempCartItem.priceVariable !== undefined) {
        tempCartItemPrice = tempCartItem.priceVariable;

        tempCartItemPriceOriginal = tempCartItem.priceVariable;
      }

      //////////////////////////////////////////////////////////

      if (tempCartItem.priceUpselling !== undefined) {
        tempCartItemPrice = tempCartItem.priceUpselling;

        tempCartItemPriceOriginal = tempCartOutletItemsDict[tempCartItem.itemId].price + extraPrice;
      }

      //////////////////////////////////////////////////////////

      if (tempCartItem.discountedPrice !== undefined) {
        tempCartItemPrice = tempCartItem.discountedPrice;

        tempCartItemPriceOriginal = tempCartOutletItemsDict[tempCartItem.itemId].price + extraPrice;
      }

      //////////////////////////////////////////////////////////

      tempCartItemPrice *= tempCartItem.quantity;

      tempTotalPriceSp = BigNumber(tempTotalPriceSp).plus(tempCartItemPrice).toNumber();
    }

    ///////////////////////////////////////////////////////////
    ///////////////////////////////////////////////////////////
    ///////////////////////////////////////////////////////////

    ////////////////////////////////////////

    for (var i = 0; i < cartItemsSorted.length; i++) {
      const tempCartItem = cartItemsSorted[i];

      var tempCartItemPrice = 0;

      var tempCartItemAddOnCategorized = {};
      var tempCartItemAddOnCategorizedPrice = {};

      var tempCartItemAddOnParsed = [];

      if (tempCartOutletItemsDict[tempCartItem.itemId] === undefined) {
        // need retrive the actual item, to show price, pic, etc

        // const outletItemSnapshot = await firestore()
        //   .collection(Collections.OutletItem)
        //   .where('uniqueId', '==', tempCartItem.itemId)
        //   .limit(1)
        //   .get();

        // if (!outletItemSnapshot.empty) {
        //   tempCartOutletItemsDict[tempCartItem.itemId] =
        //     outletItemSnapshot.docs[0].data();
        // }

        let foundItem = outletItems.find(findItem => findItem.uniqueId === tempCartItem.itemId);

        if (foundItem) {
          tempCartOutletItemsDict[tempCartItem.itemId] =
            foundItem;
        }
      }

      // tempCartItemPrice = tempCartItem.quantity * tempCartOutletItemsDict[tempCartItem.itemId].price;
      tempCartItemPrice = tempCartOutletItemsDict[tempCartItem.itemId].price;

      //////////////////////////////////////////////////////////

      currOutletId = tempCartOutletItemsDict[tempCartItem.itemId].outletId;

      // if (tempOutletsTaxDict[currOutletId] === undefined) {
      //   // need retrieve the tax rate of this outlet

      //   const outletTaxSnapshot = await firestore()
      //     .collection(Collections.OutletTax)
      //     .where('outletId', '==', currOutletId)
      //     .limit(1)
      //     .get();

      //   if (!outletTaxSnapshot.empty) {
      //     tempOutletsTaxDict[currOutletId] = outletTaxSnapshot.docs[0].data();
      //   }
      // }

      //////////////////////////////////////////////////////////

      var extraPrice = 0;
      if (orderTypeMo === ORDER_TYPE.DELIVERY &&
        currOutlet &&
        currOutlet.deliveryPrice) {
        extraPrice = currOutlet.deliveryPrice;
      }
      else if (orderTypeMo === ORDER_TYPE.PICKUP &&
        orderTypeSubMo === ORDER_TYPE_SUB.NORMAL &&
        currOutlet &&
        currOutlet.pickUpPrice) {
        extraPrice = currOutlet.pickUpPrice;
      }

      if (orderTypeMo === ORDER_TYPE.DELIVERY && tempCartOutletItemsDict[tempCartItem.itemId].deliveryChargesActive) {
        extraPrice = tempCartOutletItemsDict[tempCartItem.itemId].deliveryCharges || 0;

        if (extraPrice && tempCartOutletItemsDict[tempCartItem.itemId].deliveryChargesType === CHARGES_TYPE.PERCENTAGE_BASED) {
          // extraPrice = tempCartOutletItemsDict[tempCartItem.itemId].price * extraPrice / 100;
          extraPrice = BigNumber(tempCartOutletItemsDict[tempCartItem.itemId].price).multipliedBy(extraPrice).dividedBy(100).toNumber();
        }
      }
      // else {
      //   extraPrice = 0;
      // }

      if (orderTypeMo === ORDER_TYPE.PICKUP && orderTypeSubMo === ORDER_TYPE_SUB.NORMAL && tempCartOutletItemsDict[tempCartItem.itemId].pickUpChargesActive) {
        extraPrice = tempCartOutletItemsDict[tempCartItem.itemId].pickUpCharges || 0;

        if (extraPrice && tempCartOutletItemsDict[tempCartItem.itemId].pickUpChargesType === CHARGES_TYPE.PERCENTAGE_BASED) {
          // extraPrice = tempCartOutletItemsDict[tempCartItem.itemId].price * extraPrice / 100;
          extraPrice = BigNumber(tempCartOutletItemsDict[tempCartItem.itemId].price).multipliedBy(extraPrice).dividedBy(100).toNumber();
        }
      }
      // else {
      //   extraPrice = 0;
      // }

      if (orderTypeMo === ORDER_TYPE.PICKUP && orderTypeSubMo === ORDER_TYPE_SUB.OTHER_DELIVERY && tempCartOutletItemsDict[tempCartItem.itemId].otherDChargesActive) {
        extraPrice = tempCartOutletItemsDict[tempCartItem.itemId].otherDCharges || 0;

        if (extraPrice && tempCartOutletItemsDict[tempCartItem.itemId].otherDChargesType === CHARGES_TYPE.PERCENTAGE_BASED) {
          // extraPrice = tempCartOutletItemsDict[tempCartItem.itemId].price * extraPrice / 100;
          extraPrice = BigNumber(tempCartOutletItemsDict[tempCartItem.itemId].price).multipliedBy(extraPrice).dividedBy(100).toNumber();
        }
      }

      // var tempCartItemPriceOriginal =
      //   tempCartOutletItemsDict[tempCartItem.itemId].price + extraPrice;

      var tempCartItemPriceOriginal =
        BigNumber(tempCartOutletItemsDict[tempCartItem.itemId].price).plus(extraPrice).toNumber();

      //////////////////////////////////////////////////////////

      if (tempCartItem.priceVariable !== undefined) {
        tempCartItemPrice = tempCartItem.priceVariable;

        tempCartItemPriceOriginal = tempCartItem.priceVariable;
      }

      //////////////////////////////////////////////////////////

      var promotionIdTemp = '';
      // var applyDiscountPer = '';

      var cartItemPromotionIdListTemp = [];

      //////////////////////////////////////////////////////////

      // promotion without promo code

      var overrideCategoryPrice = undefined;
      var overridePromotionId = undefined;
      if (
        selectedOutletItemCategoriesDict[tempCartItem.categoryId] &&
        overrideCategoryPriceNameDict[
        selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
        ] !== undefined
        && overrideCategoryPriceNameDict[
          selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
        ].usePromoCode === false
        &&
        checkIsAllowPromotionVoucherToApply(
          currOutlet.allowStackedPromotionVoucher,
          APPLY_DISCOUNT_TYPE.PROMOTION,
          overrideCategoryPriceNameDict[
          selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
          ],
          selectedPromoCodePromotion,
          {},
          promotionIdAppliedListTemp,
        )
      ) {
        // overrideCategoryPrice =
        //   overrideCategoryPriceNameDict[
        //     selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
        //   ].overridePrice + extraPrice;
        overrideCategoryPrice =
          BigNumber(overrideCategoryPriceNameDict[
            selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
          ].overridePrice).toNumber();
        overridePromotionId = overrideCategoryPriceNameDict[
          selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
        ].promotionId;
      }

      if (overrideItemPriceSkuDict[tempCartItem.itemSku] !== undefined
        && overrideItemPriceSkuDict[tempCartItem.itemSku].usePromoCode === false
        &&
        checkIsAllowPromotionVoucherToApply(
          currOutlet.allowStackedPromotionVoucher,
          APPLY_DISCOUNT_TYPE.PROMOTION,
          overrideItemPriceSkuDict[tempCartItem.itemSku],
          selectedPromoCodePromotion,
          {},
          promotionIdAppliedListTemp,
        )
        // &&
        // (overrideItemPriceSkuDict[tempCartItem.itemSku].applyBefore === APPLY_BEFORE.ORDER_PLACED || overrideItemPriceSkuDict[tempCartItem.itemSku].applyBefore === undefined)
      ) {
        // tempCartItemPrice = overrideItemPriceSkuDict[tempCartItem.itemSku].overridePrice + extraPrice;
        tempCartItemPrice = BigNumber(overrideItemPriceSkuDict[tempCartItem.itemSku].overridePrice).plus(extraPrice).toNumber();
        promotionIdAppliedListTemp.push(overrideItemPriceSkuDict[tempCartItem.itemSku].promotionId);

        promotionIdTemp = overrideItemPriceSkuDict[tempCartItem.itemSku].promotionId;
        // applyDiscountPer = overrideItemPriceSkuDict[tempCartItem.itemSku].applyDiscountPer || APPLY_DISCOUNT_PER.ORDER;

        cartItemPromotionIdListTemp.push(promotionIdTemp);

      } else if (overrideCategoryPrice !== undefined
        // &&
        // (overrideCategoryPrice.applyBefore === APPLY_BEFORE.ORDER_PLACED || overrideCategoryPrice.applyBefore === undefined)
      ) {
        // tempCartItemPrice = overrideCategoryPrice + extraPrice;
        tempCartItemPrice = BigNumber(overrideCategoryPrice).plus(extraPrice).toNumber();
        promotionIdAppliedListTemp.push(overridePromotionId);

        promotionIdTemp = overridePromotionId;
        // applyDiscountPer = APPLY_DISCOUNT_PER.ORDER;

        cartItemPromotionIdListTemp.push(promotionIdTemp);

      } else if (tempCartItem.priceVariable === undefined) {
        // tempCartItemPrice = tempCartOutletItemsDict[tempCartItem.itemId].price + extraPrice;
        tempCartItemPrice = BigNumber(tempCartOutletItemsDict[tempCartItem.itemId].price).plus(extraPrice).toNumber();
      }
      else {
        tempCartItemPrice = tempCartItem.priceVariable;
      }

      // =============================================================

      overrideCategoryPrice = undefined;
      overridePromotionId = undefined;
      if (
        selectedOutletItemCategoriesDict[tempCartItem.categoryId] &&
        overrideCategoryPriceNameDict[
        selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
        ] !== undefined
        && overrideCategoryPriceNameDict[
          selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
        ].usePromoCode === true
        &&
        checkIsAllowPromotionVoucherToApply(
          currOutlet.allowStackedPromotionVoucher,
          APPLY_DISCOUNT_TYPE.PROMOTION_PROMO_CODE,
          overrideCategoryPriceNameDict[
          selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
          ],
          selectedPromoCodePromotion,
          {},
          promotionIdAppliedListTemp,
        )
      ) {
        // overrideCategoryPrice =
        //   overrideCategoryPriceNameDict[
        //     selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
        //   ].overridePrice + extraPrice;
        overrideCategoryPrice =
          BigNumber(overrideCategoryPriceNameDict[
            selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
          ].overridePrice).toNumber();
        overridePromotionId = overrideCategoryPriceNameDict[
          selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
        ].promotionId;
      }

      if (overrideItemPriceSkuDict[tempCartItem.itemSku] !== undefined
        && overrideItemPriceSkuDict[tempCartItem.itemSku].usePromoCode === true
        &&
        checkIsAllowPromotionVoucherToApply(
          currOutlet.allowStackedPromotionVoucher,
          APPLY_DISCOUNT_TYPE.PROMOTION_PROMO_CODE,
          overrideItemPriceSkuDict[tempCartItem.itemSku],
          selectedPromoCodePromotion,
          {},
          promotionIdAppliedListTemp,
        )
        // &&
        // (overrideItemPriceSkuDict[tempCartItem.itemSku].applyBefore === APPLY_BEFORE.ORDER_PLACED || overrideItemPriceSkuDict[tempCartItem.itemSku].applyBefore === undefined)
      ) {
        // tempCartItemPrice = overrideItemPriceSkuDict[tempCartItem.itemSku].overridePrice + extraPrice;
        tempCartItemPrice = BigNumber(overrideItemPriceSkuDict[tempCartItem.itemSku].overridePrice).plus(extraPrice).toNumber();
        promotionIdAppliedListTemp.push(overrideItemPriceSkuDict[tempCartItem.itemSku].promotionId);

        promotionIdTemp = overrideItemPriceSkuDict[tempCartItem.itemSku].promotionId;
        // applyDiscountPer = overrideItemPriceSkuDict[tempCartItem.itemSku].applyDiscountPer || APPLY_DISCOUNT_PER.ORDER;

        cartItemPromotionIdListTemp.push(promotionIdTemp);

      } else if (overrideCategoryPrice !== undefined
        // &&
        // (overrideCategoryPrice.applyBefore === APPLY_BEFORE.ORDER_PLACED || overrideCategoryPrice.applyBefore === undefined)
      ) {
        // tempCartItemPrice = overrideCategoryPrice + extraPrice;
        tempCartItemPrice = BigNumber(overrideCategoryPrice).plus(extraPrice).toNumber();
        promotionIdAppliedListTemp.push(overridePromotionId);

        promotionIdTemp = overridePromotionId;
        // applyDiscountPer = APPLY_DISCOUNT_PER.ORDER;

        cartItemPromotionIdListTemp.push(promotionIdTemp);

      } else {
        // 2023-04-06 - no need this first

        // if (tempCartItem.priceVariable === undefined) {
        //   // tempCartItemPrice = tempCartOutletItemsDict[tempCartItem.itemId].price + extraPrice;
        //   tempCartItemPrice = BigNumber(tempCartOutletItemsDict[tempCartItem.itemId].price).plus(extraPrice).toNumber();
        // }
        // else {
        //   tempCartItemPrice = tempCartItem.priceVariable;
        // }
      }

      //////////////////////////////////////////////////////////

      if (tempCartItem.choices) {
        const tempCartItemChoices = Object.entries(tempCartItem.choices).map(
          ([key, value]) => ({ key, value }),
        );

        for (var j = 0; j < tempCartItemChoices.length; j++) {
          if (tempCartItemChoices[j].value) {
            // means the addon of this item is picked, need to retrieve the actual addon

            if (
              tempCartOutletItemAddOnChoiceDict[tempCartItemChoices[j].key] ===
              undefined
            ) {
              // const outletItemAddOnChoiceSnapshot = await firestore()
              //   .collection(Collections.OutletItemAddOnChoice)
              //   .where('uniqueId', '==', tempCartItemChoices[j].key)
              //   .limit(1)
              //   .get();

              // if (!outletItemAddOnChoiceSnapshot.empty) {
              //   tempCartOutletItemAddOnChoiceDict[tempCartItemChoices[j].key] =
              //     outletItemAddOnChoiceSnapshot.docs[0].data();
              // }

              if (allOutletsItemAddOnChoiceIdDict[tempCartItemChoices[j].key]) {
                tempCartOutletItemAddOnChoiceDict[tempCartItemChoices[j].key] =
                  allOutletsItemAddOnChoiceIdDict[tempCartItemChoices[j].key];
              }
            }

            if (tempCartItem.priceVariable === undefined) {
              // tempCartItemPrice +=
              //   tempCartOutletItemAddOnChoiceDict[tempCartItemChoices[j].key]
              //     .price;

              // tempCartItemPriceOriginal +=
              //   tempCartOutletItemAddOnChoiceDict[tempCartItemChoices[j].key]
              //     .price;

              tempCartItemPrice =
                BigNumber(tempCartItemPrice).plus(tempCartOutletItemAddOnChoiceDict[tempCartItemChoices[j].key]
                  .price).toNumber();

              tempCartItemPriceOriginal =
                BigNumber(tempCartItemPriceOriginal).plus(tempCartOutletItemAddOnChoiceDict[tempCartItemChoices[j].key].price).toNumber();
            }

            // need to retrieve the description/type name of this addon choice

            const tempCartItemAddOnChoice =
              tempCartOutletItemAddOnChoiceDict[tempCartItemChoices[j].key];

            if (
              tempCartOutletItemAddOnDict[
              tempCartItemAddOnChoice.outletItemAddOnId
              ] === undefined
            ) {
              // const outletItemAddOnSnapshot = await firestore()
              //   .collection(Collections.OutletItemAddOn)
              //   .where(
              //     'uniqueId',
              //     '==',
              //     tempCartItemAddOnChoice.outletItemAddOnId,
              //   )
              //   .limit(1)
              //   .get();

              // if (!outletItemAddOnSnapshot.empty) {
              //   tempCartOutletItemAddOnDict[
              //     tempCartItemAddOnChoice.outletItemAddOnId
              //   ] = outletItemAddOnSnapshot.docs[0].data();
              // }

              if (allOutletsItemAddOnIdDict[tempCartItemAddOnChoice.outletItemAddOnId]) {
                tempCartOutletItemAddOnDict[tempCartItemAddOnChoice.outletItemAddOnId] =
                  allOutletsItemAddOnIdDict[tempCartItemAddOnChoice.outletItemAddOnId];
              }
            }

            if (
              tempCartItemAddOnCategorized[
              tempCartItemAddOnChoice.outletItemAddOnId
              ] === undefined
            ) {
              tempCartItemAddOnCategorized[
                tempCartItemAddOnChoice.outletItemAddOnId
              ] = [];
            }

            tempCartItemAddOnCategorized[
              tempCartItemAddOnChoice.outletItemAddOnId
            ].push(tempCartItemAddOnChoice.name);

            if (
              tempCartItemAddOnCategorizedPrice[
              tempCartItemAddOnChoice.outletItemAddOnId
              ] === undefined
            ) {
              tempCartItemAddOnCategorizedPrice[
                tempCartItemAddOnChoice.outletItemAddOnId
              ] = [];
            }

            tempCartItemAddOnCategorizedPrice[
              tempCartItemAddOnChoice.outletItemAddOnId
            ].push(tempCartItemAddOnChoice.price);
          }
        }

        const tempCartItemAddOnCategorizedList = Object.entries(
          tempCartItemAddOnCategorized,
        ).map(([key, value]) => ({ key, value }));
        const tempCartItemAddOnCategorizedPriceList = Object.entries(
          tempCartItemAddOnCategorizedPrice,
        ).map(([key, value]) => ({ key, value }));

        if (tempCartItemAddOnCategorizedList.length > 0) {
          for (var j = 0; j < tempCartItemAddOnCategorizedList.length; j++) {
            const tempCartItemAddOnName =
              tempCartOutletItemAddOnDict[
                tempCartItemAddOnCategorizedList[j].key
              ].name;

            const tempCartItemAddOnOrderIndex =
              selectedOutletItemAddOnOi[tempCartItemAddOnCategorizedList[j].key] !== undefined ?
                selectedOutletItemAddOnOi[tempCartItemAddOnCategorizedList[j].key] : 0;

            let pal = null;
            if (tempCartOutletItemAddOnDict[
              tempCartItemAddOnCategorizedList[j].key
            ].pal !== undefined) {
              pal = tempCartOutletItemAddOnDict[
                tempCartItemAddOnCategorizedList[j].key
              ].pal;
            }

            let skipSc = false;
            if (tempCartOutletItemAddOnDict[
              tempCartItemAddOnCategorizedList[j].key
            ].skipSc !== undefined) {
              skipSc = tempCartOutletItemAddOnDict[
                tempCartItemAddOnCategorizedList[j].key
              ].skipSc;
            }

            if (pal && !Array.isArray(pal)) {
              // means is shared variant/addon

              if (pal[tempCartItem.itemId]) {
                pal = pal[tempCartItem.itemId];
              }
              else {
                pal = null;
              }
            }

            tempCartItemAddOnParsed.push({
              name: tempCartItemAddOnName,
              // choiceNames: tempCartItemAddOnCategorizedList[j].value,
              choiceNames: Array.isArray(tempCartItemAddOnCategorizedList[j].value) ? tempCartItemAddOnCategorizedList[j].value.sort((a, b) => {
                return a.localeCompare(b);
              }) : tempCartItemAddOnCategorizedList[j].value,
              prices: tempCartItemAddOnCategorizedPriceList[j].value,

              oi: tempCartItemAddOnOrderIndex,

              pal: pal,

              ...(skipSc) && {
                skipSc: skipSc,
              },
            });
          }
        }
      }

      //////////////////////////////////////////////////////////////////////

      // for add-on group

      if (tempCartItem.addOnGroupList) {
        for (var j = 0; j < tempCartItem.addOnGroupList.length; j++) {
          // now separate all, might change in future

          const addOnGroup = tempCartItem.addOnGroupList[j];

          let pal = null;
          if (addOnGroup.pal) {
            pal = addOnGroup.pal;
          }

          let skipSc = false;
          if (addOnGroup.skipSc) {
            skipSc = addOnGroup.skipSc;
          }

          if (pal && !Array.isArray(pal)) {
            // means is shared variant/addon

            if (pal[tempCartItem.itemId]) {
              pal = pal[tempCartItem.itemId];
            }
            else {
              pal = null;
            }
          }

          let ots = null;
          if (addOnGroup.ots) {
            ots = addOnGroup.ots;
          }

          if (ots && !Array.isArray(ots)) {
            if (ots[tempCartItem.itemId]) {
              ots = ots[tempCartItem.itemId];
            }
            else {
              ots = [];
            }
          }

          tempCartItemAddOnParsed.push({
            name: addOnGroup.addOnName,
            addOnId: addOnGroup.outletItemAddOnId,
            choiceNames: [addOnGroup.choiceName],
            prices: [addOnGroup.quantity * addOnGroup.price],
            quantities: [addOnGroup.quantity],
            singlePrices: [addOnGroup.price],
            addOnChoiceIdList: [addOnGroup.outletItemAddOnChoiceId],
            minSelectList: [addOnGroup.minSelect],
            maxSelectList: [addOnGroup.maxSelect],

            oi: addOnGroup.oi !== undefined ? addOnGroup.oi : 0,

            pal: pal,

            ...(skipSc) && {
              skipSc: skipSc,
            },

            ...(ots) && {
              ots: ots,
            },

          });

          if (tempCartItem.priceVariable === undefined) {
            // tempCartItemPrice += addOnGroup.quantity * addOnGroup.price;
            // tempCartItemPriceOriginal += addOnGroup.quantity * addOnGroup.price;

            tempCartItemPrice = BigNumber(tempCartItemPrice).plus(BigNumber(addOnGroup.quantity).multipliedBy(addOnGroup.price)).toNumber();
            tempCartItemPriceOriginal = BigNumber(tempCartItemPriceOriginal).plus(BigNumber(addOnGroup.quantity).multipliedBy(addOnGroup.price)).toNumber();
          }
        }
      }

      //////////////////////////////////////////////////////////////////////

      // track single item price | 2022-04-12 | Herks

      var tempCartItemPriceSingle = tempCartItemPrice;
      var tempCartItemPriceSingleOriginal = tempCartItemPriceOriginal;

      //////////////////////////////////////////////////////////////////////

      // 2022-10-05 - Fixes for variable pricing (should use the inputed price already)

      if (tempCartItem.priceVariable === undefined) {
        // tempCartItemPrice = tempCartItemPrice * tempCartItem.quantity;
        // tempCartItemPriceOriginal =
        //   tempCartItemPriceOriginal * tempCartItem.quantity;

        tempCartItemPrice = BigNumber(tempCartItemPrice).multipliedBy(tempCartItem.quantity).toNumber();
        tempCartItemPriceOriginal =
          BigNumber(tempCartItemPriceOriginal).multipliedBy(tempCartItem.quantity).toNumber();
      }

      //////////////////////////////////////////////////////////////////////

      cartItemQuantitySummaries.push({
        item: tempCartItem,
        quantity: tempCartItem.quantity,
      });

      //////////////////////////////////////////////////////////////////////

      var amountOffCategory = undefined;
      if (
        selectedOutletItemCategoriesDict[tempCartItem.categoryId] &&
        amountOffCategoryNameDict[
        selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
        ] !== undefined
        && amountOffCategoryNameDict[
          selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
        ].usePromoCode === false
        &&
        checkIsAllowPromotionVoucherToApply(
          currOutlet.allowStackedPromotionVoucher,
          APPLY_DISCOUNT_TYPE.PROMOTION,
          amountOffCategoryNameDict[
          selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
          ],
          selectedPromoCodePromotion,
          {},
          promotionIdAppliedListTemp,
        )
      ) {
        amountOffCategory =
          amountOffCategoryNameDict[
          selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
          ];
      }

      if (amountOffItemSkuDict[tempCartItem.itemSku] !== undefined &&
        amountOffItemSkuDict[tempCartItem.itemSku].usePromoCode === false
        &&
        checkIsAllowPromotionVoucherToApply(
          currOutlet.allowStackedPromotionVoucher,
          APPLY_DISCOUNT_TYPE.PROMOTION,
          amountOffItemSkuDict[tempCartItem.itemSku],
          selectedPromoCodePromotion,
          {},
          promotionIdAppliedListTemp,
        )) {
        var promotionResult = checkApplyDiscountPerValidity(amountOffItemSkuDict[tempCartItem.itemSku], promotionIdAppliedListTemp, tempCartItem);

        if (promotionResult.validity) {
          const qualifiedCartItemsInfo = checkQualifiedItemsQuantityAndAmountForPromotion(
            currOutlet.allowStackedPromotionVoucher,
            APPLY_DISCOUNT_TYPE.PROMOTION,
            amountOffItemSkuDict[tempCartItem.itemSku],
            selectedPromoCodePromotion,
            {},
            promotionIdAppliedListTemp,

            amountOffItemSkuDict,
            selectedOutletItemCategoriesDict,

            cartItemsClone,
            false,

            outletItems,
            allOutletsItemAddOnIdDict,
            allOutletsItemAddOnChoiceIdDict,
            orderTypeMo,
            currOutlet,

            orderTypeSubMo,
          );

          const currPromo = amountOffItemSkuDict[tempCartItem.itemSku];
          const currPromoId = amountOffItemSkuDict[tempCartItem.itemSku].promotionId;

          let { rpQtyStatus, rpQty, currQty, matchedCartItems } = checkRPQtyStatus(
            currPromo,
            cartItemsClone,
            selectedOutletItemCategoriesDict,
            rpCategoryIdUsedPromoDict

          );

          let rpValid = false;
          let rpDiscountQty = 0;
          if (rpQtyStatus && currQty > 0) {
            // means is the required purchase promo

            if (
              rpQtyByPromotionIdDict[currPromoId] !== undefined
                ?
                rpQtyByPromotionIdDict[currPromoId] > 0
                :
                true
            ) {
              let maxApplicableQty = tempCartItem.quantity;

              let applyQty = (rpQty * maxApplicableQty);

              if (rpQtyByPromotionIdDict[currPromoId] !== undefined) {
                if (rpQtyByPromotionIdDict[currPromoId] - applyQty < 0) {
                  applyQty = rpQtyByPromotionIdDict[currPromoId];
                }

                rpQtyByPromotionIdDict[currPromoId] -= applyQty;
              }
              else {
                if (currQty - applyQty < 0) {
                  applyQty = currQty;
                }

                rpQtyByPromotionIdDict[currPromoId] = currQty - applyQty;
              }

              rpDiscountQty = Math.floor(applyQty / rpQty);

              //////////////////////////

              let consumedQty = 0;
              let initialQty = 0;
              for (let rpIndex = 0; rpIndex < matchedCartItems.length; rpIndex++) {
                const rpKey = `${matchedCartItems[rpIndex].itemId}-${matchedCartItems[rpIndex].cartItemDate.toFixed(0)}`;

                if (rpCategoryIdUsedPromoDict[rpKey]) {
                  if (typeof rpCategoryIdUsedPromoDict[rpKey] === 'boolean') {
                    consumedQty += matchedCartItems[rpIndex].quantity;
                  }
                  else if (typeof rpCategoryIdUsedPromoDict[rpKey] === 'number') {
                    consumedQty += rpCategoryIdUsedPromoDict[rpKey];
                  }
                }

                initialQty += matchedCartItems[rpIndex].quantity;
              }

              let remainingQty = initialQty - consumedQty;

              console.log(`remainingQty: ${remainingQty}`);
              console.log(`initialQty: ${initialQty}`);
              console.log(`consumedQty: ${consumedQty}`);
              console.log(`rpDiscountQty: ${rpDiscountQty}`);

              if (rpDiscountQty > remainingQty) {
                rpDiscountQty = remainingQty;
              }

              //////////////////////////

              if (rpDiscountQty > 0) {
                rpValid = true;
              }
              else {
                rpQtyStatus = false;
                rpValid = false;
              }
            }
            else {
              rpQtyStatus = false;
              rpValid = false;
            }
          }

          console.log('==test start==');
          console.log(tempCartItem);
          console.log(rpQtyStatus);
          console.log(rpQty);
          console.log(currQty);
          console.log(rpValid);
          console.log(rpDiscountQty);
          console.log('==test end==');

          if (rpQtyStatus && rpValid && rpDiscountQty > 0) {
            // can track this product is already been 'consumed', and not counted into the other required purchased promotion

            let rpDiscountQtyTrack = rpDiscountQty;

            for (let rpIndex = 0; rpIndex < matchedCartItems.length; rpIndex++) {
              const rpKey = `${matchedCartItems[rpIndex].itemId}-${matchedCartItems[rpIndex].cartItemDate.toFixed(0)}`;

              if (rpCategoryIdUsedPromoDict[rpKey] === true) {
                // existed already, can skip
                continue;
              }
              else {
                // rpCategoryIdUsedPromoDict[rpKey] = true;

                let qtyToDeduct = 0;
                if (matchedCartItems[rpIndex].quantity >= rpDiscountQtyTrack) {
                  qtyToDeduct = rpDiscountQtyTrack;
                }
                else {
                  qtyToDeduct = matchedCartItems[rpIndex].quantity;
                }

                rpDiscountQtyTrack -= qtyToDeduct;

                if (rpCategoryIdUsedPromoDict[rpKey] === undefined) {
                  rpCategoryIdUsedPromoDict[rpKey] = qtyToDeduct;
                }
                else {
                  rpCategoryIdUsedPromoDict[rpKey] += qtyToDeduct;
                }

                if (rpCategoryIdUsedPromoDict[rpKey] >= matchedCartItems[rpIndex].quantity) {
                  rpCategoryIdUsedPromoDict[rpKey] = true;
                }

                if (rpDiscountQtyTrack <= 0) {
                  break;
                }
              }
            }
          }

          if (
            rpQtyStatus &&
            qualifiedCartItemsInfo.quantity >=
            amountOffItemSkuDict[tempCartItem.itemSku].quantityMin
            // &&
            // tempCartItem.quantity <=
            // amountOffItemSkuDict[tempCartItem.itemSku].quantityMax
          ) {
            if (
              // qualifiedCartItemsInfo.price >=
              tempTotalPriceSp >=
              amountOffItemSkuDict[tempCartItem.itemSku].priceMin
            ) {
              // tempCartItemPrice -=
              //   (amountOffItemSkuDict[tempCartItem.itemSku].amountOff * promotionResult.discountQuantity);
              tempCartItemPrice =
                BigNumber(tempCartItemPrice).minus(BigNumber(amountOffItemSkuDict[tempCartItem.itemSku].amountOff).multipliedBy(((rpValid && (promotionResult.discountQuantity === tempCartItem.quantity))
                  ? rpDiscountQty : promotionResult.discountQuantity))).toNumber();
              tempCartItemPrice = Math.max(tempCartItemPrice, amountOffItemSkuDict[tempCartItem.itemSku].minPriceToDiscounted);
              promotionIdAppliedListTemp.push(amountOffItemSkuDict[tempCartItem.itemSku].promotionId);

              promotionIdTemp = amountOffItemSkuDict[tempCartItem.itemSku].promotionId;

              cartItemPromotionIdListTemp.push(promotionIdTemp);

            }
          }
        }
      } else if (amountOffCategory !== undefined
        // &&
        // (amountOffCategory.applyBefore === APPLY_BEFORE.ORDER_PLACED || amountOffCategory.applyBefore === undefined)
      ) {
        var promotionResult = checkApplyDiscountPerValidity(amountOffCategory, promotionIdAppliedListTemp, tempCartItem);

        if (promotionResult.validity) {
          const qualifiedCartItemsInfo = checkQualifiedItemsQuantityAndAmountForPromotion(
            currOutlet.allowStackedPromotionVoucher,
            APPLY_DISCOUNT_TYPE.PROMOTION,
            amountOffCategoryNameDict[
            selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
            ],
            selectedPromoCodePromotion,
            {},
            promotionIdAppliedListTemp,

            amountOffCategoryNameDict,
            selectedOutletItemCategoriesDict,

            cartItemsClone,
            false,

            outletItems,
            allOutletsItemAddOnIdDict,
            allOutletsItemAddOnChoiceIdDict,
            orderTypeMo,
            currOutlet,

            orderTypeSubMo,
          );

          const currPromo = amountOffCategoryNameDict[
            selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
          ];
          const currPromoId = amountOffCategoryNameDict[
            selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
          ].promotionId;

          let { rpQtyStatus, rpQty, currQty, matchedCartItems } = checkRPQtyStatus(
            currPromo,
            cartItemsClone,
            selectedOutletItemCategoriesDict,
            rpCategoryIdUsedPromoDict

          );

          let rpValid = false;
          let rpDiscountQty = 0;
          if (rpQtyStatus && currQty > 0) {
            // means is the required purchase promo

            if (
              rpQtyByPromotionIdDict[currPromoId] !== undefined
                ?
                rpQtyByPromotionIdDict[currPromoId] > 0
                :
                true
            ) {
              let maxApplicableQty = tempCartItem.quantity;

              let applyQty = (rpQty * maxApplicableQty);

              if (rpQtyByPromotionIdDict[currPromoId] !== undefined) {
                if (rpQtyByPromotionIdDict[currPromoId] - applyQty < 0) {
                  applyQty = rpQtyByPromotionIdDict[currPromoId];
                }

                rpQtyByPromotionIdDict[currPromoId] -= applyQty;
              }
              else {
                if (currQty - applyQty < 0) {
                  applyQty = currQty;
                }

                rpQtyByPromotionIdDict[currPromoId] = currQty - applyQty;
              }

              rpDiscountQty = Math.floor(applyQty / rpQty);

              //////////////////////////

              let consumedQty = 0;
              let initialQty = 0;
              for (let rpIndex = 0; rpIndex < matchedCartItems.length; rpIndex++) {
                const rpKey = `${matchedCartItems[rpIndex].itemId}-${matchedCartItems[rpIndex].cartItemDate.toFixed(0)}`;

                if (rpCategoryIdUsedPromoDict[rpKey]) {
                  if (typeof rpCategoryIdUsedPromoDict[rpKey] === 'boolean') {
                    consumedQty += matchedCartItems[rpIndex].quantity;
                  }
                  else if (typeof rpCategoryIdUsedPromoDict[rpKey] === 'number') {
                    consumedQty += rpCategoryIdUsedPromoDict[rpKey];
                  }
                }

                initialQty += matchedCartItems[rpIndex].quantity;
              }

              let remainingQty = initialQty - consumedQty;

              console.log(`remainingQty: ${remainingQty}`);
              console.log(`initialQty: ${initialQty}`);
              console.log(`consumedQty: ${consumedQty}`);
              console.log(`rpDiscountQty: ${rpDiscountQty}`);

              if (rpDiscountQty > remainingQty) {
                rpDiscountQty = remainingQty;
              }

              //////////////////////////

              if (rpDiscountQty > 0) {
                rpValid = true;
              }
              else {
                rpQtyStatus = false;
                rpValid = false;
              }
            }
            else {
              rpQtyStatus = false;
              rpValid = false;
            }
          }

          console.log('==test start==');
          console.log(tempCartItem);
          console.log(rpQtyStatus);
          console.log(rpQty);
          console.log(currQty);
          console.log(rpValid);
          console.log(rpDiscountQty);
          console.log('==test end==');

          if (rpQtyStatus && rpValid && rpDiscountQty > 0) {
            // can track this product is already been 'consumed', and not counted into the other required purchased promotion

            let rpDiscountQtyTrack = rpDiscountQty;

            for (let rpIndex = 0; rpIndex < matchedCartItems.length; rpIndex++) {
              const rpKey = `${matchedCartItems[rpIndex].itemId}-${matchedCartItems[rpIndex].cartItemDate.toFixed(0)}`;

              if (rpCategoryIdUsedPromoDict[rpKey] === true) {
                // existed already, can skip
                continue;
              }
              else {
                // rpCategoryIdUsedPromoDict[rpKey] = true;

                let qtyToDeduct = 0;
                if (matchedCartItems[rpIndex].quantity >= rpDiscountQtyTrack) {
                  qtyToDeduct = rpDiscountQtyTrack;
                }
                else {
                  qtyToDeduct = matchedCartItems[rpIndex].quantity;
                }

                rpDiscountQtyTrack -= qtyToDeduct;

                if (rpCategoryIdUsedPromoDict[rpKey] === undefined) {
                  rpCategoryIdUsedPromoDict[rpKey] = qtyToDeduct;
                }
                else {
                  rpCategoryIdUsedPromoDict[rpKey] += qtyToDeduct;
                }

                if (rpCategoryIdUsedPromoDict[rpKey] >= matchedCartItems[rpIndex].quantity) {
                  rpCategoryIdUsedPromoDict[rpKey] = true;
                }

                if (rpDiscountQtyTrack <= 0) {
                  break;
                }
              }
            }
          }

          if (
            rpQtyStatus &&
            qualifiedCartItemsInfo.quantity >= amountOffCategory.quantityMin
            // &&
            // tempCartItem.quantity <= amountOffCategory.quantityMax
          ) {
            if (
              // qualifiedCartItemsInfo.price >= 
              tempTotalPriceSp >=
              amountOffCategory.priceMin
            ) {
              // tempCartItemPrice -= (amountOffCategory.amountOff * promotionResult.discountQuantity);
              tempCartItemPrice =
                BigNumber(tempCartItemPrice).minus(BigNumber(amountOffCategory.amountOff).multipliedBy(((rpValid && (promotionResult.discountQuantity === tempCartItem.quantity))
                  ? rpDiscountQty : promotionResult.discountQuantity))).toNumber();
              tempCartItemPrice = Math.max(tempCartItemPrice, amountOffCategory.minPriceToDiscounted);
              promotionIdAppliedListTemp.push(amountOffCategory.promotionId);

              promotionIdTemp = amountOffCategory.promotionId;

              cartItemPromotionIdListTemp.push(promotionIdTemp);
            }
          }
        }
      }

      var percentageOffCategory = undefined;
      if (
        selectedOutletItemCategoriesDict[tempCartItem.categoryId] &&
        percentageOffCategoryNameDict[
        selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
        ] !== undefined
        && percentageOffCategoryNameDict[
          selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
        ].usePromoCode === false
        &&
        checkIsAllowPromotionVoucherToApply(
          currOutlet.allowStackedPromotionVoucher,
          APPLY_DISCOUNT_TYPE.PROMOTION,
          percentageOffCategoryNameDict[
          selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
          ],
          selectedPromoCodePromotion,
          {},
          promotionIdAppliedListTemp,
        )
      ) {
        percentageOffCategory =
          percentageOffCategoryNameDict[
          selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
          ];
      }

      if (percentageOffItemSkuDict[tempCartItem.itemSku] !== undefined
        && percentageOffItemSkuDict[tempCartItem.itemSku].usePromoCode === false
        &&
        checkIsAllowPromotionVoucherToApply(
          currOutlet.allowStackedPromotionVoucher,
          APPLY_DISCOUNT_TYPE.PROMOTION,
          percentageOffItemSkuDict[tempCartItem.itemSku],
          selectedPromoCodePromotion,
          {},
          promotionIdAppliedListTemp,
        )
        // &&
        // (percentageOffItemSkuDict[tempCartItem.itemSku].applyBefore === APPLY_BEFORE.ORDER_PLACED || percentageOffItemSkuDict[tempCartItem.itemSku].applyBefore === undefined)
      ) {
        var promotionResult = checkApplyDiscountPerValidity(percentageOffItemSkuDict[tempCartItem.itemSku], promotionIdAppliedListTemp, tempCartItem);

        if (promotionResult.validity) {
          const qualifiedCartItemsInfo = checkQualifiedItemsQuantityAndAmountForPromotion(
            currOutlet.allowStackedPromotionVoucher,
            APPLY_DISCOUNT_TYPE.PROMOTION,
            percentageOffItemSkuDict[tempCartItem.itemSku],
            selectedPromoCodePromotion,
            {},
            promotionIdAppliedListTemp,

            percentageOffItemSkuDict,
            selectedOutletItemCategoriesDict,

            cartItemsClone,
            false,

            outletItems,
            allOutletsItemAddOnIdDict,
            allOutletsItemAddOnChoiceIdDict,
            orderTypeMo,
            currOutlet,

            orderTypeSubMo,
          );

          const currPromo = percentageOffItemSkuDict[tempCartItem.itemSku];
          const currPromoId = percentageOffItemSkuDict[tempCartItem.itemSku].promotionId;

          let { rpQtyStatus, rpQty, currQty, matchedCartItems } = checkRPQtyStatus(
            currPromo,
            cartItemsClone,
            selectedOutletItemCategoriesDict,
            rpCategoryIdUsedPromoDict

          );

          let rpValid = false;
          let rpDiscountQty = 0;
          if (rpQtyStatus && currQty > 0) {
            // means is the required purchase promo

            if (
              rpQtyByPromotionIdDict[currPromoId] !== undefined
                ?
                rpQtyByPromotionIdDict[currPromoId] > 0
                :
                true
            ) {
              let maxApplicableQty = tempCartItem.quantity;

              let applyQty = (rpQty * maxApplicableQty);

              if (rpQtyByPromotionIdDict[currPromoId] !== undefined) {
                if (rpQtyByPromotionIdDict[currPromoId] - applyQty < 0) {
                  applyQty = rpQtyByPromotionIdDict[currPromoId];
                }

                rpQtyByPromotionIdDict[currPromoId] -= applyQty;
              }
              else {
                if (currQty - applyQty < 0) {
                  applyQty = currQty;
                }

                rpQtyByPromotionIdDict[currPromoId] = currQty - applyQty;
              }

              rpDiscountQty = Math.floor(applyQty / rpQty);

              //////////////////////////

              let consumedQty = 0;
              let initialQty = 0;
              for (let rpIndex = 0; rpIndex < matchedCartItems.length; rpIndex++) {
                const rpKey = `${matchedCartItems[rpIndex].itemId}-${matchedCartItems[rpIndex].cartItemDate.toFixed(0)}`;

                if (rpCategoryIdUsedPromoDict[rpKey]) {
                  if (typeof rpCategoryIdUsedPromoDict[rpKey] === 'boolean') {
                    consumedQty += matchedCartItems[rpIndex].quantity;
                  }
                  else if (typeof rpCategoryIdUsedPromoDict[rpKey] === 'number') {
                    consumedQty += rpCategoryIdUsedPromoDict[rpKey];
                  }
                }

                initialQty += matchedCartItems[rpIndex].quantity;
              }

              let remainingQty = initialQty - consumedQty;

              console.log(`remainingQty: ${remainingQty}`);
              console.log(`initialQty: ${initialQty}`);
              console.log(`consumedQty: ${consumedQty}`);
              console.log(`rpDiscountQty: ${rpDiscountQty}`);

              if (rpDiscountQty > remainingQty) {
                rpDiscountQty = remainingQty;
              }

              //////////////////////////

              if (rpDiscountQty > 0) {
                rpValid = true;
              }
              else {
                rpQtyStatus = false;
                rpValid = false;
              }
            }
            else {
              rpQtyStatus = false;
              rpValid = false;
            }
          }

          console.log('==test start==');
          console.log(tempCartItem);
          console.log(rpQtyStatus);
          console.log(rpQty);
          console.log(currQty);
          console.log(rpValid);
          console.log(rpDiscountQty);
          console.log('==test end==');

          if (rpQtyStatus && rpValid && rpDiscountQty > 0) {
            // can track this product is already been 'consumed', and not counted into the other required purchased promotion

            let rpDiscountQtyTrack = rpDiscountQty;

            for (let rpIndex = 0; rpIndex < matchedCartItems.length; rpIndex++) {
              const rpKey = `${matchedCartItems[rpIndex].itemId}-${matchedCartItems[rpIndex].cartItemDate.toFixed(0)}`;

              if (rpCategoryIdUsedPromoDict[rpKey] === true) {
                // existed already, can skip
                continue;
              }
              else {
                // rpCategoryIdUsedPromoDict[rpKey] = true;

                let qtyToDeduct = 0;
                if (matchedCartItems[rpIndex].quantity >= rpDiscountQtyTrack) {
                  qtyToDeduct = rpDiscountQtyTrack;
                }
                else {
                  qtyToDeduct = matchedCartItems[rpIndex].quantity;
                }

                rpDiscountQtyTrack -= qtyToDeduct;

                if (rpCategoryIdUsedPromoDict[rpKey] === undefined) {
                  rpCategoryIdUsedPromoDict[rpKey] = qtyToDeduct;
                }
                else {
                  rpCategoryIdUsedPromoDict[rpKey] += qtyToDeduct;
                }

                if (rpCategoryIdUsedPromoDict[rpKey] >= matchedCartItems[rpIndex].quantity) {
                  rpCategoryIdUsedPromoDict[rpKey] = true;
                }

                if (rpDiscountQtyTrack <= 0) {
                  break;
                }
              }
            }
          }

          if (
            rpQtyStatus &&
            qualifiedCartItemsInfo.quantity >=
            percentageOffItemSkuDict[tempCartItem.itemSku].quantityMin
            // &&
            // tempCartItem.quantity <=
            // percentageOffItemSkuDict[tempCartItem.itemSku].quantityMax
          ) {
            if (
              // qualifiedCartItemsInfo.price >=
              tempTotalPriceSp >=
              percentageOffItemSkuDict[tempCartItem.itemSku].priceMin
            ) {
              // var individualItemPrice = tempCartItemPrice / tempCartItem.quantity;
              var individualItemPrice = BigNumber(tempCartItemPrice).dividedBy(tempCartItem.quantity).toNumber();

              // tempCartItemPrice -=
              //   ((individualItemPrice *
              //     percentageOffItemSkuDict[tempCartItem.itemSku].percentageOff) /
              //     100) * promotionResult.discountQuantity;
              tempCartItemPrice =
                BigNumber(tempCartItemPrice).minus(
                  BigNumber(
                    individualItemPrice
                  ).multipliedBy(
                    BigNumber(
                      percentageOffItemSkuDict[tempCartItem.itemSku].percentageOff
                    ).dividedBy(100)
                  )
                    .multipliedBy(
                      ((rpValid && (promotionResult.discountQuantity === tempCartItem.quantity))
                        ? rpDiscountQty : promotionResult.discountQuantity)
                    )
                ).toNumber();
              tempCartItemPrice = Math.max(tempCartItemPrice, percentageOffItemSkuDict[tempCartItem.itemSku].minPriceToDiscounted);
              promotionIdAppliedListTemp.push(percentageOffItemSkuDict[tempCartItem.itemSku].promotionId);

              promotionIdTemp = percentageOffItemSkuDict[tempCartItem.itemSku].promotionId;

              cartItemPromotionIdListTemp.push(promotionIdTemp);

            }
          }
        }
      } else if (percentageOffCategory !== undefined
        // &&
        // (percentageOffCategory.applyBefore === APPLY_BEFORE.ORDER_PLACED || percentageOffCategory.applyBefore === undefined)
      ) {
        var promotionResult = checkApplyDiscountPerValidity(percentageOffCategory, promotionIdAppliedListTemp, tempCartItem);

        if (promotionResult.validity) {
          const qualifiedCartItemsInfo = checkQualifiedItemsQuantityAndAmountForPromotion(
            currOutlet.allowStackedPromotionVoucher,
            APPLY_DISCOUNT_TYPE.PROMOTION,
            percentageOffCategoryNameDict[
            selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
            ],
            selectedPromoCodePromotion,
            {},
            promotionIdAppliedListTemp,

            percentageOffCategoryNameDict,
            selectedOutletItemCategoriesDict,

            cartItemsClone,
            false,

            outletItems,
            allOutletsItemAddOnIdDict,
            allOutletsItemAddOnChoiceIdDict,
            orderTypeMo,
            currOutlet,

            orderTypeSubMo,
          );

          const currPromo = percentageOffCategoryNameDict[
            selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
          ];
          const currPromoId = percentageOffCategoryNameDict[
            selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
          ].promotionId;

          let { rpQtyStatus, rpQty, currQty, matchedCartItems } = checkRPQtyStatus(
            currPromo,
            cartItemsClone,
            selectedOutletItemCategoriesDict,
            rpCategoryIdUsedPromoDict

          );

          let rpValid = false;
          let rpDiscountQty = 0;
          if (rpQtyStatus && currQty > 0) {
            // means is the required purchase promo

            if (
              rpQtyByPromotionIdDict[currPromoId] !== undefined
                ?
                rpQtyByPromotionIdDict[currPromoId] > 0
                :
                true
            ) {
              let maxApplicableQty = tempCartItem.quantity;

              let applyQty = (rpQty * maxApplicableQty);

              if (rpQtyByPromotionIdDict[currPromoId] !== undefined) {
                if (rpQtyByPromotionIdDict[currPromoId] - applyQty < 0) {
                  applyQty = rpQtyByPromotionIdDict[currPromoId];
                }

                rpQtyByPromotionIdDict[currPromoId] -= applyQty;
              }
              else {
                if (currQty - applyQty < 0) {
                  applyQty = currQty;
                }

                rpQtyByPromotionIdDict[currPromoId] = currQty - applyQty;
              }

              rpDiscountQty = Math.floor(applyQty / rpQty);

              //////////////////////////

              let consumedQty = 0;
              let initialQty = 0;
              for (let rpIndex = 0; rpIndex < matchedCartItems.length; rpIndex++) {
                const rpKey = `${matchedCartItems[rpIndex].itemId}-${matchedCartItems[rpIndex].cartItemDate.toFixed(0)}`;

                if (rpCategoryIdUsedPromoDict[rpKey]) {
                  if (typeof rpCategoryIdUsedPromoDict[rpKey] === 'boolean') {
                    consumedQty += matchedCartItems[rpIndex].quantity;
                  }
                  else if (typeof rpCategoryIdUsedPromoDict[rpKey] === 'number') {
                    consumedQty += rpCategoryIdUsedPromoDict[rpKey];
                  }
                }

                initialQty += matchedCartItems[rpIndex].quantity;
              }

              let remainingQty = initialQty - consumedQty;

              console.log(`remainingQty: ${remainingQty}`);
              console.log(`initialQty: ${initialQty}`);
              console.log(`consumedQty: ${consumedQty}`);
              console.log(`rpDiscountQty: ${rpDiscountQty}`);

              if (rpDiscountQty > remainingQty) {
                rpDiscountQty = remainingQty;
              }

              //////////////////////////

              if (rpDiscountQty > 0) {
                rpValid = true;
              }
              else {
                rpQtyStatus = false;
                rpValid = false;
              }
            }
            else {
              rpQtyStatus = false;
              rpValid = false;
            }
          }

          console.log('==test start==');
          console.log(tempCartItem);
          console.log(rpQtyStatus);
          console.log(rpQty);
          console.log(currQty);
          console.log(rpValid);
          console.log(rpDiscountQty);
          console.log('==test end==');

          if (rpQtyStatus && rpValid && rpDiscountQty > 0) {
            // can track this product is already been 'consumed', and not counted into the other required purchased promotion

            let rpDiscountQtyTrack = rpDiscountQty;

            for (let rpIndex = 0; rpIndex < matchedCartItems.length; rpIndex++) {
              const rpKey = `${matchedCartItems[rpIndex].itemId}-${matchedCartItems[rpIndex].cartItemDate.toFixed(0)}`;

              if (rpCategoryIdUsedPromoDict[rpKey] === true) {
                // existed already, can skip
                continue;
              }
              else {
                // rpCategoryIdUsedPromoDict[rpKey] = true;

                let qtyToDeduct = 0;
                if (matchedCartItems[rpIndex].quantity >= rpDiscountQtyTrack) {
                  qtyToDeduct = rpDiscountQtyTrack;
                }
                else {
                  qtyToDeduct = matchedCartItems[rpIndex].quantity;
                }

                rpDiscountQtyTrack -= qtyToDeduct;

                if (rpCategoryIdUsedPromoDict[rpKey] === undefined) {
                  rpCategoryIdUsedPromoDict[rpKey] = qtyToDeduct;
                }
                else {
                  rpCategoryIdUsedPromoDict[rpKey] += qtyToDeduct;
                }

                if (rpCategoryIdUsedPromoDict[rpKey] >= matchedCartItems[rpIndex].quantity) {
                  rpCategoryIdUsedPromoDict[rpKey] = true;
                }

                if (rpDiscountQtyTrack <= 0) {
                  break;
                }
              }
            }
          }

          if (
            rpQtyStatus &&
            qualifiedCartItemsInfo.quantity >= percentageOffCategory.quantityMin
            // &&
            // tempCartItem.quantity <= percentageOffCategory.quantityMax
          ) {
            if (
              // qualifiedCartItemsInfo.price >= 
              tempTotalPriceSp >=
              percentageOffCategory.priceMin
            ) {
              // var individualItemPrice = tempCartItemPrice / tempCartItem.quantity;
              var individualItemPrice = BigNumber(tempCartItemPrice).dividedBy(tempCartItem.quantity).toNumber();

              // tempCartItemPrice -=
              //   ((individualItemPrice * percentageOffCategory.percentageOff) / 100) * promotionResult.discountQuantity;
              tempCartItemPrice =
                BigNumber(tempCartItemPrice).minus(
                  BigNumber(
                    individualItemPrice
                  ).multipliedBy(
                    BigNumber(
                      percentageOffCategory.percentageOff
                    ).dividedBy(100)
                  )
                    .multipliedBy(
                      ((rpValid && (promotionResult.discountQuantity === tempCartItem.quantity))
                        ? rpDiscountQty : promotionResult.discountQuantity)
                    )
                ).toNumber();
              tempCartItemPrice = Math.max(tempCartItemPrice, percentageOffCategory.minPriceToDiscounted);
              promotionIdAppliedListTemp.push(percentageOffCategory.promotionId);

              promotionIdTemp = percentageOffCategory.promotionId;

              cartItemPromotionIdListTemp.push(promotionIdTemp);
            }
          }
        }
      }

      //////////////////////////////////////////////////////////////////////

      // calculate takeaway promotions

      if (orderTypeMo === ORDER_TYPE.PICKUP) {
        var takeawayOffCategory = undefined;
        if (
          selectedOutletItemCategoriesDict[tempCartItem.categoryId] &&
          takeawayCategoryNameDict[
          selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
          ] !== undefined
          && takeawayCategoryNameDict[
            selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
          ].usePromoCode === false
          &&
          checkIsAllowPromotionVoucherToApply(
            currOutlet.allowStackedPromotionVoucher,
            APPLY_DISCOUNT_TYPE.PROMOTION,
            takeawayCategoryNameDict[
            selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
            ],
            selectedPromoCodePromotion,
            {},
            promotionIdAppliedListTemp,
          )
        ) {
          takeawayOffCategory =
            takeawayCategoryNameDict[
            selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
            ];
        }

        if (takeawayItemSkuDict[tempCartItem.itemSku] !== undefined &&
          takeawayItemSkuDict[tempCartItem.itemSku].usePromoCode === false
          &&
          checkIsAllowPromotionVoucherToApply(
            currOutlet.allowStackedPromotionVoucher,
            APPLY_DISCOUNT_TYPE.PROMOTION,
            takeawayItemSkuDict[tempCartItem.itemSku],
            selectedPromoCodePromotion,
            {},
            promotionIdAppliedListTemp,
          )
          // &&
          // (takeawayItemSkuDict[tempCartItem.itemSku].applyBefore === APPLY_BEFORE.ORDER_PLACED || takeawayItemSkuDict[tempCartItem.itemSku].applyBefore === undefined)
        ) {
          var promotionResult = checkApplyDiscountPerValidity(takeawayItemSkuDict[tempCartItem.itemSku], promotionIdAppliedListTemp, tempCartItem);

          if (promotionResult.validity) {
            if (
              tempCartItemPrice >=
              takeawayItemSkuDict[tempCartItem.itemSku].takeawayDiscountAboveAmount
            ) {
              // takeawayDiscountAmount +=
              //   takeawayItemSkuDict[tempCartItem.itemSku].takeawayDiscountAmount;

              // tempCartItemPrice -=
              //   takeawayItemSkuDict[tempCartItem.itemSku].takeawayDiscountAmount * promotionResult.discountQuantity;

              takeawayDiscountAmount =
                BigNumber(takeawayDiscountAmount).plus(takeawayItemSkuDict[tempCartItem.itemSku].takeawayDiscountAmount).toNumber();

              tempCartItemPrice =
                BigNumber(tempCartItemPrice).minus(
                  BigNumber(takeawayItemSkuDict[tempCartItem.itemSku].takeawayDiscountAmount).multipliedBy(promotionResult.discountQuantity)
                ).toNumber();

              tempCartItemPrice = Math.max(tempCartItemPrice, 0);

              promotionIdAppliedListTemp.push(takeawayItemSkuDict[tempCartItem.itemSku].promotionId);

              promotionIdTemp = takeawayItemSkuDict[tempCartItem.itemSku].promotionId;

              cartItemPromotionIdListTemp.push(promotionIdTemp);
            }
          }
        } else if (takeawayOffCategory !== undefined
          // &&
          // (takeawayOffCategory.applyBefore === APPLY_BEFORE.ORDER_PLACED || takeawayOffCategory.applyBefore === undefined)
        ) {
          var promotionResult = checkApplyDiscountPerValidity(takeawayOffCategory, promotionIdAppliedListTemp, tempCartItem);

          if (promotionResult.validity) {
            if (
              tempCartItemPrice >= takeawayOffCategory.takeawayDiscountAboveAmount
            ) {
              // takeawayDiscountAmount += takeawayOffCategory.takeawayDiscountAmount;

              // tempCartItemPrice -=
              //   takeawayOffCategory.takeawayDiscountAmount * promotionResult.discountQuantity;

              takeawayDiscountAmount =
                BigNumber(takeawayDiscountAmount).plus(takeawayOffCategory.takeawayDiscountAmount).toNumber();

              tempCartItemPrice =
                BigNumber(tempCartItemPrice).minus(
                  BigNumber(takeawayOffCategory.takeawayDiscountAmount).multipliedBy(promotionResult.discountQuantity)
                ).toNumber();

              tempCartItemPrice = Math.max(tempCartItemPrice, 0);

              promotionIdAppliedListTemp.push(takeawayOffCategory.promotionId);

              promotionIdTemp = takeawayOffCategory.promotionId;

              cartItemPromotionIdListTemp.push(promotionIdTemp);
            }
          }
        }
      }

      //////////////////////////////////////////////////////////////////////
      //////////////////////////////////////////////////////////////////////
      //////////////////////////////////////////////////////////////////////
      //////////////////////////////////////////////////////////////////////

      // promotions with promo code

      amountOffCategory = undefined;
      if (
        selectedOutletItemCategoriesDict[tempCartItem.categoryId] &&
        amountOffCategoryNameDict[
        selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
        ] !== undefined
        && amountOffCategoryNameDict[
          selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
        ].usePromoCode === true
        &&
        checkIsAllowPromotionVoucherToApply(
          currOutlet.allowStackedPromotionVoucher,
          APPLY_DISCOUNT_TYPE.PROMOTION_PROMO_CODE,
          amountOffCategoryNameDict[
          selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
          ],
          selectedPromoCodePromotion,
          {},
          promotionIdAppliedListTemp,
        )
      ) {
        amountOffCategory =
          amountOffCategoryNameDict[
          selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
          ];
      }

      if (amountOffItemSkuDict[tempCartItem.itemSku] !== undefined &&
        amountOffItemSkuDict[tempCartItem.itemSku].usePromoCode === true
        &&
        checkIsAllowPromotionVoucherToApply(
          currOutlet.allowStackedPromotionVoucher,
          APPLY_DISCOUNT_TYPE.PROMOTION_PROMO_CODE,
          amountOffItemSkuDict[tempCartItem.itemSku],
          selectedPromoCodePromotion,
          {},
          promotionIdAppliedListTemp,
        )) {
        var promotionResult = checkApplyDiscountPerValidity(amountOffItemSkuDict[tempCartItem.itemSku], promotionIdAppliedListTemp, tempCartItem);

        if (promotionResult.validity) {
          const qualifiedCartItemsInfo = checkQualifiedItemsQuantityAndAmountForPromotion(
            currOutlet.allowStackedPromotionVoucher,
            APPLY_DISCOUNT_TYPE.PROMOTION_PROMO_CODE,
            amountOffItemSkuDict[tempCartItem.itemSku],
            selectedPromoCodePromotion,
            {},
            promotionIdAppliedListTemp,

            amountOffItemSkuDict,
            selectedOutletItemCategoriesDict,

            cartItemsClone,
            true,

            outletItems,
            allOutletsItemAddOnIdDict,
            allOutletsItemAddOnChoiceIdDict,
            orderTypeMo,
            currOutlet,

            orderTypeSubMo,
          );

          const currPromo = amountOffItemSkuDict[tempCartItem.itemSku];
          const currPromoId = amountOffItemSkuDict[tempCartItem.itemSku].promotionId;

          let { rpQtyStatus, rpQty, currQty, matchedCartItems } = checkRPQtyStatus(
            currPromo,
            cartItemsClone,
            selectedOutletItemCategoriesDict,
            rpCategoryIdUsedPromoDict

          );

          let rpValid = false;
          let rpDiscountQty = 0;
          if (rpQtyStatus && currQty > 0) {
            // means is the required purchase promo

            if (
              rpQtyByPromotionIdDict[currPromoId] !== undefined
                ?
                rpQtyByPromotionIdDict[currPromoId] > 0
                :
                true
            ) {
              let maxApplicableQty = tempCartItem.quantity;

              let applyQty = (rpQty * maxApplicableQty);

              if (rpQtyByPromotionIdDict[currPromoId] !== undefined) {
                if (rpQtyByPromotionIdDict[currPromoId] - applyQty < 0) {
                  applyQty = rpQtyByPromotionIdDict[currPromoId];
                }

                rpQtyByPromotionIdDict[currPromoId] -= applyQty;
              }
              else {
                if (currQty - applyQty < 0) {
                  applyQty = currQty;
                }

                rpQtyByPromotionIdDict[currPromoId] = currQty - applyQty;
              }

              rpDiscountQty = Math.floor(applyQty / rpQty);

              //////////////////////////

              let consumedQty = 0;
              let initialQty = 0;
              for (let rpIndex = 0; rpIndex < matchedCartItems.length; rpIndex++) {
                const rpKey = `${matchedCartItems[rpIndex].itemId}-${matchedCartItems[rpIndex].cartItemDate.toFixed(0)}`;

                if (rpCategoryIdUsedPromoDict[rpKey]) {
                  if (typeof rpCategoryIdUsedPromoDict[rpKey] === 'boolean') {
                    consumedQty += matchedCartItems[rpIndex].quantity;
                  }
                  else if (typeof rpCategoryIdUsedPromoDict[rpKey] === 'number') {
                    consumedQty += rpCategoryIdUsedPromoDict[rpKey];
                  }
                }

                initialQty += matchedCartItems[rpIndex].quantity;
              }

              let remainingQty = initialQty - consumedQty;

              console.log(`remainingQty: ${remainingQty}`);
              console.log(`initialQty: ${initialQty}`);
              console.log(`consumedQty: ${consumedQty}`);
              console.log(`rpDiscountQty: ${rpDiscountQty}`);

              if (rpDiscountQty > remainingQty) {
                rpDiscountQty = remainingQty;
              }

              //////////////////////////

              if (rpDiscountQty > 0) {
                rpValid = true;
              }
              else {
                rpQtyStatus = false;
                rpValid = false;
              }
            }
            else {
              rpQtyStatus = false;
              rpValid = false;
            }
          }

          console.log('==test start==');
          console.log(tempCartItem);
          console.log(rpQtyStatus);
          console.log(rpQty);
          console.log(currQty);
          console.log(rpValid);
          console.log(rpDiscountQty);
          console.log('==test end==');

          if (rpQtyStatus && rpValid && rpDiscountQty > 0) {
            // can track this product is already been 'consumed', and not counted into the other required purchased promotion

            let rpDiscountQtyTrack = rpDiscountQty;

            for (let rpIndex = 0; rpIndex < matchedCartItems.length; rpIndex++) {
              const rpKey = `${matchedCartItems[rpIndex].itemId}-${matchedCartItems[rpIndex].cartItemDate.toFixed(0)}`;

              if (rpCategoryIdUsedPromoDict[rpKey] === true) {
                // existed already, can skip
                continue;
              }
              else {
                // rpCategoryIdUsedPromoDict[rpKey] = true;

                let qtyToDeduct = 0;
                if (matchedCartItems[rpIndex].quantity >= rpDiscountQtyTrack) {
                  qtyToDeduct = rpDiscountQtyTrack;
                }
                else {
                  qtyToDeduct = matchedCartItems[rpIndex].quantity;
                }

                rpDiscountQtyTrack -= qtyToDeduct;

                if (rpCategoryIdUsedPromoDict[rpKey] === undefined) {
                  rpCategoryIdUsedPromoDict[rpKey] = qtyToDeduct;
                }
                else {
                  rpCategoryIdUsedPromoDict[rpKey] += qtyToDeduct;
                }

                if (rpCategoryIdUsedPromoDict[rpKey] >= matchedCartItems[rpIndex].quantity) {
                  rpCategoryIdUsedPromoDict[rpKey] = true;
                }

                if (rpDiscountQtyTrack <= 0) {
                  break;
                }
              }
            }
          }

          if (
            rpQtyStatus &&
            qualifiedCartItemsInfo.quantity >=
            amountOffItemSkuDict[tempCartItem.itemSku].quantityMin
            // &&
            // tempCartItem.quantity <=
            // amountOffItemSkuDict[tempCartItem.itemSku].quantityMax
          ) {
            if (
              // qualifiedCartItemsInfo.price >=
              tempTotalPriceSp >=
              amountOffItemSkuDict[tempCartItem.itemSku].priceMin
            ) {
              // tempCartItemPrice -=
              //   (amountOffItemSkuDict[tempCartItem.itemSku].amountOff * promotionResult.discountQuantity);
              tempCartItemPrice =
                BigNumber(tempCartItemPrice).minus(BigNumber(amountOffItemSkuDict[tempCartItem.itemSku].amountOff).multipliedBy(((rpValid && (promotionResult.discountQuantity === tempCartItem.quantity))
                  ? rpDiscountQty : promotionResult.discountQuantity))).toNumber();
              tempCartItemPrice = Math.max(tempCartItemPrice, amountOffItemSkuDict[tempCartItem.itemSku].minPriceToDiscounted);
              promotionIdAppliedListTemp.push(amountOffItemSkuDict[tempCartItem.itemSku].promotionId);

              promotionIdTemp = amountOffItemSkuDict[tempCartItem.itemSku].promotionId;

              cartItemPromotionIdListTemp.push(promotionIdTemp);

            }
          }
        }
      } else if (amountOffCategory !== undefined
        // &&
        // (amountOffCategory.applyBefore === APPLY_BEFORE.ORDER_PLACED || amountOffCategory.applyBefore === undefined)
      ) {
        var promotionResult = checkApplyDiscountPerValidity(amountOffCategory, promotionIdAppliedListTemp, tempCartItem);

        if (promotionResult.validity) {
          const qualifiedCartItemsInfo = checkQualifiedItemsQuantityAndAmountForPromotion(
            currOutlet.allowStackedPromotionVoucher,
            APPLY_DISCOUNT_TYPE.PROMOTION_PROMO_CODE,
            amountOffCategoryNameDict[
            selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
            ],
            selectedPromoCodePromotion,
            {},
            promotionIdAppliedListTemp,

            amountOffCategoryNameDict,
            selectedOutletItemCategoriesDict,

            cartItemsClone,
            true,

            outletItems,
            allOutletsItemAddOnIdDict,
            allOutletsItemAddOnChoiceIdDict,
            orderTypeMo,
            currOutlet,

            orderTypeSubMo,
          );

          const currPromo = amountOffCategoryNameDict[
            selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
          ];
          const currPromoId = amountOffCategoryNameDict[
            selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
          ].promotionId;

          let { rpQtyStatus, rpQty, currQty, matchedCartItems } = checkRPQtyStatus(
            currPromo,
            cartItemsClone,
            selectedOutletItemCategoriesDict,
            rpCategoryIdUsedPromoDict

          );

          let rpValid = false;
          let rpDiscountQty = 0;
          if (rpQtyStatus && currQty > 0) {
            // means is the required purchase promo

            if (
              rpQtyByPromotionIdDict[currPromoId] !== undefined
                ?
                rpQtyByPromotionIdDict[currPromoId] > 0
                :
                true
            ) {
              let maxApplicableQty = tempCartItem.quantity;

              let applyQty = (rpQty * maxApplicableQty);

              if (rpQtyByPromotionIdDict[currPromoId] !== undefined) {
                if (rpQtyByPromotionIdDict[currPromoId] - applyQty < 0) {
                  applyQty = rpQtyByPromotionIdDict[currPromoId];
                }

                rpQtyByPromotionIdDict[currPromoId] -= applyQty;
              }
              else {
                if (currQty - applyQty < 0) {
                  applyQty = currQty;
                }

                rpQtyByPromotionIdDict[currPromoId] = currQty - applyQty;
              }

              rpDiscountQty = Math.floor(applyQty / rpQty);

              //////////////////////////

              let consumedQty = 0;
              let initialQty = 0;
              for (let rpIndex = 0; rpIndex < matchedCartItems.length; rpIndex++) {
                const rpKey = `${matchedCartItems[rpIndex].itemId}-${matchedCartItems[rpIndex].cartItemDate.toFixed(0)}`;

                if (rpCategoryIdUsedPromoDict[rpKey]) {
                  if (typeof rpCategoryIdUsedPromoDict[rpKey] === 'boolean') {
                    consumedQty += matchedCartItems[rpIndex].quantity;
                  }
                  else if (typeof rpCategoryIdUsedPromoDict[rpKey] === 'number') {
                    consumedQty += rpCategoryIdUsedPromoDict[rpKey];
                  }
                }

                initialQty += matchedCartItems[rpIndex].quantity;
              }

              let remainingQty = initialQty - consumedQty;

              console.log(`remainingQty: ${remainingQty}`);
              console.log(`initialQty: ${initialQty}`);
              console.log(`consumedQty: ${consumedQty}`);
              console.log(`rpDiscountQty: ${rpDiscountQty}`);

              if (rpDiscountQty > remainingQty) {
                rpDiscountQty = remainingQty;
              }

              //////////////////////////

              if (rpDiscountQty > 0) {
                rpValid = true;
              }
              else {
                rpQtyStatus = false;
                rpValid = false;
              }
            }
            else {
              rpQtyStatus = false;
              rpValid = false;
            }
          }

          console.log('==test start==');
          console.log(tempCartItem);
          console.log(rpQtyStatus);
          console.log(rpQty);
          console.log(currQty);
          console.log(rpValid);
          console.log(rpDiscountQty);
          console.log('==test end==');

          if (rpQtyStatus && rpValid && rpDiscountQty > 0) {
            // can track this product is already been 'consumed', and not counted into the other required purchased promotion

            let rpDiscountQtyTrack = rpDiscountQty;

            for (let rpIndex = 0; rpIndex < matchedCartItems.length; rpIndex++) {
              const rpKey = `${matchedCartItems[rpIndex].itemId}-${matchedCartItems[rpIndex].cartItemDate.toFixed(0)}`;

              if (rpCategoryIdUsedPromoDict[rpKey] === true) {
                // existed already, can skip
                continue;
              }
              else {
                // rpCategoryIdUsedPromoDict[rpKey] = true;

                let qtyToDeduct = 0;
                if (matchedCartItems[rpIndex].quantity >= rpDiscountQtyTrack) {
                  qtyToDeduct = rpDiscountQtyTrack;
                }
                else {
                  qtyToDeduct = matchedCartItems[rpIndex].quantity;
                }

                rpDiscountQtyTrack -= qtyToDeduct;

                if (rpCategoryIdUsedPromoDict[rpKey] === undefined) {
                  rpCategoryIdUsedPromoDict[rpKey] = qtyToDeduct;
                }
                else {
                  rpCategoryIdUsedPromoDict[rpKey] += qtyToDeduct;
                }

                if (rpCategoryIdUsedPromoDict[rpKey] >= matchedCartItems[rpIndex].quantity) {
                  rpCategoryIdUsedPromoDict[rpKey] = true;
                }

                if (rpDiscountQtyTrack <= 0) {
                  break;
                }
              }
            }
          }

          if (
            rpQtyStatus &&
            qualifiedCartItemsInfo.quantity >= amountOffCategory.quantityMin
            // &&
            // tempCartItem.quantity <= amountOffCategory.quantityMax
          ) {
            if (
              // qualifiedCartItemsInfo.price >= 
              tempTotalPriceSp >=
              amountOffCategory.priceMin
            ) {
              // tempCartItemPrice -= (amountOffCategory.amountOff * promotionResult.discountQuantity);
              tempCartItemPrice =
                BigNumber(tempCartItemPrice).minus(BigNumber(amountOffCategory.amountOff).multipliedBy(((rpValid && (promotionResult.discountQuantity === tempCartItem.quantity))
                  ? rpDiscountQty : promotionResult.discountQuantity))).toNumber();
              tempCartItemPrice = Math.max(tempCartItemPrice, amountOffCategory.minPriceToDiscounted);
              promotionIdAppliedListTemp.push(amountOffCategory.promotionId);

              promotionIdTemp = amountOffCategory.promotionId;

              cartItemPromotionIdListTemp.push(promotionIdTemp);
            }
          }
        }
      }

      percentageOffCategory = undefined;
      if (
        selectedOutletItemCategoriesDict[tempCartItem.categoryId] &&
        percentageOffCategoryNameDict[
        selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
        ] !== undefined
        && percentageOffCategoryNameDict[
          selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
        ].usePromoCode === true
        &&
        checkIsAllowPromotionVoucherToApply(
          currOutlet.allowStackedPromotionVoucher,
          APPLY_DISCOUNT_TYPE.PROMOTION_PROMO_CODE,
          percentageOffCategoryNameDict[
          selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
          ],
          selectedPromoCodePromotion,
          {},
          promotionIdAppliedListTemp,
        )
      ) {
        percentageOffCategory =
          percentageOffCategoryNameDict[
          selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
          ];
      }

      if (percentageOffItemSkuDict[tempCartItem.itemSku] !== undefined
        && percentageOffItemSkuDict[tempCartItem.itemSku].usePromoCode === true
        &&
        checkIsAllowPromotionVoucherToApply(
          currOutlet.allowStackedPromotionVoucher,
          APPLY_DISCOUNT_TYPE.PROMOTION_PROMO_CODE,
          percentageOffItemSkuDict[tempCartItem.itemSku],
          selectedPromoCodePromotion,
          {},
          promotionIdAppliedListTemp,
        )
        // &&
        // (percentageOffItemSkuDict[tempCartItem.itemSku].applyBefore === APPLY_BEFORE.ORDER_PLACED || percentageOffItemSkuDict[tempCartItem.itemSku].applyBefore === undefined)
      ) {
        var promotionResult = checkApplyDiscountPerValidity(percentageOffItemSkuDict[tempCartItem.itemSku], promotionIdAppliedListTemp, tempCartItem);

        if (promotionResult.validity) {
          const qualifiedCartItemsInfo = checkQualifiedItemsQuantityAndAmountForPromotion(
            currOutlet.allowStackedPromotionVoucher,
            APPLY_DISCOUNT_TYPE.PROMOTION_PROMO_CODE,
            percentageOffItemSkuDict[tempCartItem.itemSku],
            selectedPromoCodePromotion,
            {},
            promotionIdAppliedListTemp,

            percentageOffItemSkuDict,
            selectedOutletItemCategoriesDict,

            cartItemsClone,
            true,

            outletItems,
            allOutletsItemAddOnIdDict,
            allOutletsItemAddOnChoiceIdDict,
            orderTypeMo,
            currOutlet,

            orderTypeSubMo,
          );

          const currPromo = percentageOffItemSkuDict[tempCartItem.itemSku];
          const currPromoId = percentageOffItemSkuDict[tempCartItem.itemSku].promotionId;

          let { rpQtyStatus, rpQty, currQty, matchedCartItems } = checkRPQtyStatus(
            currPromo,
            cartItemsClone,
            selectedOutletItemCategoriesDict,
            rpCategoryIdUsedPromoDict

          );

          let rpValid = false;
          let rpDiscountQty = 0;
          if (rpQtyStatus && currQty > 0) {
            // means is the required purchase promo

            if (
              rpQtyByPromotionIdDict[currPromoId] !== undefined
                ?
                rpQtyByPromotionIdDict[currPromoId] > 0
                :
                true
            ) {
              let maxApplicableQty = tempCartItem.quantity;

              let applyQty = (rpQty * maxApplicableQty);

              if (rpQtyByPromotionIdDict[currPromoId] !== undefined) {
                if (rpQtyByPromotionIdDict[currPromoId] - applyQty < 0) {
                  applyQty = rpQtyByPromotionIdDict[currPromoId];
                }

                rpQtyByPromotionIdDict[currPromoId] -= applyQty;
              }
              else {
                if (currQty - applyQty < 0) {
                  applyQty = currQty;
                }

                rpQtyByPromotionIdDict[currPromoId] = currQty - applyQty;
              }

              rpDiscountQty = Math.floor(applyQty / rpQty);

              //////////////////////////

              let consumedQty = 0;
              let initialQty = 0;
              for (let rpIndex = 0; rpIndex < matchedCartItems.length; rpIndex++) {
                const rpKey = `${matchedCartItems[rpIndex].itemId}-${matchedCartItems[rpIndex].cartItemDate.toFixed(0)}`;

                if (rpCategoryIdUsedPromoDict[rpKey]) {
                  if (typeof rpCategoryIdUsedPromoDict[rpKey] === 'boolean') {
                    consumedQty += matchedCartItems[rpIndex].quantity;
                  }
                  else if (typeof rpCategoryIdUsedPromoDict[rpKey] === 'number') {
                    consumedQty += rpCategoryIdUsedPromoDict[rpKey];
                  }
                }

                initialQty += matchedCartItems[rpIndex].quantity;
              }

              let remainingQty = initialQty - consumedQty;

              console.log(`remainingQty: ${remainingQty}`);
              console.log(`initialQty: ${initialQty}`);
              console.log(`consumedQty: ${consumedQty}`);
              console.log(`rpDiscountQty: ${rpDiscountQty}`);

              if (rpDiscountQty > remainingQty) {
                rpDiscountQty = remainingQty;
              }

              //////////////////////////

              if (rpDiscountQty > 0) {
                rpValid = true;
              }
              else {
                rpQtyStatus = false;
                rpValid = false;
              }
            }
            else {
              rpQtyStatus = false;
              rpValid = false;
            }
          }

          console.log('==test start==');
          console.log(tempCartItem);
          console.log(rpQtyStatus);
          console.log(rpQty);
          console.log(currQty);
          console.log(rpValid);
          console.log(rpDiscountQty);
          console.log('==test end==');

          if (rpQtyStatus && rpValid && rpDiscountQty > 0) {
            // can track this product is already been 'consumed', and not counted into the other required purchased promotion

            let rpDiscountQtyTrack = rpDiscountQty;

            for (let rpIndex = 0; rpIndex < matchedCartItems.length; rpIndex++) {
              const rpKey = `${matchedCartItems[rpIndex].itemId}-${matchedCartItems[rpIndex].cartItemDate.toFixed(0)}`;

              if (rpCategoryIdUsedPromoDict[rpKey] === true) {
                // existed already, can skip
                continue;
              }
              else {
                // rpCategoryIdUsedPromoDict[rpKey] = true;

                let qtyToDeduct = 0;
                if (matchedCartItems[rpIndex].quantity >= rpDiscountQtyTrack) {
                  qtyToDeduct = rpDiscountQtyTrack;
                }
                else {
                  qtyToDeduct = matchedCartItems[rpIndex].quantity;
                }

                rpDiscountQtyTrack -= qtyToDeduct;

                if (rpCategoryIdUsedPromoDict[rpKey] === undefined) {
                  rpCategoryIdUsedPromoDict[rpKey] = qtyToDeduct;
                }
                else {
                  rpCategoryIdUsedPromoDict[rpKey] += qtyToDeduct;
                }

                if (rpCategoryIdUsedPromoDict[rpKey] >= matchedCartItems[rpIndex].quantity) {
                  rpCategoryIdUsedPromoDict[rpKey] = true;
                }

                if (rpDiscountQtyTrack <= 0) {
                  break;
                }
              }
            }
          }

          if (
            rpQtyStatus &&
            qualifiedCartItemsInfo.quantity >=
            percentageOffItemSkuDict[tempCartItem.itemSku].quantityMin
            // &&
            // tempCartItem.quantity <=
            // percentageOffItemSkuDict[tempCartItem.itemSku].quantityMax
          ) {
            if (
              // qualifiedCartItemsInfo.price >=
              tempTotalPriceSp >=
              percentageOffItemSkuDict[tempCartItem.itemSku].priceMin
            ) {
              // var individualItemPrice = tempCartItemPrice / tempCartItem.quantity;
              var individualItemPrice = BigNumber(tempCartItemPrice).dividedBy(tempCartItem.quantity).toNumber();

              // tempCartItemPrice -=
              //   (individualItemPrice *
              //     (percentageOffItemSkuDict[tempCartItem.itemSku].percentageOff /
              //       100)) * promotionResult.discountQuantity;
              tempCartItemPrice =
                BigNumber(tempCartItemPrice).minus(
                  BigNumber(
                    individualItemPrice
                  ).multipliedBy(
                    BigNumber(
                      percentageOffItemSkuDict[tempCartItem.itemSku].percentageOff
                    ).dividedBy(100)
                  )
                    .multipliedBy(
                      ((rpValid && (promotionResult.discountQuantity === tempCartItem.quantity))
                        ? rpDiscountQty : promotionResult.discountQuantity)
                    )
                ).toNumber();
              tempCartItemPrice = Math.max(tempCartItemPrice, percentageOffItemSkuDict[tempCartItem.itemSku].minPriceToDiscounted);
              promotionIdAppliedListTemp.push(percentageOffItemSkuDict[tempCartItem.itemSku].promotionId);

              promotionIdTemp = percentageOffItemSkuDict[tempCartItem.itemSku].promotionId;

              cartItemPromotionIdListTemp.push(promotionIdTemp);

            }
          }
        }
      } else if (percentageOffCategory !== undefined
        // &&
        // (percentageOffCategory.applyBefore === APPLY_BEFORE.ORDER_PLACED || percentageOffCategory.applyBefore === undefined)
      ) {
        var promotionResult = checkApplyDiscountPerValidity(percentageOffCategory, promotionIdAppliedListTemp, tempCartItem);

        if (promotionResult.validity) {
          const qualifiedCartItemsInfo = checkQualifiedItemsQuantityAndAmountForPromotion(
            currOutlet.allowStackedPromotionVoucher,
            APPLY_DISCOUNT_TYPE.PROMOTION_PROMO_CODE,
            percentageOffCategoryNameDict[
            selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
            ],
            selectedPromoCodePromotion,
            {},
            promotionIdAppliedListTemp,

            percentageOffCategoryNameDict,
            selectedOutletItemCategoriesDict,

            cartItemsClone,
            true,

            outletItems,
            allOutletsItemAddOnIdDict,
            allOutletsItemAddOnChoiceIdDict,
            orderTypeMo,
            currOutlet,

            orderTypeSubMo,
          );

          const currPromo = percentageOffCategoryNameDict[
            selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
          ];
          const currPromoId = percentageOffCategoryNameDict[
            selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
          ].promotionId;

          let { rpQtyStatus, rpQty, currQty, matchedCartItems } = checkRPQtyStatus(
            currPromo,
            cartItemsClone,
            selectedOutletItemCategoriesDict,
            rpCategoryIdUsedPromoDict

          );

          let rpValid = false;
          let rpDiscountQty = 0;
          if (rpQtyStatus && currQty > 0) {
            // means is the required purchase promo

            if (
              rpQtyByPromotionIdDict[currPromoId] !== undefined
                ?
                rpQtyByPromotionIdDict[currPromoId] > 0
                :
                true
            ) {
              let maxApplicableQty = tempCartItem.quantity;

              let applyQty = (rpQty * maxApplicableQty);

              if (rpQtyByPromotionIdDict[currPromoId] !== undefined) {
                if (rpQtyByPromotionIdDict[currPromoId] - applyQty < 0) {
                  applyQty = rpQtyByPromotionIdDict[currPromoId];
                }

                rpQtyByPromotionIdDict[currPromoId] -= applyQty;
              }
              else {
                if (currQty - applyQty < 0) {
                  applyQty = currQty;
                }

                rpQtyByPromotionIdDict[currPromoId] = currQty - applyQty;
              }

              rpDiscountQty = Math.floor(applyQty / rpQty);

              //////////////////////////

              let consumedQty = 0;
              let initialQty = 0;
              for (let rpIndex = 0; rpIndex < matchedCartItems.length; rpIndex++) {
                const rpKey = `${matchedCartItems[rpIndex].itemId}-${matchedCartItems[rpIndex].cartItemDate.toFixed(0)}`;

                if (rpCategoryIdUsedPromoDict[rpKey]) {
                  if (typeof rpCategoryIdUsedPromoDict[rpKey] === 'boolean') {
                    consumedQty += matchedCartItems[rpIndex].quantity;
                  }
                  else if (typeof rpCategoryIdUsedPromoDict[rpKey] === 'number') {
                    consumedQty += rpCategoryIdUsedPromoDict[rpKey];
                  }
                }

                initialQty += matchedCartItems[rpIndex].quantity;
              }

              let remainingQty = initialQty - consumedQty;

              console.log(`remainingQty: ${remainingQty}`);
              console.log(`initialQty: ${initialQty}`);
              console.log(`consumedQty: ${consumedQty}`);
              console.log(`rpDiscountQty: ${rpDiscountQty}`);

              if (rpDiscountQty > remainingQty) {
                rpDiscountQty = remainingQty;
              }

              //////////////////////////

              if (rpDiscountQty > 0) {
                rpValid = true;
              }
              else {
                rpQtyStatus = false;
                rpValid = false;
              }
            }
            else {
              rpQtyStatus = false;
              rpValid = false;
            }
          }

          console.log('==test start==');
          console.log(tempCartItem);
          console.log(rpQtyStatus);
          console.log(rpQty);
          console.log(currQty);
          console.log(rpValid);
          console.log(rpDiscountQty);
          console.log('==test end==');

          if (rpQtyStatus && rpValid && rpDiscountQty > 0) {
            // can track this product is already been 'consumed', and not counted into the other required purchased promotion

            let rpDiscountQtyTrack = rpDiscountQty;

            for (let rpIndex = 0; rpIndex < matchedCartItems.length; rpIndex++) {
              const rpKey = `${matchedCartItems[rpIndex].itemId}-${matchedCartItems[rpIndex].cartItemDate.toFixed(0)}`;

              if (rpCategoryIdUsedPromoDict[rpKey] === true) {
                // existed already, can skip
                continue;
              }
              else {
                // rpCategoryIdUsedPromoDict[rpKey] = true;

                let qtyToDeduct = 0;
                if (matchedCartItems[rpIndex].quantity >= rpDiscountQtyTrack) {
                  qtyToDeduct = rpDiscountQtyTrack;
                }
                else {
                  qtyToDeduct = matchedCartItems[rpIndex].quantity;
                }

                rpDiscountQtyTrack -= qtyToDeduct;

                if (rpCategoryIdUsedPromoDict[rpKey] === undefined) {
                  rpCategoryIdUsedPromoDict[rpKey] = qtyToDeduct;
                }
                else {
                  rpCategoryIdUsedPromoDict[rpKey] += qtyToDeduct;
                }

                if (rpCategoryIdUsedPromoDict[rpKey] >= matchedCartItems[rpIndex].quantity) {
                  rpCategoryIdUsedPromoDict[rpKey] = true;
                }

                if (rpDiscountQtyTrack <= 0) {
                  break;
                }
              }
            }
          }

          if (
            rpQtyStatus &&
            qualifiedCartItemsInfo.quantity >= percentageOffCategory.quantityMin
            // &&
            // tempCartItem.quantity <= percentageOffCategory.quantityMax
          ) {
            if (
              // qualifiedCartItemsInfo.price >= 
              tempTotalPriceSp >=
              percentageOffCategory.priceMin
            ) {
              // var individualItemPrice = tempCartItemPrice / tempCartItem.quantity;
              var individualItemPrice = BigNumber(tempCartItemPrice).dividedBy(tempCartItem.quantity).toNumber();

              // tempCartItemPrice -=
              //   ((individualItemPrice * percentageOffCategory.percentageOff) / 100) * promotionResult.discountQuantity;
              tempCartItemPrice =
                BigNumber(tempCartItemPrice).minus(
                  BigNumber(
                    individualItemPrice
                  ).multipliedBy(
                    BigNumber(
                      percentageOffCategory.percentageOff
                    ).dividedBy(100)
                  )
                    .multipliedBy(
                      ((rpValid && (promotionResult.discountQuantity === tempCartItem.quantity))
                        ? rpDiscountQty : promotionResult.discountQuantity)
                    )
                ).toNumber();
              tempCartItemPrice = Math.max(tempCartItemPrice, percentageOffCategory.minPriceToDiscounted);
              promotionIdAppliedListTemp.push(percentageOffCategory.promotionId);

              promotionIdTemp = percentageOffCategory.promotionId;

              cartItemPromotionIdListTemp.push(promotionIdTemp);
            }
          }
        }
      }

      //////////////////////////////////////////////////////////////////////

      // calculate takeaway promotions

      if (orderTypeMo === ORDER_TYPE.PICKUP) {
        var takeawayOffCategory = undefined;
        if (
          selectedOutletItemCategoriesDict[tempCartItem.categoryId] &&
          takeawayCategoryNameDict[
          selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
          ] !== undefined
          && takeawayCategoryNameDict[
            selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
          ].usePromoCode === true
          &&
          checkIsAllowPromotionVoucherToApply(
            currOutlet.allowStackedPromotionVoucher,
            APPLY_DISCOUNT_TYPE.PROMOTION_PROMO_CODE,
            takeawayCategoryNameDict[
            selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
            ],
            selectedPromoCodePromotion,
            {},
            promotionIdAppliedListTemp,
          )
        ) {
          takeawayOffCategory =
            takeawayCategoryNameDict[
            selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
            ];
        }

        if (takeawayItemSkuDict[tempCartItem.itemSku] !== undefined &&
          takeawayItemSkuDict[tempCartItem.itemSku].usePromoCode === true
          &&
          checkIsAllowPromotionVoucherToApply(
            currOutlet.allowStackedPromotionVoucher,
            APPLY_DISCOUNT_TYPE.PROMOTION_PROMO_CODE,
            takeawayItemSkuDict[tempCartItem.itemSku],
            selectedPromoCodePromotion,
            {},
            promotionIdAppliedListTemp,
          )
          // &&
          // (takeawayItemSkuDict[tempCartItem.itemSku].applyBefore === APPLY_BEFORE.ORDER_PLACED || takeawayItemSkuDict[tempCartItem.itemSku].applyBefore === undefined)
        ) {
          var promotionResult = checkApplyDiscountPerValidity(takeawayItemSkuDict[tempCartItem.itemSku], promotionIdAppliedListTemp, tempCartItem);

          if (promotionResult.validity) {
            if (
              tempCartItemPrice >=
              takeawayItemSkuDict[tempCartItem.itemSku].takeawayDiscountAboveAmount
            ) {
              // takeawayDiscountAmount +=
              //   takeawayItemSkuDict[tempCartItem.itemSku].takeawayDiscountAmount;

              // tempCartItemPrice -=
              //   takeawayItemSkuDict[tempCartItem.itemSku].takeawayDiscountAmount * promotionResult.discountQuantity;

              takeawayDiscountAmount =
                BigNumber(takeawayDiscountAmount).plus(takeawayItemSkuDict[tempCartItem.itemSku].takeawayDiscountAmount).toNumber();

              tempCartItemPrice =
                BigNumber(tempCartItemPrice).minus(
                  BigNumber(takeawayItemSkuDict[tempCartItem.itemSku].takeawayDiscountAmount).multipliedBy(promotionResult.discountQuantity)
                ).toNumber();

              tempCartItemPrice = Math.max(tempCartItemPrice, 0);

              promotionIdAppliedListTemp.push(takeawayItemSkuDict[tempCartItem.itemSku].promotionId);

              promotionIdTemp = takeawayItemSkuDict[tempCartItem.itemSku].promotionId;

              cartItemPromotionIdListTemp.push(promotionIdTemp);
            }
          }
        } else if (takeawayOffCategory !== undefined
          // &&
          // (takeawayOffCategory.applyBefore === APPLY_BEFORE.ORDER_PLACED || takeawayOffCategory.applyBefore === undefined)
        ) {
          var promotionResult = checkApplyDiscountPerValidity(takeawayOffCategory, promotionIdAppliedListTemp, tempCartItem);

          if (promotionResult.validity) {
            if (
              tempCartItemPrice >= takeawayOffCategory.takeawayDiscountAboveAmount
            ) {
              // takeawayDiscountAmount += takeawayOffCategory.takeawayDiscountAmount;

              // tempCartItemPrice -=
              //   takeawayOffCategory.takeawayDiscountAmount * promotionResult.discountQuantity;              

              takeawayDiscountAmount =
                BigNumber(takeawayDiscountAmount).plus(takeawayOffCategory.takeawayDiscountAmount).toNumber();

              tempCartItemPrice =
                BigNumber(tempCartItemPrice).minus(
                  BigNumber(takeawayOffCategory.takeawayDiscountAmount).multipliedBy(promotionResult.discountQuantity)
                ).toNumber();

              tempCartItemPrice = Math.max(tempCartItemPrice, 0);

              promotionIdAppliedListTemp.push(takeawayOffCategory.promotionId);

              promotionIdTemp = takeawayOffCategory.promotionId;

              cartItemPromotionIdListTemp.push(promotionIdTemp);
            }
          }
        }
      }

      //////////////////////////////////////////////////////////////////////
      //////////////////////////////////////////////////////////////////////
      //////////////////////////////////////////////////////////////////////
      //////////////////////////////////////////////////////////////////////

      //////////////////////////////////////////////////////////////////////

      // var discountPromotions = 0; // use previous or new? (previous = redeem points)
      var discountPromotions = 0; // use previous or new? (previos = redeem points)
      // var discountPromotionsLCC = 0;

      if (tempCartItemPrice < tempCartItemPriceOriginal) {
        // means got deducted by promotions/points

        // discountPromotions = tempCartItemPriceOriginal - tempCartItemPrice;
        discountPromotions = BigNumber(tempCartItemPriceOriginal).minus(tempCartItemPrice).toNumber();
        // discountPromotionsLCC = tempCartItemPriceOriginal - tempCartItemPrice;
      }

      //////////////////////////////////////////////////////////////////////

      tempCartItemsProcessed.push({
        isFreeItem: tempCartItem.isFreeItem || false,
        promotionId: tempCartItem.promotionId || promotionIdTemp,

        promotionIdList: cartItemPromotionIdListTemp,

        priceOriginal: +tempCartItemPriceOriginal.toFixed(2),

        ...(tempCartItemPrice < tempCartItemPriceOriginal && {
          discount: +(tempCartItemPriceOriginal - tempCartItemPrice).toFixed(2),

          discountPromotions: +(tempCartItemPriceOriginal - tempCartItemPrice).toFixed(2),
        }),

        itemId: tempCartItem.itemId,
        choices: tempCartItem.choices,
        remarks: tempCartItem.remarks,
        fireOrder: tempCartItem.fireOrder,
        cartItemDate: moment(tempCartItem.cartItemDate).valueOf(),
        image: tempCartOutletItemsDict[tempCartItem.itemId].image,
        name: tempCartOutletItemsDict[tempCartItem.itemId].name,
        itemName: tempCartOutletItemsDict[tempCartItem.itemId].name,
        itemSku: tempCartOutletItemsDict[tempCartItem.itemId].sku,
        printerAreaList: tempCartOutletItemsDict[tempCartItem.itemId].printerAreaList || [],
        printingTypeList: tempCartOutletItemsDict[tempCartItem.itemId].printingTypeList || null,
        price: +tempCartItemPrice.toFixed(2),
        quantity: tempCartItem.quantity,
        addOns: tempCartItemAddOnParsed.sort((a, b) => {
          return (
            ((a.oi !== undefined)
              ? a.oi
              : tempCartItemAddOnParsed.length) -
            ((b.oi !== undefined)
              ? b.oi
              : tempCartItemAddOnParsed.length)
          );
        }),

        deliveredAt: null,
        cookedAt: null,
        isChecked: false,

        orderType: orderTypeMo, // might relocate in other places in future
        orderTypeSub: orderTypeSubMo,

        prepareTime:
          tempCartOutletItemsDict[tempCartItem.itemId].prepareTime *
          tempCartItem.quantity,

        categoryId: tempCartItem.categoryId,

        discountPromotions, // to record deducted amount

        isDocket: tempCartOutletItemsDict[tempCartItem.itemId].isDocket || false,
        printDocketQuantity: tempCartOutletItemsDict[tempCartItem.itemId].printDocketQuantity || 1,

        originCartItemId: tempCartItem.originCartItemId || null,

        extraPrice: +parseFloat(extraPrice).toFixed(2),

        ...(tempCartItem.priceVariable !== undefined && {
          priceVariable: tempCartItem.priceVariable,
        }),

        priceType: tempCartItem.priceType ? tempCartItem.priceType : PRODUCT_PRICE_TYPE.FIXED,
        unitType: tempCartItem.unitType ? tempCartItem.unitType : UNIT_TYPE.GRAM,

        itemCostPrice: tempCartItem.itemCostPrice ? tempCartItem.itemCostPrice : 0,

        ...(tempCartItem.tId !== undefined && {
          tId: tempCartItem.tId,
          tRate: tempCartItem.tRate,
          tCode: tempCartItem.tCode,
          tName: tempCartItem.tName,
        }),
      });

      tempTotalPrice += tempCartItemPrice;
      // tempTotalPrice = BigNumber(tempTotalPrice).plus(tempCartItemPrice).toNumber();
      tempTotalPrepareTime +=
        tempCartOutletItemsDict[tempCartItem.itemId].prepareTime *
        tempCartItem.quantity;

      discountPromotionsTotalTemp += discountPromotions;

      if (tempCartItem.tId === 'default' || tempCartItem.tId === undefined) {
        if (tempTotalPriceTaxDict['default'] !== undefined) {
          tempTotalPriceTaxDict['default'] += tempCartItemPrice;
        }
        else {
          tempTotalPriceTaxDict['default'] = tempCartItemPrice;
        }
      }
      else {
        if (tempTotalPriceTaxDict[tempCartItem.tId] !== undefined) {
          tempTotalPriceTaxDict[tempCartItem.tId] += tempCartItemPrice;
        }
        else {
          tempTotalPriceTaxDict[tempCartItem.tId] = tempCartItemPrice;
        }
      }
    }

    //here can undo the sorting back (need do operate on the tempCartItemsProccessed)
    tempCartItemsProcessed.sort((a, b) => {
      return cartItemIdOrderIndexDict[a.itemId + a.cartItemDate] -
        cartItemIdOrderIndexDict[b.itemId + b.cartItemDate];
    });

    //////////////////////////////////////////////////////////

    // add free items to processed carts

    var freeItemQuantitySummaries = [];

    var redeemedB1F1PromotionIdList = [];

    const buy1Free1ItemSkuDictList = Object.entries(buy1Free1ItemSkuDict).map(
      ([key, value]) => ({
        ...value,
        key, // the item sku
      }),
    );

    for (var i = 0; i < buy1Free1ItemSkuDictList.length; i++) {
      if (
        true
        // buy1Free1ItemSkuDictList[i].applyBefore === APPLY_BEFORE.ORDER_PLACED || buy1Free1ItemSkuDictList[i].applyBefore === undefined
      ) {
        var quantity = 0;

        for (var j = 0; j < cartItemQuantitySummaries.length; j++) {
          if (buy1Free1ItemSkuDictList[i].key === cartItemQuantitySummaries[j].item.sku ||
            buy1Free1ItemSkuDictList[i].key === cartItemQuantitySummaries[j].item.itemSku) {
            // means eligible item, accum quantity

            var promotionResult = checkApplyDiscountPerValidity(buy1Free1ItemSkuDictList[i], promotionIdAppliedListTemp, { quantity: cartItemQuantitySummaries[j].quantity });

            if (promotionResult.validity
              &&
              checkIsAllowPromotionVoucherToApply(
                currOutlet.allowStackedPromotionVoucher,
                APPLY_DISCOUNT_TYPE.PROMOTION,
                buy1Free1ItemSkuDictList[i],
                selectedPromoCodePromotion,
                {},
                promotionIdAppliedListTemp,
              )) {
              quantity += cartItemQuantitySummaries[j].quantity;
              if (buy1Free1ItemSkuDictList[i].key === cartItemQuantitySummaries[j].item.sku) {
                promotionIdAppliedListTemp.push(buy1Free1ItemSkuDictList[i].promotionId);
              }
              else if (buy1Free1ItemSkuDictList[i].key === cartItemQuantitySummaries[j].item.itemSku) {
                promotionIdAppliedListTemp.push(buy1Free1ItemSkuDictList[i].promotionId);
              }
            }
          }
        }

        if (
          quantity >= buy1Free1ItemSkuDictList[i].buyAmount
          &&
          !redeemedB1F1PromotionIdList.includes(
            buy1Free1ItemSkuDictList[i].promotionId,
          )
        ) {
          // need add more than 1, if lets said purchased 2, but the buyAmount condition is 1, then free 2 b1f1 sets

          var freeSetsNum = Math.floor(quantity / buy1Free1ItemSkuDictList[i].buyAmount);

          for (var k = 0; k < freeSetsNum; k++) {
            redeemedB1F1PromotionIdList.push(
              buy1Free1ItemSkuDictList[i].promotionId,
            );
          }
        }
      }
    }

    const buy1Free1CategoryNameDictList = Object.entries(
      buy1Free1CategoryNameDict,
    ).map(([key, value]) => ({
      ...value,
      key,
    }));

    for (var i = 0; i < buy1Free1CategoryNameDictList.length; i++) {
      if (
        true
        // buy1Free1CategoryNameDictList[i].applyBefore === APPLY_BEFORE.ORDER_PLACED || buy1Free1CategoryNameDictList[i].applyBefore === undefined
      ) {
        var quantity = 0;

        for (var j = 0; j < cartItemQuantitySummaries.length; j++) {
          if (
            selectedOutletItemCategoriesDict[
            cartItemQuantitySummaries[j].item.categoryId
            ] &&
            buy1Free1CategoryNameDictList[i].key ===
            selectedOutletItemCategoriesDict[
              cartItemQuantitySummaries[j].item.categoryId
            ].name
          ) {
            // means eligible item, accum quantity

            var promotionResult = checkApplyDiscountPerValidity(buy1Free1CategoryNameDictList[i], promotionIdAppliedListTemp, { quantity: cartItemQuantitySummaries[j].quantity });

            if (promotionResult.validity
              &&
              checkIsAllowPromotionVoucherToApply(
                currOutlet.allowStackedPromotionVoucher,
                APPLY_DISCOUNT_TYPE.PROMOTION,
                buy1Free1CategoryNameDictList[i],
                selectedPromoCodePromotion,
                {},
                promotionIdAppliedListTemp,
              )) {
              promotionIdAppliedListTemp.push(buy1Free1CategoryNameDictList[i].promotionId);
              quantity += cartItemQuantitySummaries[j].quantity;
            }
          }
        }

        if (
          quantity >= buy1Free1CategoryNameDictList[i].buyAmount
          &&
          !redeemedB1F1PromotionIdList.includes(
            buy1Free1CategoryNameDictList[i].promotionId,
          )
        ) {
          // redeemedB1F1PromotionIdList.push(
          //   buy1Free1CategoryNameDictList[i].promotionId,
          // );

          // need add more than 1, if lets said purchased 2, but the buyAmount condition is 1, then free 2 b1f1 sets

          var freeSetsNum = Math.floor(quantity / buy1Free1CategoryNameDictList[i].buyAmount);

          for (var k = 0; k < freeSetsNum; k++) {
            redeemedB1F1PromotionIdList.push(
              buy1Free1CategoryNameDictList[i].promotionId,
            );
          }
        }
      }
    }

    const buy1Free1ItemAllList = buy1Free1ItemSkuDictList.concat(
      buy1Free1CategoryNameDictList,
    );

    for (var i = 0; i < redeemedB1F1PromotionIdList.length; i++) {
      const b1f1Promotion = buy1Free1ItemAllList.find(
        (promotion) => promotion.promotionId === redeemedB1F1PromotionIdList[i],
      );

      if (
        b1f1Promotion &&
        b1f1Promotion.getVariation ===
        PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS
      ) {
        for (var j = 0; j < b1f1Promotion.getVariationItemsSku.length; j++) {
          // const freeItem =
          //   selectedOutletItemsSkuDict[b1f1Promotion.getVariationItemsSku[j]];

          let foundItem = outletItems.find(findItem => findItem.sku === b1f1Promotion.getVariationItemsSku[j]);

          const freeItem =
            foundItem;

          if (freeItem) {
            const getItemPrice = b1f1Promotion.getPrice > 0 ? +(b1f1Promotion.getPrice / b1f1Promotion.getVariationItemsSku.length).toFixed(2) : b1f1Promotion.getPrice;

            tempCartItemsProcessed.push({
              isFreeItem: true,
              promotionId: b1f1Promotion.promotionId,
              promotionName: b1f1Promotion.campaignName,

              promotionIdList: [b1f1Promotion.promotionId],

              voucherId: '',
              voucherName: '',

              // priceOriginal: getItemPrice,
              priceOriginal: freeItem.price * b1f1Promotion.getAmount,

              ...(getItemPrice < freeItem.price && {
                discount: +(freeItem.price - getItemPrice).toFixed(2),

                discountPromotions: +(freeItem.price - getItemPrice).toFixed(2),
              }),

              itemId: freeItem.uniqueId,
              choices: [],
              remarks: '',
              fireOrder: false,
              // cartItemDate: Date.now(),
              cartItemDate: moment().add(i, 'second').valueOf(), // if multiple items might caused issue

              image: freeItem.image,
              name: freeItem.name,
              itemName: freeItem.name,
              itemSku: freeItem.sku,
              // price: b1f1Promotion.getPrice * b1f1Promotion.getAmount, 
              price: getItemPrice, // try different logic
              quantity: b1f1Promotion.getAmount,
              addOns: [],

              deliveredAt: null,
              cookedAt: null,
              isChecked: false,

              orderType: orderTypeMo, // might relocate in other places in future
              orderTypeSub: orderTypeSubMo,

              prepareTime: freeItem.prepareTime * b1f1Promotion.getAmount,

              cartItemPointsDeducted: 0,
              cartItemAmountDeducted: 0,

              categoryId: freeItem.categoryId,

              printerAreaList: freeItem.printerAreaList || [],
              printingTypeList: freeItem.printingTypeList || null,

              isDocket: freeItem.isDocket || false,
              printDocketQuantity: freeItem.printDocketQuantity || 1,
            });

            // tempTotalPrice += b1f1Promotion.getPrice * b1f1Promotion.getAmount;

            // tempTotalPrice += getItemPrice;
            tempTotalPrice = BigNumber(tempTotalPrice).plus(getItemPrice).toNumber();
            tempTotalPrepareTime +=
              freeItem.prepareTime * b1f1Promotion.getAmount;


            if (getItemPrice < freeItem.price) {
              discountPromotionsTotalTemp += +(freeItem.price - getItemPrice).toFixed(2);
            };

            if (tempCartItem.tId === 'default' || tempCartItem.tId === undefined) {
              if (tempTotalPriceTaxDict['default'] !== undefined) {
                tempTotalPriceTaxDict['default'] += tempCartItemPrice;
              }
              else {
                tempTotalPriceTaxDict['default'] = tempCartItemPrice;
              }
            }
            else {
              if (tempTotalPriceTaxDict[tempCartItem.tId] !== undefined) {
                tempTotalPriceTaxDict[tempCartItem.tId] += tempCartItemPrice;
              }
              else {
                tempTotalPriceTaxDict[tempCartItem.tId] = tempCartItemPrice;
              }
            }
          }
        }
      }
    }

    //////////////////////////////////////////////////////////

    // console.log('tempOutletsTaxDict');
    // console.log(tempOutletsTaxDict);
    // console.log('tempCartOutletItemsDict');
    // console.log(tempCartOutletItemsDict);
    // console.log('tempCartOutletItemAddOnDict');
    // console.log(tempCartOutletItemAddOnDict);
    // console.log('tempCartOutletItemAddOnChoiceDict');
    // console.log(tempCartOutletItemAddOnChoiceDict);
    // console.log('tempCartItemsProcessed');
    // console.log(tempCartItemsProcessed);

    //////////////////////////////////////////////////////////////////////

    CommonStore.update((s) => {
      s.outletsTaxDict = tempOutletsTaxDict;
      s.cartOutletItemsDict = tempCartOutletItemsDict;
      s.cartOutletItemAddOnDict = tempCartOutletItemAddOnDict;
      s.cartOutletItemAddOnChoiceDict = tempCartOutletItemAddOnChoiceDict;
      s.cartItemsMoProcessed = tempCartItemsProcessed;
    });

    setPromotionIdAppliedList(promotionIdAppliedListTemp);

    setTotalPrice(tempTotalPrice);

    // if (tempOutletsTaxDict[currOutletId]) {
    //   setTotalTax(tempTotalPrice * tempOutletsTaxDict[currOutletId].rate);
    // }
    // else {
    //   setTotalTax(tempTotalPrice * 0.06);
    // }

    if (checkToApplyTaxOrNot(currOutlet, orderTypeMo, orderTypeSubMo)) {
      // setTotalTax(tempTotalPrice * currOutlet.taxRate);
      // setTotalTax(BigNumber(tempTotalPrice).multipliedBy(currOutlet.taxRate).toNumber());

      let totalTaxTemp = 0;
      const tempTotalPriceTaxList = Object.entries(tempTotalPriceTaxDict).map(
        ([key, value]) => {
          let taxTemp = 0;
          let tRate = currOutlet.taxRate;
          let tCode = currOutlet.taxCode ? currOutlet.taxCode : 'SST';
          let tName = currOutlet.taxName ? currOutlet.taxName : 'SST';

          if (key === 'default') {
            taxTemp = BigNumber(taxTemp).plus(
              BigNumber(value).multipliedBy(currOutlet.taxRate)
            ).toNumber();
          }
          else {
            let foundCustomTax = outletCustomTaxList.find(customTax => customTax.uniqueId === key);

            if (foundCustomTax) {
              tRate = foundCustomTax.rate;
              tCode = foundCustomTax.code;
              tName = foundCustomTax.name;

              taxTemp = BigNumber(taxTemp).plus(
                BigNumber(value).multipliedBy(foundCustomTax.rate)
              ).toNumber();
            }
            else {
              taxTemp = BigNumber(taxTemp).plus(
                BigNumber(value).multipliedBy(currOutlet.taxRate)
              ).toNumber();
            }
          }

          totalTaxTemp = BigNumber(totalTaxTemp).plus(taxTemp).toNumber();

          return {
            key: key,
            totalPrice: value,
            tax: taxTemp,
            tRate: tRate,
            tCode: tCode,
            tName: tName,
          };
        },
      );

      setTotalTax(totalTaxTemp);

      CommonStore.update(s => {
        s.totalPriceTaxList = tempTotalPriceTaxList;
      });
    }
    else {
      setTotalTax(0);

      CommonStore.update(s => {
        s.totalPriceTaxList = [{
          key: '',
          totalPrice: tempTotalPrice,
          tax: 0,
          tRate: 0,
          tCode: '',
          tName: 'SST',
        }];
      });
    }

    if (checkToApplyScOrNot(currOutlet, orderTypeMo, orderTypeSubMo)) {
      // setTotalSc(tempTotalPrice * currOutlet.scRate);
      setTotalSc(excludeSkipScItems(BigNumber(tempTotalPrice).multipliedBy(currOutlet.scRate).toNumber(), tempCartItemsProcessed, currOutlet.scRate));
    }
    else {
      setTotalSc(0);
    }

    ////////////////////////////////////////

    // 2023-05-10 - For other d. sc

    if (currOutlet.scActiveOtherD &&
      orderTypeMo === ORDER_TYPE.PICKUP &&
      orderTypeSubMo === ORDER_TYPE_SUB.OTHER_DELIVERY) {
      // setTotalSc(tempTotalPrice * currOutlet.scRate);
      setTotalSc(excludeSkipScItems(BigNumber(tempTotalPrice).multipliedBy(currOutlet.scRateOtherD).toNumber(), tempCartItemsProcessed, currOutlet.scRate));

      setScOtherDApplied(true);
    }
    else {
      setScOtherDApplied(false);
    }

    ////////////////////////////////////////

    setTotalPrepareTime(tempTotalPrepareTime);

    setDiscountPromotionsTotal(discountPromotionsTotalTemp);

    setIsCartLoading(false);
  };

  const setState = () => { };

  // navigation.dangerouslyGetParent().setOptions({
  //   tabBarVisible: false,
  // });

  // navigation.setOptions({
  //   headerLeft: () => (
  //     <TouchableOpacity
  //       onPress={() => {
  //         if (isAlphaUser || true) {
  //           navigation.navigate('MenuOrderingScreen');

  //           CommonStore.update((s) => {
  //             s.currPage = 'MenuOrderingScreen';
  //             s.currPageStack = [...currPageStack, 'MenuOrderingScreen'];
  //           });
  //         }
  //         else {
  //           navigation.navigate('Table');

  //           CommonStore.update((s) => {
  //             s.currPage = 'Table';
  //             s.currPageStack = [...currPageStack, 'Table'];
  //           });
  //         }
  //         if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
  //           CommonStore.update((s) => {
  //             s.expandTab = EXPAND_TAB_TYPE.OPERATION;
  //           });
  //         }
  //       }}
  //       style={{
  //         width: windowWidth * 0.17,
  //         justifyContent: 'center',
  //         alignItems: 'center',
  //       }}>
  //       <Image
  //         style={[{
  //           width: 124,
  //           height: 26,
  //         }, switchMerchant ? {
  //           transform: [
  //             { scaleX: 0.7 },
  //             { scaleY: 0.7 }
  //           ],
  //         } : {}]}
  //         resizeMode="contain"
  //         source={require('../assets/image/logo.png')}
  //       />
  //     </TouchableOpacity>
  //   ),
  //   headerTitle: () => (
  //     <View
  //       style={[
  //         {
  //           justifyContent: Platform.OS === 'android' ? 'center' : 'flex-start',
  //           alignItems: Platform.OS === 'android' ? 'center' : 'flex-start',
  //           marginRight: Platform.OS === 'ios' ? "27%" : 0,
  //           bottom: switchMerchant ? '2%' : 0,
  //           width: Platform.OS === 'ios' ? "96%" : "55%",
  //         },
  //         windowWidth >= 768 && switchMerchant
  //           ? { right: windowWidth * 0.1 }
  //           : {},
  //         windowWidth <= 768
  //           ? { right: windowWidth * 0.12 }
  //           : {},
  //       ]}>
  //       <Text
  //         style={{
  //           fontSize: switchMerchant ? 20 : 24,
  //           // lineHeight: 25,
  //           textAlign: 'left',
  //           alignItems: 'flex-start',
  //           justifyContent: 'flex-start',
  //           fontFamily: 'NunitoSans-Bold',
  //           color: Colors.whiteColor,
  //           opacity: 1,
  //           paddingLeft: Platform.OS === 'ios' ? "1%" : 0,
  //         }}>
  //         Cart
  //       </Text>
  //     </View>
  //   ),
  //   headerRight: () => (
  //     <View
  //       style={{
  //         flexDirection: 'row',
  //         alignItems: 'center',
  //         justifyContent: 'space-between',
  //         // backgroundColor: 'red',
  //       }}>
  //       {outletSelectDropdownView()}
  //       <View
  //         style={{
  //           backgroundColor: 'white',
  //           width: 0.5,
  //           height: windowHeight * 0.025,
  //           opacity: 0.8,
  //           marginHorizontal: 15,
  //           bottom: -1,
  //         }}></View>

  //       <TouchableOpacity
  //         onPress={() => {
  //     if (global.currUserRole === 'admin') {
  //         navigation.navigate('Setting');
  //     }
  // }}
  //         style={{ flexDirection: 'row', alignItems: 'center' }}>
  //         <Text
  //           style={{
  //             fontFamily: 'NunitoSans-SemiBold',
  //             fontSize: 16,
  //             color: Colors.secondaryColor,
  //             marginRight: 15,
  //           }}>
  //           {name}
  //         </Text>

  //         <View
  //           style={{
  //             marginRight: 30,
  //             width: windowHeight * 0.05,
  //             height: windowHeight * 0.05,
  //             borderRadius: windowHeight * 0.05 * 0.5,
  //             alignItems: 'center',
  //             justifyContent: 'center',
  //             backgroundColor: 'white',
  //           }}>
  //           <Image
  //             style={{
  //               width: windowHeight * 0.035,
  //               height: windowHeight * 0.035,
  //               alignSelf: 'center',
  //             }}
  //             source={require('../assets/image/profile-pic.jpg')}
  //           />
  //         </View>
  //       </TouchableOpacity>
  //     </View>
  //   ),
  // });

  const MenuItem = (param) => {
    ApiClient.GET(API.getItemAddOnChoice + param).then((result) => {
      setState({ menuItemDetails: result });
    });
  };

  const deleteCRMSegments = async () => {
    APILocal.deleteSupplier({ body }).then((result) => {
      // ApiClient.POST(API.createSupplier, body).then((result) => {
      if (result && result.status === 'success') {
        Alert.alert(
          'Success',
          'CRM has been deleted',
          [
            {
              text: 'OK',
              onPress: () => {
              },
            },
          ],
          { cancelable: false },
        );
      } else {
        Alert.alert('Error', 'Supplier failed to create');
      }
    });

  };
  // componentDidMount() {
  //   const { navigation } = props;
  //   _unsubscribe = navigation.addListener('focus', () => {
  //     getCartItem()
  //   });
  //   getCartItem();
  //   Cart.setRefreshCartPage(getCartItem.bind(this));
  //   // console.log("CART OUTLET", outletData)
  //   ApiClient.GET(API.merchantMenu + outletData.id).then(
  //     (result) => {
  //       if (result != undefined) {
  //         if (result.length > 0) {
  //           setState({
  //             category: result[0].category,
  //             menu: result[0].items,
  //           });
  //         }
  //         setState({ outletMenu: result });
  //       }
  //       else {
  //         setState({
  //           category: result[0].category,
  //           menu: result[0].items,
  //         });
  //         setState({ outletMenu: [] });
  //       }
  //     }
  //   );

  //   getPopular()
  // }

  // componentWillUnmount() {
  //   _unsubscribe();
  // }

  // function here
  const sum = (key) => {
    return reduce((a, b) => a + (b[key] || 0), 0);
  };

  const getCartItem = () => {
    setState({ cartItem: Cart.getCartItem() }, () => {
      calculateFinalTotal();
    });
    // console.log('888', Cart.getCartItem());
  };

  const checkDuplicate = (item, i, j) => {
    for (const r of item) {
      if (r[0] == i && r[1] == j) {
        return true;
      }
    }
    return false;
  };

  const getPopular = () => {
    var newList = [];
    var randomList = [];
    ApiClient.GET(API.activeMenu + outletData.id).then((result) => {
      var maxItem = 0;
      for (const item of result) {
        maxItem += item.items.length;
      }
      var maxIteration = 5;
      if (maxItem < 5) {
        maxIteration = maxIteration;
      }
      var k;
      for (k = 0; k < maxIteration; k++) {
        var i = Math.floor(Math.random() * result.length);
        var j = Math.floor(Math.random() * result[i].items.length);

        while (checkDuplicate(randomList, i, j)) {
          i = Math.floor(Math.random() * result.length);
          j = Math.floor(Math.random() * result[i].items.length);
        }
        newList.push(result[i].items[j]);
        randomList.push([i, j]);
        setState({ popular: newList });
      }
    });
  };

  const goToPopular = (item) => {
    // console.log(outletData, 'outletData');

    props.navigation.navigate({
      name: 'MoMenuItemDetails',
      params: {
        refresh: refresh.bind(this),
        menuItem: item,
        outletData,
        test,
      },
      merge: true,
    });
  };

  const renderPopularItem = ({ item, index }) => {
    return (
      <View
        style={{
          width: 180,
          height: 80,
          borderWidth: StyleSheet.hairlineWidth,
          marginRight: 10,
          borderRadius: 10,
          justifyContent: 'space-around',
        }}>
        <Text
          numberOfLines={2}
          style={{
            fontSize: switchMerchant ? 10 : 16,
            fontWeight: '400',
            marginLeft: 10,
          }}>
          {item.name}
        </Text>
        <View style={{ flexDirection: 'row', marginLeft: 10, marginTop: 10 }}>
          <Text style={{ fontSize: switchMerchant ? 10 : 16, color: '#9c9c9c' }}>
            {item.price}
          </Text>
          <TouchableOpacity
            style={{ marginLeft: 70 }}
            onPress={() => {
              goToPopular(item);
            }}>
            <Close name="pluscircle" size={22} color={Colors.primaryColor} />
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  const refresh = () => {
    setState({ refresh: true });
  };

  const onChangeQty = (e, id) => {
    // const cartItem = cartItem;
    const cartItem = Cart.getCartItem();
    const item = cartItem.find((obj) => obj.itemId === id);
    item.quantity -= e;
    if (item.quantity == 0) {
      Cart.deleteCartItem(id);
    } else {
      setState({
        cartItem,
      });
      Cart.updateCartItem(
        Cart.getCartItem().find((obj) => obj.itemId === id),
        item,
      );
    }
    // console.log(
    //   'CART',
    //   Cart.getCartItem().find((obj) => obj.itemId === id),
    // );
  };

  const optional = (id) => {
    const cartItem = cartItem;
    // console.log(cartItem);
    const item = cartItem.find((obj) => obj.itemId === id);
    if (item.fireOrder == 0) {
      item.fireOrder = 1;
    } else if (item.fireOrder == 1) {
      item.fireOrder = 0;
    }
    setState({
      cartItem,
    });
  };

  const extend = (id, day) => {
    const body = {
      parkingId: id,
      dayExtend: day,
      paymentMethod: '',
    };
    // console.log('PARKING BODY', body);
    ApiClient.POST(API.extendParking, body).then((result) => {
      // console.log(result);
      if (result != null) {
        Alert.alert(
          'Success',
          'You have extended successfully',
          [
            {
              text: 'OK',
              onPress: () => {
                props.navigation.navigate({
                  name: 'ConfirmOrder',
                  params: {
                    outletData,
                  },
                  merge: true,
                });
              },
            },
          ],
          { cancelable: false },
        );
        User.getRefreshCurrentAction();
        Cart.clearCart();
        getCartItem();
      }
    });
  };

  const newPurchase = (outletId, itemId, qty) => {
    const body = {
      userId: User.getUserId(),
      outletId,
      itemId,
      quantity: qty,
      waiterId: 1,
    };
    ApiClient.POST(API.createUserParking, body).then((result) => {
      // console.log(result);
      if (result != null) {
        Alert.alert(
          'Success',
          'You have parked successfully',
          [
            {
              text: 'OK',
              onPress: () => {
                props.navigation.navigate({
                  name: 'ConfirmOrder',
                  params: {
                    outletData
                  },
                  merge: true,
                });
              },
            },
          ],
          { cancelable: false },
        );
        User.getRefreshCurrentAction();
        Cart.clearCart();
        getCartItem();
      }
    });
  };

  const clearCartItems = async () => {
    // await AsyncStorage.removeItem(`${firebaseUid}.cartItems`);

    CommonStore.update((s) => {
      s.cartItemsMo = [];
      s.cartItemsMoProcessed = [];
    });
  };

  const occupyingSeats = async (seatedPax) => {
    await new Promise((resolve, reject) => {
      var moPaxParsed = !isNaN(parseInt(moPax)) ? parseInt(moPax) : 0;

      if (!selectedOutletTableMo || !moPaxParsed) {
        Alert.alert('Error', 'Minimum pax must be 1');
        resolve();
      } else {
        var body = {
          tableId: selectedOutletTableMo.uniqueId,
          pax: moPaxParsed,
          outletId: currOutletId,
        };

        // ApiClient.POST(API.addCustomer, body, false)
        APILocal.addCustomer({ body }).then((result) => {
          // if (result.outletId == User.getOutletId()) {
          //     getTableBySection(currentSectionArea)
          //     setState({ seatingModal: false })
          //     Alert.alert("Table seated!")
          // }

          if (result && result.status === 'success') {
            //setSeatingModal(false);
            // Alert.alert('Success', 'Table has been seated');
          }

          resolve();
        });
      }
    });
  };
  const placeUserOrder = async () => {
    // var orderBodyType = '';
    // switch (Cart.getOrderType()) {
    //   case 0:
    //     orderBodyType = 'Dine In';
    //     break;
    //   case 1:
    //     orderBodyType = 'Take Away';
    //     break;
    //   case 2:
    //     orderBodyType = 'Pick Up';
    //     break;
    // }

    CommonStore.update((s) => {
      s.isLoading = true;
    });

    // const outletSnapshot = await firestore()
    //   .collection(Collections.Outlet)
    //   .where(
    //     'uniqueId',
    //     '==',
    //     cartOutletItemsDict[cartItems[0].itemId].outletId,
    //   )
    //   .limit(1)
    //   .get();

    // var merchant = {};
    // var outlet = {};

    // if (!outletSnapshot.empty) {
    //   outlet = outletSnapshot.docs[0].data();

    //   const merchantSnapshot = await firestore()
    //     .collection(Collections.Merchant)
    //     .where('uniqueId', '==', outlet.merchantId)
    //     .limit(1)
    //     .get();

    //   if (!merchantSnapshot.empty) {
    //     merchant = merchantSnapshot.docs[0].data();
    //   }
    // }

    ////////////////////////////////////////////////////////

    // generate register url for this order

    // var orderIdEncrypted = hashids.encodeHex(
    //   selectedOutletTableMo.uniqueId.replaceAll('-', ''),
    // );

    ////////////////////////////////////////////////////////
    if ((selectedOutletTableMo.uniqueId !== undefined && orderTypeMo === ORDER_TYPE.DINEIN) ||
      (selectedOutletTableMo.uniqueId === undefined && orderTypeMo === ORDER_TYPE.PICKUP)) {

      if (isLater === 'LATER' && !scheduleDateTime) {
        Alert.alert(
          'Warning',
          'Please select your preferred pickup time before placing the order.',
          [{ text: 'OK' }],
          { cancelable: false }
        );

        CommonStore.update(s => {
          s.isLoading = false;
        });

        global.isPlacingOrder = false;
        return;
      }

      if (isLater === 'LATER' && scheduleDateTime) {
        const currentTime = moment();
        const scheduledTime = moment(scheduleDateTime);

        if (scheduledTime.isBefore(currentTime)) {
          Alert.alert(
            'Warning',
            'Selected schedule time cannot be earlier than current time. Please select a future time.',
            [{ text: 'OK' }],
            { cancelable: false }
          );

          CommonStore.update(s => {
            s.isLoading = false;
          });

          global.isPlacingOrder = false;
          return;
        }
      }

      if (selectedPromoCodePromotion && selectedPromoCodePromotion.uniqueId) {
        if (promotionsDict[selectedPromoCodePromotion.uniqueId] &&
          promotionsDict[selectedPromoCodePromotion.uniqueId].promoCodeUsageLimit <= 0) {
          Alert.alert('Info', `Promotion '${selectedPromoCodePromotion.campaignName}' has been fully redeemed.`);

          CommonStore.update(s => {
            s.isLoading = false;
          });

          global.isPlacingOrder = false;

          return;
        }

        if (promotionsDict[selectedPromoCodePromotion.uniqueId] &&
          promotionsDict[selectedPromoCodePromotion.uniqueId].promoCodeCustomerLimit <= 0) {
          Alert.alert('Info', `Promotion '${selectedPromoCodePromotion.campaignName}' usage limit per customer must be more than 0 to proceed.`);

          CommonStore.update(s => {
            s.isLoading = false;
          });

          global.isPlacingOrder = false;

          return;
        }
      }

      ////////////////////////////////////////////////////////

      const orderDateTemp = Date.now();

      var body = {
        // cartItems: cartItems,
        cartItems: currOutlet.retailMode === false ? cartItemsProcessed : cartItemsProcessed.map(item => {
          // Find the corresponding outletItem
          const outletItem = outletItems.find(oi => oi.uniqueId === item.itemId);

          // Get the linked ingredients
          const ingredients = outletItem ? outletItem.stockLinkItems.map(supply => ({
            n: supply.name,
            p: supply.price,
            u: supply.quantityUsage
          })) : [];

          return {
            ...item,
            sli: ingredients
          };
        }),
        promotionIdList: promotionIdAppliedList,
        promoCodePromotionIdList: promotionIdAppliedList.filter(promotionId => {
          if (availablePromoCodePromotions.find(promoCodePromotion => promoCodePromotion.uniqueId === promotionId)) {
            return true;
          }
          else {
            return false;
          }
        }),

        // tableId: Cart.getTableNumber(),
        // outletId: Cart.getOutletId(),
        // type: Cart.getOrderType() == 0 ? "Dine In" : "Take Away",
        orderType: orderTypeMo,
        orderTypeSub: orderTypeSubMo,
        paymentMethod: 'Online Banking',
        userVoucherId: null,
        userAddressId: selectedUserAddress ? selectedUserAddress.uniqueId : null,
        orderDate: orderDateTemp,
        // voucherType: "",
        // customTable: "",
        // sessionId: 0,
        // remarks: null,
        // collectionTime: rev_date === undefined ? new Date() : new Date(rev_date),
        totalPrice,
        outletId: cartOutletItemsDict[cartItems[0].itemId].outletId,

        dineInRequiredAuthorization: currOutlet.dineInRequiredAuthorization ? currOutlet.dineInRequiredAuthorization : false,

        merchantId,
        outletCover: currOutlet.cover,
        merchantLogo,
        outletName: currOutlet.name,
        merchantName,

        tableId: orderTypeMo !== ORDER_TYPE.DINEIN ? '' : (selectedOutletTableMo.uniqueId || ''),
        // tablePax: orderTypeMo !== ORDER_TYPE.DINEIN ? 0 : (selectedOutletTableMo.seated || 0),
        tablePax: orderTypeMo !== ORDER_TYPE.DINEIN ? 0 : (selectedOutletTableMo.seated > 0 ? selectedOutletTableMo.seated : (!isNaN(parseInt(moPax)) ? parseInt(moPax) : 0)),
        tableCode: orderTypeMo !== ORDER_TYPE.DINEIN ? '' : (selectedOutletTableMo.code || ''),
        tax: checkToApplyTaxOrNot(currOutlet, orderTypeMo, orderTypeSubMo) ? totalTax : 0,
        totalPriceTaxList: totalPriceTaxList,
        taxId: currOutlet && outletsTaxDict[currOutlet.uniqueId] ? outletsTaxDict[currOutlet.uniqueId].uniqueId : '',
        sc: (currOutlet.scActive && currOutlet.scOrderTypes.includes(orderTypeMo) &&
          (currOutlet.scOrderTypeDetails === undefined || (currOutlet.scOrderTypeDetails && currOutlet.scOrderTypeDetails.includes(ORDER_TYPE_DETAILS.POS)))) ? totalSc : 0,
        discount: totalDiscount,
        discountPromotionsTotal,

        waiterName: (currOutlet.showPinPlaceOrder && global.currPinName !== null) ? global.currPinName : name,
        waiterId: (currOutlet.showPinPlaceOrder && global.currPinId !== null) ? global.currPinId : firebaseUid,

        totalPrepareTime,
        estimatedPreparedDate: moment(orderDateTemp)
          .add(totalPrepareTime, 'second')
          .valueOf(),

        remarks: '',

        outletAddress: currOutlet.address,
        outletPhone: currOutlet.phone,
        outletTaxId: currOutlet && outletsTaxDict[currOutlet.uniqueId]
          ? outletsTaxDict[currOutlet.uniqueId].uniqueId
          : '',
        outletTaxNumber: currOutlet && outletsTaxDict[currOutlet.uniqueId]
          ? outletsTaxDict[currOutlet.uniqueId].taxNumber
          : '',
        outletTaxName: currOutlet && outletsTaxDict[currOutlet.uniqueId] ? outletsTaxDict[currOutlet.uniqueId].name : 'SST',
        // outletTaxRate: (currOutlet && outletsTaxDict[currOutlet.uniqueId]) ? outletsTaxDict[currOutlet.uniqueId].rate : 0.06,
        outletTaxRate: checkToApplyTaxOrNot(currOutlet, orderTypeMo, orderTypeSubMo) ? currOutlet.taxRate : 0,

        outletTaxOrderTypes: currOutlet.taxOrderTypes ? currOutlet.taxOrderTypes : '',

        outletScRate: checkToApplyScOrNot(currOutlet, orderTypeMo, orderTypeSubMo) ? currOutlet.scRate : 0,

        outletScOrderTypes: currOutlet.scOrderTypes ? currOutlet.scOrderTypes : [ORDER_TYPE.DINEIN],

        paymentDetails: null,

        tableQRUrl: currTableQRUrl ? currTableQRUrl : '',

        deliveryPackagingFee: currOutlet.deliveryPackagingFee && orderTypeMo === ORDER_TYPE.DELIVERY ? currOutlet.deliveryPackagingFee : 0,
        pickupPackagingFee: currOutlet.pickupPackagingFee && orderTypeMo === ORDER_TYPE.PICKUP && orderTypeSubMo === ORDER_TYPE_SUB.NORMAL ? currOutlet.pickupPackagingFee : 0,

        isCounterOrdering,

        noUserId: true,

        appType: APP_TYPE.MERCHANT,

        scName: currOutlet.scName ? currOutlet.scName : '',

        isLater: isLater ? isLater : 'NOW',
        scheduleDateTime: scheduleDateTime ? scheduleDateTime : null,

        odpt: orderTypeMo === ORDER_TYPE.PICKUP && orderTypeSubMo === ORDER_TYPE_SUB.OTHER_DELIVERY ? odpt : '',
        odpoi: orderTypeMo === ORDER_TYPE.PICKUP && orderTypeSubMo === ORDER_TYPE_SUB.OTHER_DELIVERY ? odpoi : '',

        retailMode: currOutlet.retailMode !== undefined ? currOutlet.retailMode : false,

        // orderIdShared: currOutlet.orderIdShared !== undefined ? currOutlet.orderIdShared : false,

        ...((customerUniqueId?.trim() !== '' && customerName?.trim() !== '') ? {
          userName: customerName || '',
          userPhone: (customerPhone && customerPhone.length > 3) ? customerPhone : '',
          crmUserId: customerUniqueId || '',
        } : {})
      };

      ////////////////////////////////////////

      // 2023-05-10 - For other d. sc

      if (currOutlet.scActiveOtherD &&
        orderTypeMo === ORDER_TYPE.PICKUP &&
        orderTypeSubMo === ORDER_TYPE_SUB.OTHER_DELIVERY) {
        body.outletScRate = currOutlet.scRateOtherD;
      }

      ////////////////////////////////////////

      // 2024-07-29 - to auto pay other d. orders

      if (
        orderTypeMo === ORDER_TYPE.PICKUP &&
        orderTypeSubMo === ORDER_TYPE_SUB.OTHER_DELIVERY &&
        odpt
      ) {
        const opmMatched = outletPaymentMethods.find(opm => opm.deliveryPartner === odpt);

        if (opmMatched && opmMatched.uniqueId) {
          // to auto pay

          body.cartItems = cartItemsProcessed.map(cip => {
            return {
              ...cip,

              priceToPay: cip.price,
            };
          });

          let paymentChannel = '';
          if (opmMatched.channel) {
            paymentChannel = paymentMethod.channel;
          }
          else if (opmMatched.name) {
            paymentChannel = opmMatched.name;
          }

          body.paymentMethodId = opmMatched.uniqueId ? opmMatched.uniqueId : null;
          body.paymentDetails = {
            channel: paymentChannel,
            remarks: '',

            bankCode: '',
            last4CardDigit: '',
          };
          body.paymentDate = moment().valueOf();

          const orderIdLocal = uuidv4();

          body.orderIdLocal = orderIdLocal;
          body.combinedOrderListFrom = [orderIdLocal];
        }
      }

      ////////////////////////////////////////

      // (
      //   netInfo.isInternetReachable && netInfo.isConnected
      //     ?
      //     ApiClient.POST(API.createUserOrder, body)
      //     :
      //     APILocal.createUserOrder({
      //       body: body,
      //       uid: firebaseUid,
      //       role: role,
      //     })
      // )
      APILocal.createUserOrder({
        body,
        uid: firebaseUid,
        role,
      })
        .then(async (result) => {
          // console.log('placeOrder', result);

          //////////////////////////////////////////

          // reset promo code selection

          CommonStore.update(s => {
            s.selectedPromoCodePromotion = {};

            s.selectedOutletTableMo = {};
          });

          setSelectedPromoCodePromotionId('');

          // reset member
          TableStore.update(s => {
            s.customerName = '';
            s.customerPhone = '+60';
            s.customerUniqueId = '';
          })
          //////////////////////////////////////////

          // CommonStore.update((s) => {
          //   s.isLoading = false;
          // });

          // if (result) {
          //   await clearCartItems();

          //   if (userCart.userId) {
          //     await deleteUserCart();
          //   }

          //   Alert.alert(
          //     'Success',
          //     'Your order has been placed',
          //     [
          //       {
          //         text: 'OK',
          //         onPress: () => {
          //           props.navigation.navigate('Table');
          //         },
          //       },
          //     ],
          //     { cancelable: false },
          //   );

          //   User.getRefreshCurrentAction();
          // }

          // if (result &&
          //   result.uniqueId &&
          //   !netInfo.isInternetReachable && netInfo.isConnected) {
          //   await printUserOrder({
          //     orderId: result.uniqueId,
          //   },
          //     false,
          //     [PRINTER_USAGE_TYPE.ORDER_SUMMARY, PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
          //     false,
          //     false,
          //     false,
          //     netInfoData = { isInternetReachable: netInfo.isInternetReachable, isConnected: netInfo.isConnected },
          //   );
          // }        

          if ((result && result.uniqueId) &&
            (isCounterOrdering || orderTypeMo === ORDER_TYPE.PICKUP)) {
            setTimeout(async () => {
              CommonStore.update((s) => {
                s.isLoading = false;
              });

              if (orderTypeSubMo !== ORDER_TYPE_SUB.OTHER_DELIVERY) {
                // takeaway navigate table (1) off
                if (global.currOutlet.taNT1Off) {
                  // do nothing
                }
                else {
                  if (global.currOutlet.taNT4Off) {
                    // do nothing
                  }
                  else {
                    TableStore.update(s => {
                      s.orderDisplayIndividual = false;
                      s.orderDisplayProduct = false;
                      s.orderDisplaySummary = true;

                      s.viewTableOrderModal = false;
                      s.renderPaymentSummary = false;
                      s.renderReceipt = false;

                      s.displayQrModal = false;
                      s.displayQModal = false;
                      s.deleteTableModal = false;
                      s.updateTableModal = false;
                      s.joinTableModal = false;
                      s.moveOrderModal = false;
                      s.addSectionAreaModel = false;
                      s.addTableModal = false;
                      s.preventDeleteTableModal = false;
                      s.seatingModal = false;
                      s.showLoyaltyModal = false;
                      s.showAddLoyaltyModal = false;
                      s.cashbackModal = false;
                    });
                  }

                  setTimeout(() => {
                    global.openPaymentSummary = true;

                    CommonStore.update(s => {
                      s.isCheckingOutTakeaway = true;

                      s.checkingOutTakeawayOrder = result;

                      s.timestamp = Date.now();

                      s.checkingOutTakeawayTimestamp = Date.now();

                      s.currPage = 'Table';
                    }, () => {
                      setTimeout(() => {
                        // takeaway navigate table (2) off
                        if (global.currOutlet.taNT2Off) {
                          // do nothing
                        }
                        else {
                          navigation.navigate('Table');
                        }
                      }, global.currOutlet.taNT6Timer ? global.currOutlet.taNT6Timer : 100);
                    });
                  }, global.currOutlet.taNT5Timer ? global.currOutlet.taNT5Timer : 100);
                }
              }

              // Alert.alert(
              //   'Success',
              //   'Your order has been placed',
              //   [
              //     {
              //       text: 'OK',
              //       onPress: () => {
              //         // CommonStore.update(s => {
              //         //   s.isCheckingOutTakeaway = true;

              //         //   s.checkingOutTakeawayOrder = result;

              //         //   s.timestamp = Date.now();
              //         // }, () => {
              //         //   navigation.navigate('Table');
              //         // });

              //         // props.navigation.navigate('Table');
              //       },
              //     },
              //   ],
              //   { cancelable: false },
              // );

              User.getRefreshCurrentAction();

              console.log('=====================================');
              console.log('clear cart items');
              console.log('=====================================');

              await clearCartItems();

              if (userCart.userId) {
                await deleteUserCart();
              }
            }, 100);
          }

          /////////////////////////////////////

          CommonStore.update((s) => {
            s.orderType = ORDER_TYPE.DINEIN;
            s.orderTypeSub = ORDER_TYPE_SUB.NORMAL;

            s.orderTypeMo = ORDER_TYPE.DINEIN;
            s.orderTypeSubMo = ORDER_TYPE_SUB.NORMAL;

            s.moMethod = 'dinein';
          });

          global.isPlacingOrder = false;
          global.currPinId = null;
          global.currPinName = null;

          // setMethod('dinein');

          ////////////////////////////////////////

          // 2024-07-29 - to print auto paid other d. orders

          if (
            orderTypeMo === ORDER_TYPE.PICKUP &&
            orderTypeSubMo === ORDER_TYPE_SUB.OTHER_DELIVERY &&
            odpt
          ) {
            const opmMatched = outletPaymentMethods.find(opm => opm.deliveryPartner === odpt);

            if (opmMatched && opmMatched.uniqueId) {
              // to auto pay

              let payPRNum = global.currOutlet.payPRNum;
              // if (selectedPaymentMethodType === OFFLINE_PAYMENT_METHOD_TYPE.CASH) {
              //   payPRNum = 1;
              // }

              for (let indexPR = 0; indexPR < payPRNum; indexPR++) {
                logToFile('mo cart - printUserOrder - receipt');

                await printUserOrder(
                  {
                    // orderId: result.orderId,
                    orderData: result,
                    receiptNote: currOutlet.receiptNote || '',
                  },
                  true,
                  [PRINTER_USAGE_TYPE.RECEIPT],
                  // selectedPaymentMethodType === OFFLINE_PAYMENT_METHOD_TYPE.CASH,
                  false, // use false for isOpenCashDrawer first
                  false,
                  false,
                  netInfoData = { isInternetReachable: netInfo.isInternetReachable, isConnected: netInfo.isConnected },
                  true, // for isPrioritized
                );
              }
            }
          }

          ////////////////////////////////////////

          // var isNeededToPrintHere = false;

          // if (netInfo.isInternetReachable && netInfo.isConnected) {
          //   // if (result.orderTypeMo === ORDER_TYPE.DINEIN) {
          //   //   isNeededToPrintHere = true;
          //   // }
          // }
          // else {
          //   isNeededToPrintHere = true;
          // }

          // if (isNeededToPrintHere) {
          //   await printUserOrder(
          //     {
          //       orderData: result,
          //     },
          //     false,
          //     [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
          //     false,
          //     false,
          //     false,
          //     { isInternetReachable: true, isConnected: true },
          //   );

          //   await printUserOrder(
          //     {
          //       orderData: result,
          //     },
          //     false,
          //     [PRINTER_USAGE_TYPE.ORDER_SUMMARY],
          //     false,
          //     false,
          //     false,
          //     { isInternetReachable: true, isConnected: true },
          //   );
          // }
        });

      if (
        // 2022-10-25 - Fixes
        // !isCounterOrdering && orderTypeMo !== ORDER_TYPE.PICKUP
        true
      ) {
        setTimeout(async () => {
          CommonStore.update((s) => {
            s.isLoading = false;

            s.currPage = 'Table';
          });

          console.log('=====================================');
          console.log('clear cart items');
          console.log('=====================================');

          await clearCartItems();

          if (userCart.userId) {
            await deleteUserCart();
          }

          // Alert.alert(
          //   'Success',
          //   'Your order has been placed',
          //   [
          //     {
          //       text: 'OK',
          //       onPress: () => {
          //         props.navigation.navigate('Table');
          //       },
          //     },
          //   ],
          //   { cancelable: false },
          // );

          if (
            orderTypeMo === ORDER_TYPE.DINEIN
            ||
            orderTypeSubMo === ORDER_TYPE_SUB.OTHER_DELIVERY
            ||
            global.currOutlet.taNT3On // takeaway navigate table (3) on
          ) {
            props.navigation.navigate('Table');
          }

          User.getRefreshCurrentAction();
        }, 100);
      }
    }
    else {
      if (orderTypeMo === ORDER_TYPE.DINEIN) {
        Alert.alert('Info', 'Please select a table before placing the order, or restart the app')
      }
      if (orderTypeMo === ORDER_TYPE.PICKUP) {
        Alert.alert('Info', 'Please unselect the table before placing the order, or restart the app')
      }

      CommonStore.update(s => {
        s.isLoading = false;
      });

      global.isPlacingOrder = false;
      global.currPinId = null;
      global.currPinName = null;
    }

  };

  const placeOrder = () => {
    var orderBodyType = '';
    switch (Cart.getOrderType()) {
      case 0:
        orderBodyType = 'Dine In';
        break;
      case 1:
        orderBodyType = 'Take Away';
        break;
      case 2:
        orderBodyType = 'Pick Up';
        break;
    }

    var body = {
      items: Cart.getCartItem(),
      tableId: Cart.getTableNumber(),
      outletId: Cart.getOutletId(),
      // type: Cart.getOrderType() == 0 ? "Dine In" : "Take Away",
      type: orderBodyType,
      paymentMethod: 'Online Banking',
      voucherId: '',
      voucherType: '',
      customTable: '',
      sessionId: 0,
      remarks: null,
      collectionTime: rev_date === undefined ? new Date() : new Date(rev_date),
    };
    // console.log('PLACE ORDER BODY', body);

    ApiClient.POST(API.createOrder, body).then((result) => {
      // console.log('placeOrder', result);
      if (result.success == true) {
        if (type == 1) {
          orderDelivery(result);
        } else if (type == 2) {
          orderPickUp(result);
        }

        // Alert.alert(
        //   'Success',
        //   'Your order has been placed',
        //   [
        //     {
        //       text: 'OK',
        //       onPress: () => {
        //if navFrom Takeaway then jump to Home
        // if (navFrom == "TAKEAWAY") {
        //   props.navigation.jumpTo('Home')
        // }
        // else {
        //   props.navigation.popToTop()
        // }
        // props.navigation.navigate('Table');
        // },
        // },
        // ],
        // { cancelable: false },
        // );
        props.navigation.navigate('Table');
        User.getRefreshCurrentAction();
        Cart.clearCart();
        getCartItem();
      }
    });
  };

  const orderDelivery = (result) => {
    var deliverBody = {
      orderId: result.id,
      userId: User.getUserId(),
      addressId: Cart.getDeliveryAddress().id,
    };
    ApiClient.POST(API.createOrderDelivery, deliverBody).then((result) => {
      // console.log('order delivery', result);
      var body = {
        serviceType: 'MOTORCYCLE',
        specialRequests: [],
        stops: [
          {
            // Location information for pick-up point
            location: {
              lat: currOutlet.latlng.split(',')[0],
              lng: currOutlet.latlng.split(',')[1],
            },
            addresses: {
              ms_MY: {
                displayString: currOutlet.address,
                country: 'MY_KUL',
              },
            },
          },
          {
            // Location information for drop-off point (#1)
            location: {
              lat: Cart.getDeliveryAddress().lat,
              lng: Cart.getDeliveryAddress().lng,
            },
            addresses: {
              ms_MY: {
                displayString: Cart.getDeliveryAddress().address,
                country: 'MY_KUL',
              },
            },
          },
        ],
        // Pick-up point copntact details
        requesterContact: {
          name: 'Chris Wong', //get outlet person in charge?
          phone: '0376886555', //or get waiter?
        },
        deliveries: [
          {
            // Contact information at the drop-off point (#1)
            toStop: 1,
            toContact: {
              name: User.getName(),
              phone: User.getUserData().number,
            },
            remarks: Cart.getDeliveryAddress().note,
          },
        ],
      };
      if (UTCRevDate != undefined) {
        const scheduleAt = UTCRevDate;
        // console.log('scheduleAt', scheduleAt);
        body.scheduleAt = scheduleAt;
        // console.log('SCHEDULE BODY', body);
        getScheduleQuotation(body, result);
      } else {
        // console.log('NOW BODY ', body);
        placeDelivery(Cart.getDeliveryQuotation(), body, result);
      }
    });
  };

  const orderPickUp = (result) => {
    var pickUpBody = {
      orderId: result.id,
      userId: User.getUserId(),
      // addressId: Cart.getDeliveryAddress().id,
    };
    ApiClient.POST(API.createOrderPickUp, pickUpBody).then((result) => {
      // console.log('order pickup', result);
    });
  };

  const getScheduleQuotation = (body, order) => {
    ApiClient.POST(API.lalamoveQuotation, body).then((result) => {
      // console.log('quotation result', result);
      placeDelivery(result, body, order);
    });
  };

  const placeDelivery = (quotation, body, order) => {
    // console.log('Placing delivery', quotation);
    body.quotedTotalFee = {
      amount: quotation.totalFee,
      currency: quotation.totalFeeCurrency,
    };
    // console.log('LALA ORDER BODY', body);
    ApiClient.POST(API.lalamovePlaceOrder, body).then((result) => {
      // console.log('lalamvoe place order', result);
      if (result) {
        const lalaOrderId = result.orderRef;
        ApiClient.GET(API.lalamoveGetOrderStatus + result.orderRef).then(
          (result) => {
            // console.log('lalamove order status', result);

            var updateBody = {
              orderId: order.orderId,
              lalaOrderId,
              orderStatus: result.status,
            };
            // console.log('UPDATE BODY', updateBody);
            ApiClient.PATCH(API.updateOrderDelivery, updateBody).then(
              (result) => {
                // console.log('update order delivery', result);
              },
            );
          },
        );
      }
    });
  };

  const calculateFinalTotal = () => {
    // console.log('cartItem', cartItem);
    const totalFloat = parseFloat(
      cartItem.reduce(
        (accumulator, current) =>
          accumulator + current.price * current.quantity,
        0,
      ),
    );

    const totalTax = parseFloat(
      (cartItem
        .reduce(
          (accumulator, current) =>
            accumulator + current.price * current.quantity,
          0,
        )
        .toFixed(2) /
        parseFloat(100)) *
      parseFloat(Cart.getTax()),
    );

    //+ '001' //.toFixed() bug, occour only if last significant digit is 5 and if fixing to only 1 less digit, i.e. 1.75.toFixed(1) => 1.7 instead of 1.8
    var taxStr = totalTax.toString().split('.');
    // console.log(taxStr);
    if (taxStr.length > 1) {
      taxStr[1] += '001';
    }
    const taxFloat = parseFloat(`${taxStr[0]}.${taxStr[1]}`);

    setState({
      totalFloat: parseFloat(totalFloat).toFixed(2),
      taxFloat: parseFloat(taxFloat).toFixed(2),
    });
  };

  const generateScheduleTime = () => {
    const now = new Date();
    var m = ((((now.getMinutes() + 7.5) / 15) | 0) * 15) % 60;
    var h = (((now.getMinutes() / 105 + 0.5) | 0) + now.getHours()) % 24;
    const nearestNow = new Date(
      now.getFullYear(),
      now.getMonth(),
      now.getDate(),
      h,
      m,
    );
    var startTime = moment(nearestNow, 'YYYY-MM-DD hh:mm');
    var endTime = moment(
      new Date(
        now.getFullYear(),
        now.getMonth(),
        now.getDate() + 1,
        now.getHours(),
        now.getMinutes(),
      ),
      'YYYY-MM-DD hh:mm a',
    );

    var result = [];
    var current = moment(startTime);

    var tempstr = current.format('llll').split(',');
    var daystr = tempstr[0];
    var datestr = tempstr[1].split(' ');
    var yeartimestr = tempstr[2].split(' ');
    var currentNearestTime =
      `${daystr
      } ${datestr[2]
      } ${datestr[1]
      },${yeartimestr[2]
      },${yeartimestr[1]
      },${yeartimestr[3]}`;
    result.push(currentNearestTime);

    current.add(15, 'minutes');
    while (current <= endTime) {
      var tempstr = current.format('llll').split(',');
      // console.log('tempstr', tempstr);
      var daystr = tempstr[0];
      var datestr = tempstr[1].split(' ');
      var yeartimestr = tempstr[2].split(' ');
      var finalstr =
        `${daystr
        } ${datestr[2]
        } ${datestr[1]
        },${yeartimestr[2]
        },${yeartimestr[1]
        },${yeartimestr[3]}`;
      result.push(finalstr);

      current.add(15, 'minutes');
    }
    // console.log('schedule result', result);
    setState({
      schedulteTimeList: result,
      schedulteTimeSelected: result[0],
      currentNearestTime,
    });
  };

  const renderSuccess = () => (
    <View style={{ backgroundColor: 'blue' }}>
      <Image source={require('../assets/image/dineinGrey.png')} />

    </View>
  );

  const renderSchedule = (item) => (
    <View
      style={{
        flexDirection: 'row',
        padding: 20,
        justifyContent: 'space-between',
        alignContent: 'center',
      }}>
      <View style={{ justifyContent: 'flex-start', marginHorizontal: 10 }}>
        <Text>
          {currentNearestTime == item.toString()
            ? 'TODAY'
            : item.toString().split(',')[0]}
        </Text>
      </View>
      <View style={{ justifyContent: 'flex-end', marginHorizontal: 10 }}>
        <Text>
          {currentNearestTime == item.toString()
            ? 'ASAP'
            : item.toString().split(',')[1]}
        </Text>
      </View>
    </View>
  );

  const proceedToMenuItemDetailsForUpdating = async (item) => {
    // const outletItemSnapshot = await firestore()
    //   .collection(Collections.OutletItem)
    //   .where('uniqueId', '==', item.itemId)
    //   .limit(1)
    //   .get();

    // if (!outletItemSnapshot.empty) {
    //   const outletItem = outletItemSnapshot.docs[0].data();

    //   CommonStore.update((s) => {
    //     s.selectedOutletItem = outletItem;

    //     s.onUpdatingCartItem = item;
    //   });

    //   props.navigation.navigate('MenuItemDetails', {
    //     refresh: refresh.bind(this),
    //     menuItem: outletItem,
    //     cartItem: item,
    //     outletData: currOutlet,
    //   });
    // }

    let foundItem = outletItems.find(findItem => findItem.uniqueId === item.itemId);

    if (foundItem) {
      const outletItem = foundItem;

      CommonStore.update((s) => {
        s.selectedOutletItem = outletItem;

        s.onUpdatingCartItem = item;
        s.isOnMenu = false;
      });

      //props.navigation.navigate('MenuItemDetails', {
      //  refresh: refresh.bind(this),
      //  menuItem: outletItem,
      //  cartItem: item,
      //  outletData: currOutlet,
      //});
    }
  };

  const removeFromCartItems = async (item) => {
    var updateCartItemIndex = 0;

    for (var i = 0; i < cartItems.length; i++) {
      if (
        cartItems[i].itemId === item.itemId &&
        cartItems[i].cartItemDate === item.cartItemDate
      ) {
        updateCartItemIndex = i;
      }
    }

    const newCartItems = [
      ...cartItems.slice(0, updateCartItemIndex),
      ...cartItems.slice(updateCartItemIndex + 1),
    ];

    if (newCartItems.length > 0) {
      // await AsyncStorage.setItem(
      //   `${firebaseUid}.cartItems`,
      //   JSON.stringify(newCartItems),
      // );
    } else {
      // await AsyncStorage.removeItem(`${firebaseUid}.cartItems`);
    }

    CommonStore.update((s) => {
      s.cartItemsMo = newCartItems;
    });

    // if (newCartItems.length === 0)
    //   props.navigation.goBack();
  };

  const renderItem = (params) => {
    // var item = {
    //   ...params.item,
    // };

    // if (cartOutletItemsDict[item.itemId]) {
    //   item.image = cartOutletItemsDict[item.itemId].image;
    //   item.name = cartOutletItemsDict[item.itemId].name;
    //   item.itemName = cartOutletItemsDict[item.itemId].name;

    // }

    const { item } = params;

    // console.log('item');
    // console.log(item);

    var overrideCategoryPrice = undefined;
    if (
      selectedOutletItemCategoriesDict[item.categoryId] &&
      overrideCategoryPriceNameDict[
      selectedOutletItemCategoriesDict[item.categoryId].name
      ] !== undefined
    ) {
      overrideCategoryPrice =
        overrideCategoryPriceNameDict[
          selectedOutletItemCategoriesDict[item.categoryId].name
        ].overridePrice;
    }

    /////////////////////////////////////////////////////////

    return <>
      <View>
        <View
          style={{
            flexDirection: 'row',
            paddingVertical: 2,
            paddingBottom: 2,
            borderColor: Colors.descriptionColor,
            justifyContent: 'flex-start',
            alignContent: 'center',
            alignItems: 'center',
            display: 'flex',
            // borderBottomWidth: StyleSheet.hairlineWidth,
            // marginBottom: 5,
            //backgroundColor: 'green',
          }}>
          {/* <View
            style={[
              {
                marginRight: 15,
                backgroundColor: Colors.secondaryColor,
                //backgroundColor: 'red',
                borderRadius: 5,
                alignItems: 'center',
                // flex: 1,
              },
              item.image
                ? {
                  // display: 'flex',
                  // alignItems: 'center',
                  // justifyContent: 'center',
                }
                : {
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                },
            ]}>
            {item.image ? (
              <AsyncImage
                source={{ uri: item.image }}
                item={item}
                style={{
                  // width: 70,
                  // height: 70,
                  width: switchMerchant
                    ? windowWidth * 0.03
                    : windowWidth * 0.04,
                  height: switchMerchant
                    ? windowWidth * 0.03
                    : windowWidth * 0.04,
                  borderRadius: 5,
                }}
              />
            ) : (
              <View style={{
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: Colors.secondaryColor,
                width: switchMerchant
                  ? windowWidth * 0.03
                  : windowWidth * 0.04,
                height: switchMerchant
                  ? windowWidth * 0.03
                  : windowWidth * 0.04,
                borderRadius: 5,
              }}>
                <Ionicons name="fast-food-outline" size={switchMerchant ? 12 : 25} />
              </View>
            )}
          </View> */}

          <View
            style={{
              // backgroundColor: 'red',
              width: windowWidth * 0.2,
            }}>
            <View
              style={{
                display: 'flex',
                // width: 180,
                // backgroundColor: 'red',
              }}>
              <Text
                numberOfLines={2}
                style={{
                  // fontWeight: "700",
                  fontSize: switchMerchant ? windowWidth / 60 : 18,
                  fontFamily: 'NunitoSans-Bold',
                  color: Colors.mainTxtColor,
                }}>
                {item.name}
              </Text>

              {/* {item.options ? item.options.map((item, index) => {
            return (
              (item.quantity > 0 ? <Text style={{ color: Colors.descriptionColor, marginTop: 5, fontWeight: '400', fontSize: 11, fontFamily: "NunitoSans-Regular" }}>
                {" "}
              + {item.quantity} ({item.itemName})
            </Text> : null)
            );
          }) : null} */}

              {item.addOns &&
                item.addOns.length > 0 &&
                item.addOns.map((addOn, index) => {
                  const addOnChoices = addOn.choiceNames.join(', ');

                  return (
                    <Text
                      key={index}
                      style={{
                        color: Colors.descriptionColor,
                        marginTop: 3,
                        // fontWeight: "700",
                        fontSize: switchMerchant ? windowWidth / 60 : 14,
                        marginBottom: 3,
                        fontFamily: 'NunitoSans-Regular',
                      }}>
                      {/* Size: {item.size == "small" ? "Small" : item.size == "medium" ? "Medium" : item.size == "big" ? "Big" : null} */}
                      {addOn.quantities > 0 ? (
                        <>
                          {`+ ${addOn.quantities.reduce(
                            (accum, value) => accum + value,
                            0,
                          )} (${addOnChoices}) (+RM${addOn.prices.reduce((accumAddOn, priceAddOn) => accumAddOn + priceAddOn, 0)
                            .toFixed(2)
                            .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')})`}
                        </>
                      ) : (
                        <>
                          {`${addOn.name
                            } (${addOnChoices}) (+RM${addOn.prices.reduce((accumAddOn, priceAddOn) => accumAddOn + priceAddOn, 0)
                              .toFixed(2)
                              .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')})`}
                        </>
                      )
                      }
                    </Text>
                  );
                })}
            </View>

            {/* <Text style={{ 
          fontWeight: "700", 
          fontSize: 19, 
          fontFamily: 
          "NunitoSans-Regular" 
        }}>
          {test1 == 1 ? item.name : test2 == 1 ? item.name : ('x' + item.quantity + " " + item.name)}
        </Text> */}

            {/* {test1 == 1 ?
          <Text style={{ fontSize: 14, fontFamily: "NunitoSans-Regular", color: Colors.descriptionColor }}> ({item.quantity} days extension)
        </Text>
          : test2 == 1 ? <Text style={{ fontSize: 14, fontFamily: "NunitoSans-Regular", color: Colors.descriptionColor }}> x{item.quantity} mugs
          </Text> : null} */}

            {item.remarks ? (
              <Text
                style={{
                  color: Colors.descriptionColor,
                  marginTop: 0,
                  fontWeight: '400',
                  fontSize: switchMerchant ? windowWidth / 60 : 14,
                  fontFamily: 'NunitoSans-Italic',
                }}>
                Remarks: {item.remarks}
              </Text>
            ) : null}

            {/* {type == 0 ? <TouchableOpacity
            onPress={() => {
              optional(item.itemId);
            }}
          >
            <Text
              style={{
                color: Colors.primaryColor,
                marginTop: 3,
                fontWeight: "700",
                fontSize: 14,
                marginBottom: 3,
                fontFamily: "NunitoSans-Regular"
              }}
            >
              {item.fireOrder == 0 ? "Serve now" : item.fireOrder == 1 ? "Serve later" : null}
            </Text>
          </TouchableOpacity> : null} */}
          </View>

          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              // justifyContent: 'flex-end',
              width: windowWidth * 0.06,
            }}>
            <Text
              style={{
                color: Colors.descriptionColor,
                fontSize: switchMerchant ? windowWidth / 60 : 16,
                fontFamily: 'NunitoSans-Regular',
                textAlign: 'center',
              }}>
              x{item.quantity}{item.priceType === PRODUCT_PRICE_TYPE.UNIT ? `${UNIT_TYPE_SHORT[item.unitType]}` : ''}
            </Text>
          </View>
        </View>

        {/*need edit*/}

        <View
          style={{
            flexDirection: 'row',
            width: windowWidth * 0.26,
          }}
        >
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'flex-start',
              width: '35%',
            }}>
            <View
              style={{
                flexDirection: 'row',
                width: '80%',
                justifyContent: 'space-between',
              }}>
              <Text
                style={{
                  color: Colors.descriptionColor,
                  fontSize: switchMerchant ? windowWidth / 60 : 16,
                  fontFamily: 'NunitoSans-Regular',
                }}>
                RM
              </Text>
              <Text
                style={{
                  color: Colors.descriptionColor,
                  fontSize: switchMerchant ? windowWidth / 60 : 16,
                  fontFamily: 'NunitoSans-Regular',
                }}>
                {
                  (
                    (overrideItemPriceSkuDict[item.sku] !== undefined ||
                      overrideCategoryPrice !== undefined)
                      ?
                      overrideItemPriceSkuDict[item.sku] !== undefined ?
                        parseFloat(
                          overrideItemPriceSkuDict[item.sku].overridePrice
                        ).toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')
                        :
                        parseFloat(
                          overrideCategoryPrice,
                        ).toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')
                      :
                      parseFloat(item.price).toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')
                  )
                }
              </Text>
              {/* <Text
              style={{
                color: Colors.descriptionColor,
                fontSize: switchMerchant ? 10 : 16,
                fontFamily: 'NunitoSans-Regular',
              }}>
              {parseFloat(item.price)
                .toFixed(2)
                .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
            </Text> */}
            </View>
            {/* <TouchableOpacity
          onPress={() => {
            if (item.quantity <= 1) {
              Cart.deleteCartItem(item.itemId, item);
              getCartItem();
            }
            else {
              setState({
                visible: true,
                editingItemId: item.itemId,
                qty: item.quantity,
                value: item.quantity,
              });
            }
          }}
        >
          <Entypo name="cross" size={28} color="red" />
        </TouchableOpacity> */}
          </View>

          <View
            style={{
              width: windowWidth,
              flexDirection: 'row',
              // marginLeft: 30,
              alignItems: 'center',
              padding: 15,
            }}>
            {
              (!item.isFreeItem) ? (
                <>
                  <TouchableOpacity
                    onPress={() => {
                      // console.log('ITEM@@@@@@@@@@@@@@@@@@@@@@@@@@@@', item);

                      requestAnimationFrame(() => {
                        proceedToMenuItemDetailsForUpdating(item);
                      });

                      // props.navigation.navigate("MenuItemDetailsUpdate", {
                      //   refresh: refresh.bind(this),
                      //   menuItem: item.menuItem,
                      //   cartItem: item,
                      //   outletData: outletData,
                      // });
                    }}
                    style={
                      {
                        // width: '10%',
                        // marginLeft: 30,
                        // backgroundColor: 'green',
                      }
                    }>
                    {switchMerchant ? (
                      <Icons name="edit" size={windowWidth / 80} color={Colors.primaryColor} />
                    ) : (
                      <Icons name="edit" size={24} color={Colors.primaryColor} />
                    )}
                  </TouchableOpacity>

                  <TouchableOpacity
                    onPress={() => {
                      requestAnimationFrame(() => {
                        removeFromCartItems(item);
                      });
                    }}
                    style={{
                      // width: '10%',
                      // marginLeft: 30,
                      // backgroundColor: 'green',

                      left: 20,
                    }}>
                    {switchMerchant ? (
                      <FontAwesome name="trash-o" size={windowWidth / 80} color={Colors.tabRed} />
                    ) : (
                      <FontAwesome name="trash-o" size={25} color={Colors.tabRed} />
                    )}
                  </TouchableOpacity>
                </>
              )
                :
                (
                  <Text
                    style={{
                      fontFamily: 'NunitoSans-Regular',
                      fontSize: switchMerchant ? 10 : 16,
                      color: Colors.descriptionColor,
                      textAlign: 'center',
                    }}
                    line
                  >
                    {'Bundle\nItem'}
                  </Text>
                )
            }
          </View>
        </View>
      </View>

      <View
        style={{
          height: 1.5,
          left: '-2%',
          width: windowWidth * 0.26,
          backgroundColor: '#C2C1C0',
          opacity: 0.2,
          marginBottom: 2,

          shadowColor: '#000',
          shadowOffset: {
            width: 0,
            height: 1,
          },
          shadowOpacity: 0.22,
          shadowRadius: 2.22,
          elevation: 3,
        }} />
    </>;
  };

  const changeClick = () => {
    //delivery
    if (clicked == 1) {
      //setState({ clicked: 0 })
    } else {
      // setState({ clicked: 1, clicked1: 0, shouldShow: true })
      setClicked(1);
      setClicked1(0);
      // setShouldShow(true);

      Cart.setOrderType(1);

      CommonStore.update((s) => {
        s.orderTypeMo = ORDER_TYPE.PICKUP;
        s.orderTypeSubMo = ORDER_TYPE_SUB.OTHER_DELIVERY;
      });
    }
  };

  const changeClick1 = () => {
    //pickup
    if (clicked1 == 1) {
      //setState({ clicked1: 0 })
    } else {
      // setState({ clicked1: 1, clicked: 0, shouldShow: false })
      setClicked(0);
      setClicked1(1);
      // setShouldShow(false);

      Cart.setOrderType(2);

      CommonStore.update((s) => {
        s.orderTypeMo = ORDER_TYPE.PICKUP;
        s.orderTypeSubMo = ORDER_TYPE_SUB.NORMAL;
      });
    }
  };

  const deleteUserCart = async () => {
    const body = {
      userCartId: userCart.uniqueId,
    };

    ApiClient.POST(API.deleteUserCart, body).then((result) => {
      if (result && result.status === 'success') {
        // console.log('ok');
      }
    });
  };

  const handlePlaceOrder = async () => {
    if (isLoading) return;

    CommonStore.update(s => {
      s.isLoading = true;
    });

    try {
      if (moMethod === 'dinein') {
        if (!selectedOutletTableMo.uniqueId) {
          Alert.alert('Info', 'Please select a table first');
          return;
        }

        const moPaxParsed = parseInt(moPax) || 0;

        if (moPaxParsed > selectedOutletTableMo.capacity) {
          Alert.alert('Info', 'Seat not enough');
          return;
        }

        if (selectedOutletTableMo.seated <= 0) {
          await occupyingSeats(moPaxParsed);
        }
      }

      //////////////////////////

      // 2025-02-28 - place order required pin

      global.pinUnlockCallback = async () => {
        await placeUserOrder();
      };

      if (
        // (
        //   (currOutlet && currOutlet.privileges &&
        //     currOutlet.privileges.includes(PRIVILEGES_NAME.REFUND_ORDER))
        //   && privileges && privileges.includes(PRIVILEGES_NAME.REFUND_ORDER)
        // )
        !currOutlet.showPinPlaceOrder
      ) {
        await global.pinUnlockCallback();
      }
      else {
        CommonStore.update(s => {
          s.pinUnlockType = PRIVILEGES_NAME.ORDER;
          s.showPinUnlockModal = true;
        });
      }

      //////////////////////////
    } catch (error) {
      console.error('Error placing order:', error);
      Alert.alert('Error', 'Failed to place order. Please try again.');
    } finally {
      setIsPlacingOrder(false);
    }
  };

  // function end

  ///////////////////////////////////////////////////////////////

  var overrideCategoryPrice = undefined;

  if (selectedOutletItem && selectedOutletItemCategoriesDict[selectedOutletItem.categoryId] && overrideCategoryPriceNameDict[selectedOutletItemCategoriesDict[selectedOutletItem.categoryId].name] !== undefined) {
    overrideCategoryPrice = overrideCategoryPriceNameDict[selectedOutletItemCategoriesDict[selectedOutletItem.categoryId].name].overridePrice;
  }

  var amountOffCategory = undefined;
  if (selectedOutletItem && selectedOutletItemCategoriesDict[selectedOutletItem.categoryId] && amountOffCategoryNameDict[selectedOutletItemCategoriesDict[selectedOutletItem.categoryId].name] !== undefined) {
    amountOffCategory = amountOffCategoryNameDict[selectedOutletItemCategoriesDict[selectedOutletItem.categoryId].name];
  }

  var percentageOffCategory = undefined;
  if (selectedOutletItem && selectedOutletItemCategoriesDict[selectedOutletItem.categoryId] && percentageOffCategoryNameDict[selectedOutletItemCategoriesDict[selectedOutletItem.categoryId].name] !== undefined) {
    percentageOffCategory = percentageOffCategoryNameDict[selectedOutletItemCategoriesDict[selectedOutletItem.categoryId].name];
  }

  // var pointsRedeemCategory = undefined;
  // if (selectedOutletItemCategoriesDict[item.categoryId] && pointsRedeemCategoryNameDict[selectedOutletItemCategoriesDict[item.categoryId].name] !== undefined) {
  //   pointsRedeemCategory = pointsRedeemCategoryNameDict[selectedOutletItemCategoriesDict[item.categoryId].name];
  // }

  var buy1Free1Category = undefined;
  if (selectedOutletItem && selectedOutletItemCategoriesDict[selectedOutletItem.categoryId] && buy1Free1CategoryNameDict[selectedOutletItemCategoriesDict[selectedOutletItem.categoryId].name] !== undefined) {
    buy1Free1Category = buy1Free1CategoryNameDict[selectedOutletItemCategoriesDict[selectedOutletItem.categoryId].name];
  }

  ///////////////////////////////////////////////////////////////

  return (
    (
      (cartItemsProcessed.length === 0)
      // && isOnMenu
    ) || isCartLoading) ? (
    // <View style={styles.container}>
    //   <View style={styles.sidebar}>

    (<View
      style={[
        styles.container,
        !isTablet()
          ? {
            transform: [{ scaleX: 1 }, { scaleY: 1 }],
          }
          : {},
      ]}>
      <ScrollView
        style={[
          { flex: 1 },
          switchMerchant
            ? { marginBottom: windowHeight * 0.05 }
            : {},
        ]}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={() => {
              // getCartItem();
              // getPopular();
            }}
          />
        }
        contentContainerStyle={{
          paddingBottom: switchMerchant ? (windowHeight * 0.05) : (windowHeight * 0.1),
        }}>

        <View>
          <View>
            <View
              style={{
                flexDirection: 'row',
                justifyContent: 'center',
                alignItems: 'center',
                borderBottomWidth: 0.6,

              }} />

          </View>
          {/* Dine in button (JJ's comment) */}
          <View style={{
            flexDirection: 'row',
            // alignSelf: 'center',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: Colors.fieldtBgColor, paddingVertical: 10, borderBottomWidth: 0.6,  //marginTop: 20, borderWidth: 1.5, borderRadius: 10,

            width: '100%',
            height: windowHeight * 0.07,
            // backgroundColor: 'red',

            marginLeft: -(windowWidth * 0.36) / 7,
          }}>
            <View style={{
              // width: '33.3%'
              width: (windowWidth * 0.36) / 4,
            }}>
              <TouchableOpacity
                // testID='MoCartScreen.buttonDineIn'
                onPress={() => {
                  requestAnimationFrame(() => {
                    // setMethod('dinein');
                    //selectedOutletTableMo.uniqueId != undefined ? null : navigation.navigate('MoTable')                 

                    if (currOutletShiftStatus === OUTLET_SHIFT_STATUS.OPENED) {
                      CommonStore.update((s) => {
                        s.orderType = ORDER_TYPE.DINEIN;
                        s.orderTypeSub = ORDER_TYPE_SUB.NORMAL;

                        s.orderTypeMo = ORDER_TYPE.DINEIN;
                        s.orderTypeSubMo = ORDER_TYPE_SUB.NORMAL;

                        s.moMethod = 'dinein';
                      });
                    }
                    else {
                      Alert.alert('Info', 'Please open the shift first before proceed.')
                    }
                  });
                }}
              // style={{ width: '30%' }}
              >

                <Text style={{
                  fontSize: switchMerchant ? windowWidth / 60 : 18,
                  textAlign: 'center',
                  fontFamily: 'NunitoSans-Bold',
                  // color: moMethod === 'dinein' ? Colors.primaryColor : Colors.blackColor,
                  color: orderTypeMo === ORDER_TYPE.DINEIN ? Colors.primaryColor : Colors.blackColor,
                }}>{`Dine In`}</Text>

              </TouchableOpacity>
            </View>

            <View style={{ borderWidth: 0.5, borderRightWidth: 0, height: windowHeight * 0.07 }} />

            <View style={{
              // width: '33.3%'
              width: (windowWidth * 0.36) / 4,
            }}>
              <TouchableOpacity
                testID='MoCartScreen.buttonTakeaway'
                onPress={() => {
                  requestAnimationFrame(() => {
                    // setMethod('takeaway');

                    if (currOutletShiftStatus === OUTLET_SHIFT_STATUS.OPENED) {
                      CommonStore.update((s) => {
                        s.orderType = ORDER_TYPE.PICKUP;
                        s.orderTypeSub = ORDER_TYPE_SUB.NORMAL;

                        s.orderTypeMo = ORDER_TYPE.PICKUP;
                        s.orderTypeSubMo = ORDER_TYPE_SUB.NORMAL;

                        s.selectedOutletTableMo = {};

                        s.moMethod = 'takeaway';

                        // 2025-06-07 - to address issue when place takeaway orders, the last selected table still listen to snapshot, and causing side effects: table auto unseated
                        s.selectedOutletTable = {};
                      });

                      TableStore.update(s => {
                        s.orderDisplayIndividual = false;
                        s.orderDisplayProduct = false;
                        s.orderDisplaySummary = true;

                        s.viewTableOrderModal = false;
                        s.renderPaymentSummary = false;
                        s.renderReceipt = false;

                        s.displayQrModal = false;
                        s.displayQModal = false;
                        s.deleteTableModal = false;
                        s.updateTableModal = false;
                        s.joinTableModal = false;
                        s.moveOrderModal = false;
                        s.addSectionAreaModel = false;
                        s.addTableModal = false;
                        s.preventDeleteTableModal = false;
                        s.seatingModal = false;
                        s.showLoyaltyModal = false;
                        s.showAddLoyaltyModal = false;
                        s.cashbackModal = false;
                      });
                    }
                    else {
                      Alert.alert('Info', 'Please open the shift first before proceed.')
                    }
                  });
                }}
              // style={{ width: '30%' }}
              >

                <Text style={{
                  fontSize: switchMerchant ? windowWidth / 60 : 18, textAlign: 'center', fontFamily: 'NunitoSans-Bold',
                  // color: moMethod === 'takeaway' ? Colors.primaryColor : Colors.blackColor,
                  color: (orderTypeMo === ORDER_TYPE.PICKUP && orderTypeSubMo === ORDER_TYPE_SUB.NORMAL) ? Colors.primaryColor : Colors.blackColor,
                }}>Takeaway</Text>

              </TouchableOpacity>
            </View>

            <View style={{ borderWidth: 0.5, borderLeftWidth: 0.5, height: windowHeight * 0.07 }} />

            <View style={{
              // width: '33.3%'
              width: (windowWidth * 0.36) / 4,
            }}>
              <TouchableOpacity
                testID='MoCartScreen.buttonOtherD'
                onPress={() => {
                  requestAnimationFrame(() => {
                    // setMethod('takeaway');

                    if (currOutletShiftStatus === OUTLET_SHIFT_STATUS.OPENED) {
                      CommonStore.update((s) => {
                        s.orderType = ORDER_TYPE.PICKUP;
                        s.orderTypeSub = ORDER_TYPE_SUB.OTHER_DELIVERY;

                        s.orderTypeMo = ORDER_TYPE.PICKUP;
                        s.orderTypeSubMo = ORDER_TYPE_SUB.OTHER_DELIVERY;

                        s.moMethod = 'otherD';

                        // 2025-06-07 - to address issue when place takeaway orders, the last selected table still listen to snapshot, and causing side effects: table auto unseated
                        s.selectedOutletTable = {};
                      });

                      TableStore.update(s => {
                        s.orderDisplayIndividual = false;
                        s.orderDisplayProduct = false;
                        s.orderDisplaySummary = true;

                        s.viewTableOrderModal = false;
                        s.renderPaymentSummary = false;
                        s.renderReceipt = false;

                        s.displayQrModal = false;
                        s.displayQModal = false;
                        s.deleteTableModal = false;
                        s.updateTableModal = false;
                        s.joinTableModal = false;
                        s.moveOrderModal = false;
                        s.addSectionAreaModel = false;
                        s.addTableModal = false;
                        s.preventDeleteTableModal = false;
                        s.seatingModal = false;
                        s.showLoyaltyModal = false;
                        s.showAddLoyaltyModal = false;
                        s.cashbackModal = false;
                      });

                      CommonStore.update((s) => {
                        s.odpt = 'INHOUSE';
                        s.odpoi = '';
                      });
                      // setOdpoi('');
                    }
                    else {
                      Alert.alert('Info', 'Please open the shift first before proceed.')
                    }
                  });
                }}
              // style={{ width: '30%' }}
              >

                <Text style={{
                  fontSize: switchMerchant ? windowWidth / 60 : 18, textAlign: 'center', fontFamily: 'NunitoSans-Bold',
                  // color: moMethod === 'takeaway' ? Colors.primaryColor : Colors.blackColor,
                  color: (orderTypeMo === ORDER_TYPE.PICKUP && orderTypeSubMo === ORDER_TYPE_SUB.OTHER_DELIVERY) ? Colors.primaryColor : Colors.blackColor,
                }}>Other D.</Text>
              </TouchableOpacity>
            </View>

          </View>

          {moMethod === 'dinein' ?
            <View stlye={{ flexDirection: 'column' }}>
              <View style={{ flexDirection: 'row', marginTop: 20, marginBottom: 5, alignItems: 'center', justifyContent: 'flex-start', paddingLeft: 20, flexWrap: 'wrap', display: 'flex' }}>
                <View>
                  <Text style={{
                    fontSize: switchMerchant ? windowWidth / 60 : 20,
                    fontFamily: 'NunitoSans-Bold',
                    color: Colors.blackColor,
                    // width: '90%',

                  }}
                    numberOfLines={4}
                  >Table: {selectedOutletTableMo.code ? selectedOutletTableMo.code : ''}</Text>
                  <Text style={{
                    fontSize: switchMerchant ? 10 : 20,
                    fontFamily: 'NunitoSans-Bold',
                    color: Colors.blackColor,
                  }}>{selectedOutletTableMo.capacity ? `(${selectedOutletTableMo.capacity} seats)` : ''}</Text>
                </View>
                <TouchableOpacity
                  testID='MoCartScreen.buttonSelectTable'
                  onPress={() => {
                    // requestAnimationFrame(() => {
                    if (currOutletShiftStatus !== OUTLET_SHIFT_STATUS.OPENED) {
                      Alert.alert('Info', 'Please open the shift first before proceed.');
                      return;
                    }

                    navigation.navigate('MoTable');
                    InteractionManager.runAfterInteractions(() => {

                      CommonStore.update(s => {
                        s.moPax = '1';
                      });
                    });
                    // });
                  }}
                  style={[
                    Styles.button,
                    {
                      marginLeft: 5, backgroundColor: Colors.primaryColor, paddingVertical: 12,
                      // width: windowWidth * 0.1,
                      paddingHorizontal: 30,
                      // borderRadius: 4,
                    }
                  ]}>
                  <Text style={{
                    color: Colors.whiteColor,
                    fontFamily: 'NunitoSans-SemiBold',
                    fontSize: switchMerchant ? windowWidth / 60 : 18,

                  }}>
                    {selectedOutletTableMo.uniqueId === undefined ? 'SELECT' : 'CHANGE'}
                  </Text>
                </TouchableOpacity>

              </View>
              <View style={{ flexDirection: 'row', alignItems: 'center', marginTop: 15, marginBottom: 5, justifyContent: 'flex-start', paddingLeft: 20, }}>
                <Text style={{
                  fontSize: switchMerchant ? windowWidth / 60 : 22,
                  fontFamily: 'NunitoSans-Bold',
                }}>Pax:</Text>
                <View style={{ flexDirection: 'row', paddingLeft: 10, }}>
                  <TouchableOpacity
                    onPress={() => {
                      requestAnimationFrame(() => {
                        // setPax(pax - 1 >= 0 ? pax - 1 : 0);

                        var moPaxParsed = !isNaN(parseInt(moPax)) ? parseInt(moPax) : 1;

                        CommonStore.update(s => {
                          s.moPax = (moPaxParsed - 1 >= 1 ? moPaxParsed - 1 : 1).toFixed(0);
                        });
                      });
                    }}>
                    <View
                      style={[
                        styles.addBtn,
                        { backgroundColor: Colors.descriptionColor },
                        switchMerchant
                          ? {
                            width: windowWidth * 0.03,
                            height: windowWidth * 0.03,
                          }
                          : {},
                      ]}>
                      <Text
                        style={{
                          fontSize: switchMerchant ? windowWidth / 60 : 30,
                          fontWeight: '500',
                          color: Colors.whiteColor,
                        }}>
                        -
                      </Text>
                    </View>
                  </TouchableOpacity>
                  <View
                    style={[
                      styles.addBtn,
                      {
                        backgroundColor: Colors.whiteColor,
                        borderWidth: StyleSheet.hairlineWidth,
                        borderColor: Colors.descriptionColor,
                        borderWidth: 1.5,

                        width: 50,

                        marginLeft: -1,
                      },
                      switchMerchant
                        ? {
                          width: windowWidth * 0.03,
                          height: windowWidth * 0.03,
                        }
                        : {},
                    ]}>
                    <TextInput
                      // editable={editQuantity}
                      style={[
                        {
                          fontSize: switchMerchant ? windowWidth / 80 : 25,
                          fontWeight: 'bold',
                          color: Colors.primaryColor,
                          //paddingTop: 5,
                          paddingBottom: 5,
                        },
                        switchMerchant ? {
                          paddingBottom: Platform.OS === 'ios' ? 5 : 0,
                        } : {

                        },
                      ]}
                      // defaultValue={quantity.toFixed(0)}
                      // onChangeText={(text) => {
                      //   // setQuantity(isNaN(text) ? quantity : parseInt(text))
                      //   text = text.replace(/[^0-9]/g, '');
                      //   setQuantity(text.length > 0 ? parseInt(text) : 0);
                      //   setEditQuantity(false);
                      // }}
                      // onSubmitEditing={(text) => {
                      //   setQuantity(isNaN(text) ? quantity : parseInt(text))                          
                      // }}
                      // onEndEditing={() => {
                      //   setEditQuantity(false);
                      // }}
                      //iOS
                      placeholder={'0'}
                      placeholderTextColor={Platform.select({
                        ios: '#a9a9a9',
                      })}
                      // clearTextOnFocus
                      selectTextOnFocus
                      //////////////////////////////////////////////
                      //Android
                      // onFocus={() => {
                      //   setTemp(moPax);
                      //   CommonStore.update(s => {
                      //     s.moPax = '';
                      //   });
                      // }}
                      ///////////////////////////////////////////////
                      //When textinput is not selected
                      // onEndEditing={() => {
                      //   if (moPax == '') {
                      //     CommonStore.update(s => {
                      //       s.moPax = temp;
                      //     });
                      //   }
                      // }}
                      onChangeText={(text) => {
                        // setState({ itemPrice: text });

                        var value = parseValidIntegerText(text);

                        var moPaxParsed = !isNaN(parseInt(value)) ? parseInt(value) : 1;

                        if (moPaxParsed < selectedOutletTableMo.capacity) {
                          CommonStore.update(s => {
                            s.moPax = moPaxParsed.toFixed(0);
                          });
                        }
                        else if (selectedOutletTableMo.capacity) {
                          CommonStore.update(s => {
                            s.moPax = selectedOutletTableMo.capacity.toFixed(0);
                          });
                        }
                        else {
                          CommonStore.update(s => {
                            s.moPax = '1';
                          });
                        }

                        // setEditQuantity(false);
                      }}
                      value={moPax}
                      // autoFocus={editQuantity}
                      keyboardType={'decimal-pad'}
                      // keyboardType={'default'}
                      // placeholder="0"
                      underlineColorAndroid={Colors.fieldtBgColor}
                    />
                    {/* <TouchableOpacity onPress={() => setEditPax(true)}>
                        {editPax ?
                          <TextInput
                            style={[
                              {
                                fontSize: switchMerchant ? 10 : 25,
                                fontWeight: 'bold',
                                color: Colors.primaryColor,
                                paddingTop: 5,
                                paddingBottom: 0,
                              },
                            ]}
                            defaultValue={moPax.toFixed(0)}
                            onChangeText={(text) => {
                              // setPax(text.length > 0 ? parseInt(text) : 0)
                              setPax(isNaN(text) ? 0 : parseInt(text));
                              setEditPax(false)
                            }}
                            // onEndEditing={() => {
                            //   setEditPax(false)
                            // }}
                            autoFocus={editPax}
                            keyboardType={'decimal-pad'}
                            // placeholder="0"
                            underlineColorAndroid={Colors.fieldtBgColor}
                          />
                          :
                          <Text
                            style={[
                              {
                                fontSize: switchMerchant ? 10 : 25,
                                fontWeight: 'bold',
                                color: Colors.primaryColor,
                              },
                            ]}>
                            {moPax}
                          </Text>
                        }
                      </TouchableOpacity> */}

                  </View>
                  <TouchableOpacity
                    onPress={() => {
                      requestAnimationFrame(() => {
                        var moPaxParsed = !isNaN(parseInt(moPax)) ? parseInt(moPax) : 0;

                        if (moPaxParsed < selectedOutletTableMo.capacity)
                          // setPax(pax + 1);

                          CommonStore.update(s => {
                            s.moPax = (moPaxParsed + 1).toFixed(0);
                          });
                        else {
                          null
                        }
                      });
                    }}>
                    <View
                      style={[
                        styles.addBtn,
                        {
                          backgroundColor: Colors.primaryColor,
                          left: -1,
                        },
                        switchMerchant
                          ? {
                            width: windowWidth * 0.03,
                            height: windowWidth * 0.03,
                          }
                          : {},
                      ]}>
                      <Text
                        style={{
                          fontSize: switchMerchant ? windowWidth / 60 : 30,
                          fontWeight: '500',
                          color: Colors.whiteColor,
                        }}>
                        +
                      </Text>
                    </View>
                  </TouchableOpacity>
                </View>

              </View>
            </View>

            //</View>

            : null}

          {moMethod === 'otherD' ?
            <View stlye={{ flexDirection: 'column' }}>
              <View style={{ flexDirection: 'row', marginTop: 40, marginBottom: 5, alignItems: 'center', justifyContent: 'flex-start', paddingLeft: 20, zindex: 1 }}>
                <Text style={{
                  fontSize: switchMerchant ? windowWidth / 60 : 20,
                  fontFamily: 'NunitoSans-Bold',
                  color: Colors.blackColor,
                  marginRight: 10,
                }}>
                  Courier:
                </Text>

                {/* <DropDownPicker
                  globalTextStyle={{
                    fontSize: switchMerchant ? 10 : 14,
                  }}
                  containerStyle={{
                    height: switchMerchant ? 35 : 40,
                  }}
                  arrowColor={'black'}
                  arrowSize={switchMerchant ? 17 : 20}
                  arrowStyle={{ fontWeight: 'bold' }}
                  labelStyle={{
                    fontFamily: 'NunitoSans-Regular',
                    fontSize: switchMerchant ? 10 : 14,
                  }}
                  style={[{
                    width: windowWidth * 0.15,
                    paddingVertical: 0,
                    backgroundColor: Colors.fieldtBgColor,
                    borderRadius: 10,
                  }, switchMerchant ? {
                    marginRight: 5,
                  } : {}]}
                  placeholderStyle={{ color: Colors.fieldtTxtColor }}
                  placeholder={'Select'}
                  items={OTHER_DELIVERY_PARTNER_TYPES_DROPDOWN_LIST}
                  itemStyle={{
                    justifyContent: 'flex-start',
                    paddingHorizontal:
                      windowWidth * 0.0079,
                    zIndex: 100,
                  }}
                  onChangeItem={(item) => {
                    setOdpt(item.value);
                  }}
                  defaultValue={odpt}
                  dropDownStyle={{
                    width: windowWidth * 0.15,
                    height: 80,
                    backgroundColor: Colors.fieldtBgColor,
                    borderRadius: 10,
                    borderWidth: 1,
                    textAlign: 'left',
                    zIndex: 1000,
                  }}
                /> */}
                <View style={{
                  backgroundColor: Colors.fieldtBgColor,
                  borderWidth: 1,
                  borderColor: '#E5E5E5',
                  width: windowWidth * 0.15,
                  borderRadius: 5,
                  height: 40,
                  justifyContent: 'center',
                }}>
                  <RNPickerSelect
                    placeholder={{}}
                    useNativeAndroidPickerStyle={false}
                    style={{
                      inputIOS: {
                        fontSize: switchMerchant ? 10 : 14,
                        fontFamily: 'NunitoSans-Regular',
                        textAlign: 'center',
                      },
                      inputAndroid: {
                        fontSize: switchMerchant ? 10 : 14,
                        fontFamily: 'NunitoSans-Regular',
                        justifyContent: 'center',
                        textAlign: 'center',
                        height: 40,
                        color: 'black',
                      },
                      inputAndroidContainer: { width: '100%' },
                      //backgroundColor: 'red',
                      height: 35,

                      chevronContainer: {
                        display: 'none',
                      },
                      chevronDown: {
                        display: 'none',
                      },
                      chevronUp: {
                        display: 'none',
                      },
                    }}
                    items={OTHER_DELIVERY_PARTNER_TYPES_DROPDOWN_LIST}
                    value={odpt}
                    onValueChange={(value) => {
                      // setOdpt(value);

                      CommonStore.update((s) => {
                        s.odpt = value;
                      })
                    }}
                  />
                </View>
              </View>

              <View style={{ flexDirection: "row", alignItems: 'center', marginTop: 15, marginBottom: 5, justifyContent: 'flex-start', paddingLeft: 20, zindex: -1, }}>

                <Text style={{
                  fontSize: switchMerchant ? windowWidth / 60 : 20,
                  fontFamily: 'NunitoSans-Bold',
                  color: Colors.blackColor,
                  marginRight: 10,
                }}>
                  D. Order ID:
                </Text>

                <TextInput
                  style={{
                    backgroundColor: Colors.fieldtBgColor,
                    width: windowWidth * 0.15,
                    height: switchMerchant ? 35 : 40,
                    borderRadius: 5,
                    padding: 5,
                    marginVertical: 5,
                    borderWidth: 1,
                    borderColor: '#E5E5E5',
                    paddingLeft: 10,
                    marginLeft: 5,
                    fontFamily: 'NunitoSans-Regular',
                    fontSize: switchMerchant ? 10 : 14,
                  }}
                  placeholder="D. Order ID"
                  placeholderStyle={{
                    fontFamily: 'NunitoSans-Regular',
                    fontSize: switchMerchant ? 10 : 14,
                  }}
                  placeholderTextColor={Platform.select({
                    ios: '#a9a9a9',
                  })}
                  onChangeText={(text) => {
                    // setOdpoi(text);

                    CommonStore.update((s) => {
                      s.odpoi = text;
                    })
                  }}
                  defaultValue={odpoi}
                />
              </View>
            </View>
            : null}

          {/* <TouchableOpacity
              onPress={() => {
                setIsSelectinfo(false)
              }}>
              <View
                style={[
                  {
                    backgroundColor: Colors.primaryColor,
                    padding: 20,
                    paddingVertical: 12,
                    borderRadius: 10,
                    alignItems: 'center',

                    marginHorizontal: 48,
                    width: '50%',
                    marginTop: 20,
                    alignSelf: 'center',
                  },
                  switchMerchant
                    ? {
                      width: windowWidth * 0.2,
                      height: windowHeight * 0.1,
                      bottom: windowHeight * 0.02,
                      padding: 0,
                    }
                    : {},
                ]}>
                <Text
                  style={[
                    {
                      color: '#ffffff',
                      fontSize: switchMerchant ? 10 : 18,
                      // borderWidth: 1,
                      bottom: switchMerchant ? 2 : 0,
                      fontFamily: 'NunitoSans-SemiBold',
                    },
                    switchMerchant
                      ? {
                        height: '500%',
                        top: -5,
                      }
                      : {},
                  ]}>
                  CONFIRM
                </Text>
              </View>
            </TouchableOpacity> */}
        </View>
      </ScrollView >
      {/* <View style={{ flex: 1 }}>

        <View
          style={{
            // backgroundColor: 'red',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',

            height: '100%',
          }}>
          <View
            style={{
              marginBottom: Platform.OS === 'ios' ? '15%' : '10%',
            }}>
            <Text style={{
              fontFamily: 'NunitoSans-SemiBold',
              fontSize: switchMerchant ? 10 : 16,
            }}>No items in the cart now.</Text>            
          </View>
        </View>
      </View> */}
    </View>)
  ) : (
    // Checkpoint (JJ's comment)
    (<View
      style={[
        styles.container,
        !isTablet()
          ? {
            transform: [{ scaleX: 1 }, { scaleY: 1 }],
          }
          : {},
      ]}>
      <ScrollView
        style={[
          { flex: 1 },
          switchMerchant
            ? { marginBottom: windowHeight * 0.05 }
            : {},
        ]}
        scrollEnabled={false}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={() => {
              getCartItem();
              getPopular();
            }}
          />
        }
        contentContainerStyle={{
          // paddingBottom: switchMerchant ? (windowHeight * 0.05) : (windowHeight * 0.1),
        }}>

        {/* <ModalView
          style={{ flex: 1 }}
          visible={visible}
          transparent={true}
          animationType="slide">
          <View
            style={{
              backgroundColor: 'rgba(0,0,0,0.5)',
              flex: 1,
              justifyContent: 'center',
              alignItems: 'center',
            }}>
            <View style={styles.confirmBox}>
              <TouchableOpacity
                onPress={() => {
                  setState({ visible: false });
                  setState({ visible1: false });
                }}>
                <View
                  style={{
                    alignSelf: 'flex-end',
                    padding: 16,
                  }}>
                  <Close name="closecircle" size={28} />
                </View>
              </TouchableOpacity>
              <View>
                <Text
                  style={{
                    textAlign: 'center',
                    fontWeight: 'bold',
                    fontSize: 16,
                    marginBottom: 5,
                  }}>
                  Do you want to delete all?
              </Text>
              </View>
              <View
                style={{
                  justifyContent: 'center',
                  alignItems: 'center',
                  width: '100%',
                  alignContent: 'center',
                  marginBottom: '6%',
                  height: '50%',
                }}>
                <View
                  style={{
                    alignItems: 'center',
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                  }}>
                  <TouchableOpacity
                    onPress={() => {
                      Cart.deleteCartItem(editingItemId);
                      getCartItem();
                      setState({ visible1: false });
                      setState({ visible: false });
                    }}
                    style={{
                      backgroundColor: Colors.fieldtBgColor,
                      width: '30%',
                      justifyContent: 'center',
                      alignItems: 'center',
                      alignContent: 'center',
                      borderRadius: 5,
                      height: '75%',
                    }}>
                    <Text style={{ fontSize: 15, color: Colors.primaryColor }}>
                      Yes
                  </Text>
                  </TouchableOpacity>
                  <View style={{ marginLeft: '3%', marginRight: '3%' }}></View>
                  <TouchableOpacity
                    onPress={() => {
                      setState({ visible1: true });
                    }}
                    style={{
                      backgroundColor: Colors.fieldtBgColor,
                      width: '30%',
                      justifyContent: 'center',
                      alignItems: 'center',
                      alignContent: 'center',
                      borderRadius: 5,
                      height: '75%',
                    }}>
                    <Text style={{ fontSize: 15, color: Colors.primaryColor }}>
                      No
                  </Text>
                  </TouchableOpacity>
                  
                </View>
              </View>
            </View>
          </View>
        </ModalView> */}
        <ModalView
          supportedOrientations={['landscape', 'portrait']}
          style={{ flex: 1 }}
          visible={showSuccess}
          transparent>
          <View style={{ width: windowWidth * Styles.sideBarWidth, }} />
          <View style={{
            alignItems: 'center',
            justifyContent: 'center',
          }}>
            <Image
              style={{
                //marginLeft: windowWidth  * 0.16, 
                marginTop: windowHeight * 0.08,
                width: windowWidth,
                height: windowHeight * 0.84
              }}
              source={require('../assets/image/Success.gif')}
              fadeDuration={400}
            //resizeMode='contain'
            // onLoad={() => {
            //   placeUserOrder();
            // }}
            />
          </View>
        </ModalView>

        <ModalView
          supportedOrientations={['landscape', 'portrait']}
          style={{ flex: 1 }}
          visible={visible}
          transparent
          animationType="slide">
          <View
            style={{
              backgroundColor: 'rgba(0,0,0,0.5)',
              flex: 1,
              justifyContent: 'center',
              alignItems: 'center',
            }}>
            <View style={styles.confirmBox}>
              <TouchableOpacity
                onPress={() => {
                  setState({ visible1: false });
                  setState({ visible: false });
                }}>
                <View
                  style={{
                    alignSelf: 'flex-end',
                    padding: 13,
                  }}>
                  {/* <Close name="closecircle" size={25} /> */}
                  <AntDesign
                    name="closecircle"
                    size={25}
                    color={Colors.fieldtTxtColor}
                  />
                </View>
              </TouchableOpacity>
              <View>
                <Text
                  style={{
                    textAlign: 'center',
                    fontWeight: 'bold',
                    fontSize: switchMerchant ? 10 : 16,
                    marginBottom: 10,
                  }}>
                  Delete
                </Text>
              </View>
              <View
                style={{
                  justifyContent: 'center',
                  alignItems: 'center',
                  width: '100%',
                  alignContent: 'center',
                  marginBottom: '10%',
                  height: '50%',
                }}>
                <View
                  style={{
                    alignItems: 'center',
                    marginBottom: '2%',
                  }}>
                  {/* <NumericInput
                    value={value}
                    onChange={(value) => setState({ value })}
                    minValue={1}
                    maxValue={qty}
                    totalWidth={200}
                    totalHeight={40}
                    iconSize={25}
                    step={1}
                    valueType="real"
                    rounded
                    textColor={Colors.primaryColor}
                    iconStyle={{ color: 'white' }}
                    rightButtonBackgroundColor={Colors.primaryColor}
                    leftButtonBackgroundColor={'grey'}
                  /> */}
                </View>
                <View
                  style={{
                    alignItems: 'center',
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                  }}>
                  <TouchableOpacity
                    onPress={() => {
                      onChangeQty(value, editingItemId);
                      getCartItem();
                      setState({ visible1: false });
                      setState({ visible: false });
                      setState({ value: '' });
                    }}
                    style={{
                      backgroundColor: Colors.primaryColor,
                      width: '30%',
                      justifyContent: 'center',
                      alignItems: 'center',
                      alignContent: 'center',
                      borderRadius: 5,
                      height: '75%',
                      marginTop: 10,
                    }}>
                    <Text
                      style={{
                        fontSize: switchMerchant ? 10 : 15,
                        color: Colors.whiteColor,
                      }}>
                      Update
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </View>
        </ModalView>

        <ModalView
          supportedOrientations={['landscape', 'portrait']}
          style={{ flex: 1 }}
          visible={showCustomerList}
          transparent>
          <View style={[styles.modalContainer, {

          }]}>
            <View style={[styles.modalView, {
              height: windowHeight * 0.8,
              width: Dimensions.get('window').width * 0.4,
              padding: Dimensions.get('window').width * 0.03,
              paddingHorizontal: Dimensions.get('window').width * 0.015,

              ...getTransformForModalInsideNavigation(),
            }]}>
              <TouchableOpacity
                disabled={isLoading}
                style={[styles.closeButton, {
                  right: Dimensions.get('window').width * 0.02,
                  top: Dimensions.get('window').width * 0.02,
                }]}
                onPress={() => {

                  requestAnimationFrame(() => {
                    TableStore.update(s => {
                      s.showCustomerList = false;
                      s.selectedRegisteredCRMUserId = '';

                      s.customerName = '';
                      s.customerPhone = '+60';
                      s.customerEmail = '';
                      s.customerUniqueId = '';
                      s.isCustomer = false;
                    });

                    CommonStore.update(s => {
                      s.selectedCustomerEdit = null;
                    });
                  });
                }}>
                {switchMerchant ? (
                  <AIcon
                    name="closecircle"
                    size={20}
                    color={Colors.fieldtTxtColor}
                  />
                ) : (
                  <AIcon
                    name="closecircle"
                    size={40}
                    color={Colors.fieldtTxtColor}
                  />
                )}
              </TouchableOpacity>
              <View style={styles.modalTitle}>
                <Text
                  style={[
                    styles.modalTitleText,
                    switchMerchant
                      ? {
                        fontSize: 16,
                      }
                      : {},
                  ]}>
                  Select Member
                </Text>
              </View>
              <View
                style={{ ...styles.modalBody, justifyContent: 'space-around' }}>
                <View
                  style={[{
                    flexDirection: 'row',
                    width: '100%',
                    height: 40,
                    borderBottomWidth: 1,
                    borderColor: Colors.fieldtBgColor2,
                    paddingHorizontal: switchMerchant ? 8 : 10,
                    marginBottom: 5,
                  }, switchMerchant ? {
                    alignItems: 'center',
                  } : {}]}>
                  <View style={{ flexDirection: "row", alignItems: 'center', }}>
                    <Feather
                      name="search"
                      size={switchMerchant ? 13 : 18}
                      color={Colors.primaryColor}
                      style={{ paddingTop: 0 }}
                    />
                    <TextInput

                      underlineColorAndroid={Colors.whiteColor}
                      style={
                        switchMerchant
                          ? {
                            width: 180,
                            fontSize: 10,

                            fontFamily: 'NunitoSans-Regular',
                            height: '100%',
                            height: 40,

                            marginLeft: 5,
                            marginTop: 11,
                          }
                          : {
                            width: 220,
                            fontSize: 15,
                            fontFamily: 'NunitoSans-Regular',
                            paddingLeft: 5,
                            height: 40,
                          }
                      }
                      clearButtonMode="while-editing"
                      placeholder="Name Phone"
                      placeholderTextColor={Platform.select({
                        ios: '#a9a9a9',
                      })}
                      onChangeText={(text) => {

                        TableStore.update(s => {
                          s.searchCustomer = text;
                        });
                      }}
                      value={searchCustomer}
                    />
                    <TouchableOpacity
                      disabled={isLoading}
                      style={{ paddingTop: 0 }}
                      onPress={() => {

                        requestAnimationFrame(() => {
                          TableStore.update(s => {
                            s.searchCustomer = '';
                          });
                        });
                      }}>
                      {switchMerchant ? (
                        <AIcon
                          name="closecircle"
                          size={15}
                          color={Colors.fieldtTxtColor}
                        />
                      ) : (
                        <AIcon
                          name="closecircle"
                          size={25}
                          color={Colors.fieldtTxtColor}
                        />
                      )}
                    </TouchableOpacity>
                  </View>
                  <TouchableOpacity
                    disabled={isLoading}
                    style={{
                      justifyContent: 'center',
                      flexDirection: 'row',
                      borderWidth: 1,
                      borderColor: Colors.primaryColor,
                      backgroundColor: '#4E9F7D',
                      borderRadius: 5,

                      paddingHorizontal: 10,
                      height: switchMerchant ? 35 : 40,
                      alignItems: 'center',
                      shadowOffset: {
                        width: 0,
                        height: 2,
                      },
                      shadowOpacity: 0.22,
                      shadowRadius: 3.22,
                      elevation: 1,
                      zIndex: -1,

                      marginLeft: 15,

                    }}
                    onPress={() => {
                      CommonStore.update((s) => {
                        s.isLoading = true;
                      });

                      if (searchList.length > 0) {
                        setSearchList([]);

                        CommonStore.update((s) => {
                          s.isLoading = false;
                        });
                      }
                      else {
                        searchUsers(searchCustomer);
                      }
                    }}>
                    {isLoading ?
                      <View style={{ justifyContent: 'center', alignItems: 'center' }}>
                        <ActivityIndicator size='small' color={Colors.whiteColor} />
                      </View>
                      :
                      <Text
                        style={{
                          marginLeft: 5,
                          color: Colors.primaryColor,
                          fontSize: switchMerchant ? 10 : 16,
                          color: '#FFFFFF',
                          fontFamily: 'NunitoSans-Bold',
                        }}>
                        {searchList.length > 0 ? 'CLEAR' : 'SEARCH'}
                      </Text>
                    }
                  </TouchableOpacity>
                </View>
                <View
                  style={{
                    flex: 1,
                    marginVertical: 5,
                    width: windowWidth * 0.3,
                  }}>
                  {!isLoading ?
                    <>
                      {searchList.length > 0 ?
                        <FlatList

                          data={registeredCRMUsersNoLimit}
                          renderItem={renderCrmUser}
                          keyExtractor={(item, index) => String(index)}
                          showsVerticalScrollIndicator={false}
                        />
                        :
                        <FlatList

                          data={registeredCRMUsersDropdownList

                          }
                          renderItem={renderCrmUser}
                          keyExtractor={(item, index) => String(index)}
                          showsVerticalScrollIndicator={false}
                        />
                      }
                    </>
                    :
                    <View style={{ justifyContent: 'center', alignItems: 'center', marginTop: windowHeight * 0.3 }}>
                      <ActivityIndicator size='large' color={Colors.primaryColor} />
                    </View>
                  }
                </View>
              </View>
              <View style={{ flexDirection: 'row' }}>
                <TouchableOpacity
                  style={[
                    styles.modalSaveButton,
                    switchMerchant
                      ? {
                        fontSize: 12,
                      }
                      : { width: Dimensions.get('window').width * 0.1, backgroundColor: Colors.tabCyan },
                  ]}
                  onPress={() => {

                    TableStore.update(s => {
                      s.customerName = '';
                      s.customerPhone = '+60';
                      s.customerEmail = '';
                      s.showAddCustomer = true;
                    });
                    setCustomerIDNum('');
                    setCustomerTIN('');
                    setCustomerIdType('NRIC');
                    setCustomerEINStreet('');
                    setCustomerEINCity('');
                    setCustomerEINState('sgr');
                    setCustomerEINPostcode('');
                    setIsEdit(false);
                    setEInvoiceInfo(false);
                  }}>
                  <Text
                    style={[
                      styles.modalDescText,
                      { color: Colors.whiteColor },
                      switchMerchant ? {
                        fontSize: 12,
                      } : {},
                    ]}>
                    REGISTER
                  </Text>
                </TouchableOpacity>
                <View style={{ width: 10 }} />
                <TouchableOpacity
                  style={[
                    styles.modalSaveButton,
                    switchMerchant
                      ? {
                        fontSize: 12,
                      }
                      : { width: Dimensions.get('window').width * 0.1, backgroundColor: Colors.secondaryColor },
                  ]}
                  onPress={() => {
                    var crmUser = crmUsers.find(
                      (user) =>
                        user.userId === selectedRegisteredCRMUserId ||
                        user.email === selectedRegisteredCRMUserId,
                    );
                    if (crmUser) {
                      TableStore.update(s => {
                        s.customerName = crmUser.name;
                        s.customerPhone = crmUser.number;
                        s.customerEmail = crmUser.emailSecond ? crmUser.emailSecond : '';
                        s.showAddCustomer = true;
                      });

                      // CommonStore.update(s => {
                      //   s.selectedCustomerEdit = crmUser;
                      // });

                      setCustomerIDNum(crmUser.eiId ? crmUser.eiId : '');
                      setCustomerTIN(crmUser.tin ? crmUser.tin : '');
                      setCustomerIdType(crmUser.eiIdType ? crmUser.eiIdType : 'NRIC');
                      setCustomerEINStreet(crmUser.epAddr1To ? crmUser.epAddr1To : '');
                      setCustomerEINCity(crmUser.epCityTo ? crmUser.epCityTo : '');
                      setCustomerEINState(crmUser.epStateTo ? crmUser.epStateTo : 'sgr');
                      setCustomerEINPostcode(crmUser.epCodeTo ? crmUser.epCodeTo : '');
                      setIsEdit(true);
                      if (crmUser.tin && crmUser.tin !== '') {
                        setEInvoiceInfo(true);
                      }
                      else {
                        setEInvoiceInfo(false);
                      }
                    } else {
                      Alert.alert('Info', 'Please select a member');
                    }
                  }}>
                  <Text
                    style={[
                      styles.modalDescText,
                      { color: Colors.whiteColor },
                      switchMerchant ? {
                        fontSize: 12,
                      } : {},
                    ]}>
                    EDIT
                  </Text>
                </TouchableOpacity>
                <View style={{ width: 10 }} />
                <TouchableOpacity
                  style={[
                    styles.modalSaveButton,
                    switchMerchant
                      ? {
                        fontSize: 12,
                      }
                      : { width: Dimensions.get('window').width * 0.1, backgroundColor: Colors.primaryColor },
                  ]}
                  onPress={() => {
                    if (isCustomer) {
                      Alert.alert(
                        'Success',
                        'Customer has been added',
                      );

                      var crmUser = crmUsers.find(
                        (user) =>
                          user.userId === selectedRegisteredCRMUserId ||
                          user.email === selectedRegisteredCRMUserId,
                      );
                      if (crmUser) {
                        CommonStore.update(s => {
                          s.selectedCustomerEdit = crmUser;
                        });

                        TableStore.update(s => {
                          s.customerName = crmUser.name;
                          s.customerPhone = crmUser.number;
                          s.customerEmail = crmUser.emailSecond ? crmUser.emailSecond : '';
                          s.customerUniqueId = crmUser.uniqueId;
                        });
                      }

                      TableStore.update(s => {
                        s.showCustomerList = false;
                        s.selectedRegisteredCRMUserId = '';
                        s.isCustomer = false;
                      });

                    } else {
                      Alert.alert(
                        'Failed',
                        'Please select one customer to proceed',
                      );
                    }
                  }}>
                  <Text
                    style={[
                      styles.modalDescText,
                      { color: Colors.whiteColor },
                      switchMerchant ? {
                        fontSize: 12,
                      } : {},
                    ]}>
                    DONE
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </ModalView>

        <ModalView supportedOrientations={['landscape', 'portrait']} style={{ flex: 1 }} visible={showAddCustomer} transparent>
          <View style={[styles.modalContainer, {

          }]}>
            <View
              style={[
                styles.modalView,
                {
                  height: windowHeight * 0.9,
                  width: windowWidth * 0.75,

                  padding: Dimensions.get('window').width * 0.03,
                  paddingHorizontal: Dimensions.get('window').width * 0.015,

                  ...getTransformForModalInsideNavigation(),
                },
              ]}>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => {

                  requestAnimationFrame(() => {
                    TableStore.update(s => {
                      s.showAddCustomer = false;
                    });
                  });
                }}>
                {switchMerchant ? (
                  <AIcon
                    name="closecircle"
                    size={20}
                    color={Colors.fieldtTxtColor}
                  />
                ) : (
                  <AIcon
                    name="closecircle"
                    size={40}
                    color={Colors.fieldtTxtColor}
                  />
                )}
              </TouchableOpacity>
              <View style={styles.modalTitle}>
                <Text style={styles.modalTitleText}>{isEdit ? 'Edit Member' : 'Add New Member'}</Text>
              </View>

              <View
                style={{ ...styles.modalBody, justifyContent: 'space-around' }}>
                <View
                  style={{
                    flexDirection: 'row',
                    flex: 1,
                    paddingHorizontal: switchMerchant ? 8 : 15,

                  }}>

                  <View
                    style={{ ...styles.modalBody, justifyContent: 'space-around' }}>
                    <View
                      style={{
                        flexDirection: 'column',
                        flex: 1,
                        paddingHorizontal: switchMerchant ? 8 : 15,

                      }} />
                    <View
                      style={{
                        flexDirection: 'row',
                        width: '90%',
                        alignItems: 'center',
                        justifyContent: 'flex-start',

                      }}>
                      <Text
                        style={{
                          fontSize: switchMerchant ? 10 : 20,

                          fontFamily: 'Nunitosans-Bold',
                          width: '40%',
                        }}>
                        Name *
                      </Text>
                      <TextInput

                        style={{

                          width: switchMerchant ? 100 : 160,
                          height: switchMerchant ? 30 : 35,
                          borderRadius: 5,
                          borderBottomWidth: 1,
                          padding: 5,
                          marginVertical: 5,

                          borderColor: '#E5E5E5',
                          paddingLeft: 10,
                          marginLeft: 5,
                          fontFamily: 'NunitoSans-Regular',
                          fontSize: switchMerchant ? 10 : 16,
                        }}
                        placeholder="Johnson"
                        placeholderStyle={{
                          fontFamily: 'NunitoSans-Regular',
                          fontSize: switchMerchant ? 10 : 14,
                        }}
                        placeholderTextColor={Platform.select({
                          ios: '#a9a9a9',
                        })}
                        onChangeText={(text) => {

                          TableStore.update(s => {
                            s.customerName = text;
                          });
                        }}
                        defaultValue={customerName}

                      />
                    </View>
                    <TouchableOpacity
                      onPress={() => {
                        setEInvoiceInfo(!eInvoiceInfo);
                      }}
                      style={{
                        flexDirection: 'row',
                        width: '90%',
                        alignItems: 'center',
                        justifyContent: 'flex-start',
                        left: -5,
                        marginVertical: 10,
                      }}>
                      <CheckBox
                        style={{
                          ...(Platform.OS === 'ios' && {
                            width: !isTablet() ? 10 : 20,
                            height: !isTablet() ? 10 : 20,
                            marginRight: 10,
                          }),
                        }}
                        value={eInvoiceInfo}
                        onValueChange={(value) => {
                          setEInvoiceInfo(!eInvoiceInfo);
                        }}
                      />
                      <Text
                        style={[
                          styles.modalBodyText,
                          { fontSize: 20, fontFamily: 'NunitoSans-Bold', width: '100%' },
                        ]}
                      >
                        Register for e-invoice
                      </Text>
                    </TouchableOpacity>
                    {eInvoiceInfo ?
                      <>
                        {/* email */}
                        <View
                          style={{
                            flexDirection: 'row',
                            width: '90%',
                            alignItems: 'center',
                            justifyContent: 'flex-start',

                          }}>
                          <Text
                            style={{
                              fontSize: switchMerchant ? 10 : 20,

                              fontFamily: 'Nunitosans-Bold',
                              width: '40%',
                            }}>
                            Email
                          </Text>
                          <TextInput

                            style={{

                              width: switchMerchant ? 100 : 160,
                              height: switchMerchant ? 30 : 35,
                              borderRadius: 5,
                              borderBottomWidth: 1,
                              padding: 5,
                              marginVertical: 5,

                              borderColor: '#E5E5E5',
                              paddingLeft: 10,
                              marginLeft: 5,
                              fontFamily: 'NunitoSans-Regular',
                              fontSize: switchMerchant ? 10 : 16,
                            }}
                            placeholder="<EMAIL>"
                            placeholderStyle={{
                              fontFamily: 'NunitoSans-Regular',
                              fontSize: switchMerchant ? 10 : 14,
                            }}
                            placeholderTextColor={Platform.select({
                              ios: '#a9a9a9',
                            })}
                            onChangeText={(text) => {
                              TableStore.update(s => {
                                s.customerEmail = text;
                              });
                            }}
                            defaultValue={customerEmail}

                          />
                        </View>

                        {/* ID Type */}
                        <View style={{
                          flexDirection: 'row',
                          width: '90%',
                          alignItems: 'center',
                          justifyContent: 'flex-start',

                        }}>
                          {/* ID Type */}

                          <Text
                            style={{
                              fontSize: switchMerchant ? 10 : 20,

                              fontFamily: 'Nunitosans-Bold',
                              width: '40%',
                            }}>
                            ID Type
                          </Text>
                          <RNPickerSelect
                            style={{
                              inputAndroidContainer: {
                                height: 35,
                                justifyContent: 'center',
                                backgroundColor: '#fafafa',
                                borderRadius: 4,
                                shadowColor: '#000',
                                shadowOffset: {
                                  width: 0,
                                  height: 2,
                                },
                                shadowOpacity: 0.22,
                                shadowRadius: 3.22,
                                elevation: 1,
                              },
                              inputAndroid: {

                                color: 'black',
                                fontFamily: 'NunitoSans-Regular',
                                fontSize: 16,
                                borderWidth: 1,
                                borderColor: Colors.primaryColor,
                                borderRadius: 5,
                                width: '100%',
                                paddingHorizontal: 10,
                                height: 40,
                                paddingLeft: 12,
                                textAlign: 'center',
                              },
                              inputIOS: {

                                color: 'black',
                                fontFamily: 'NunitoSans-Regular',
                                fontSize: 16,
                                borderWidth: 1,
                                borderColor: Colors.primaryColor,
                                borderRadius: 5,
                                width: '100%',
                                paddingHorizontal: 10,
                                height: 40,
                                paddingLeft: 12,
                                textAlign: 'center',
                              },
                              viewContainer: {
                                backgroundColor: Colors.fieldtBgColor,
                                borderRadius: 4,
                                height: 40,
                                width: switchMerchant ? 100 : 160,
                                justifyContent: 'center',
                                fontSize: switchMerchant ? 10 : 14,
                                fontFamily: 'NunitoSans-Regular',
                                paddingLeft: 5,
                                marginLeft: 10,
                                shadowColor: '#000',
                                shadowOffset: {
                                  width: 0,
                                  height: 2,
                                },
                                shadowOpacity: 0.22,
                                shadowRadius: 3.22,
                                elevation: 1,
                              },
                            }}
                            onValueChange={(value) => setCustomerIdType(value)}
                            items={[
                              { label: 'NRIC', value: 'NRIC' },
                              { label: 'Passport', value: 'Passport' },
                              { label: 'MyTentera', value: 'MyTentera' },
                            ]}
                            placeholder={{
                              label: 'Select an ID Type',
                              value: null,
                              color: '#000000',
                            }}
                            value={customerIdType}

                          />
                        </View>

                        {/* ID */}
                        <View
                          style={{
                            flexDirection: 'row',
                            width: '90%',
                            alignItems: 'center',
                            justifyContent: 'flex-start',

                          }}>
                          <Text
                            style={{
                              fontSize: switchMerchant ? 10 : 20,

                              fontFamily: 'Nunitosans-Bold',
                              width: '40%',
                            }}>
                            ID
                          </Text>
                          <TextInput
                            style={{

                              width: switchMerchant ? 100 : 160,
                              height: switchMerchant ? 30 : 35,
                              borderRadius: 5,
                              borderBottomWidth: 1,
                              padding: 5,
                              marginVertical: 5,

                              borderColor: '#E5E5E5',
                              paddingLeft: 10,
                              marginLeft: 5,
                              fontFamily: 'NunitoSans-Regular',
                              fontSize: switchMerchant ? 10 : 16,
                            }}
                            placeholder={
                              customerIdType === 'NRIC' || customerIdType === 'MyTentera'
                                ? '010101020303'
                                : customerIdType === 'Passport'
                                  ? 'A12345678'
                                  : 'Enter an ID'
                            }
                            placeholderStyle={{
                              fontFamily: 'NunitoSans-Regular',
                              fontSize: switchMerchant ? 10 : 14,
                            }}
                            placeholderTextColor={Platform.select({
                              ios: '#a9a9a9',
                            })}
                            onChangeText={(text) => {
                              setCustomerIDNum(text);
                            }}
                            defaultValue={customerIDNum}

                          />
                        </View>

                        {/* tin */}
                        <View
                          style={{
                            flexDirection: 'row',
                            width: '90%',
                            alignItems: 'center',
                            justifyContent: 'flex-start',

                          }}>
                          <Text
                            style={{
                              fontSize: switchMerchant ? 10 : 20,

                              fontFamily: 'Nunitosans-Bold',
                              width: '40%',
                            }}>
                            TIN
                          </Text>
                          <TextInput
                            style={{

                              width: switchMerchant ? 100 : 160,
                              height: switchMerchant ? 30 : 35,
                              borderRadius: 5,
                              borderBottomWidth: 1,
                              padding: 5,
                              marginVertical: 5,

                              borderColor: '#E5E5E5',
                              paddingLeft: 10,
                              marginLeft: 5,
                              fontFamily: 'NunitoSans-Regular',
                              fontSize: switchMerchant ? 10 : 16,
                            }}
                            placeholder="AB12345678901"
                            placeholderStyle={{
                              fontFamily: 'NunitoSans-Regular',
                              fontSize: switchMerchant ? 10 : 14,
                            }}
                            placeholderTextColor={Platform.select({
                              ios: '#a9a9a9',
                            })}
                            onChangeText={(text) => {
                              setCustomerTIN(text);
                            }}
                            defaultValue={customerTIN}

                          />
                        </View>

                        {/* Street */}
                        <View
                          style={{
                            flexDirection: 'row',
                            width: '90%',
                            alignItems: 'center',
                            justifyContent: 'flex-start',

                          }}>
                          <Text
                            style={{
                              fontSize: switchMerchant ? 10 : 20,

                              fontFamily: 'Nunitosans-Bold',
                              width: '40%',
                            }}>
                            Street
                          </Text>
                          <TextInput
                            style={{

                              width: switchMerchant ? 100 : 160,
                              height: switchMerchant ? 30 : 35,
                              borderRadius: 5,
                              borderBottomWidth: 1,
                              padding: 5,
                              marginVertical: 5,

                              borderColor: '#E5E5E5',
                              paddingLeft: 10,
                              marginLeft: 5,
                              fontFamily: 'NunitoSans-Regular',
                              fontSize: switchMerchant ? 10 : 16,
                              zIndex: -2,
                            }}
                            placeholder="Lot 1210, Jalan Lapangan Terbang"
                            placeholderStyle={{
                              fontFamily: 'NunitoSans-Regular',
                              fontSize: switchMerchant ? 10 : 14,
                            }}
                            placeholderTextColor={Platform.select({
                              ios: '#a9a9a9',
                            })}
                            onChangeText={(text) => {
                              setCustomerEINStreet(text);
                            }}
                            defaultValue={customerEINStreet}

                          />
                        </View>

                        {/* Postcode */}
                        <View
                          style={{
                            flexDirection: 'row',
                            width: '90%',
                            alignItems: 'center',
                            justifyContent: 'flex-start',
                          }}>
                          <Text
                            style={{
                              fontSize: switchMerchant ? 10 : 20,

                              fontFamily: 'Nunitosans-Bold',
                              width: '40%',
                            }}>
                            Postcode
                          </Text>
                          <TextInput
                            style={{

                              width: switchMerchant ? 100 : 160,
                              height: switchMerchant ? 30 : 35,
                              borderRadius: 5,
                              borderBottomWidth: 1,
                              padding: 5,
                              marginVertical: 5,

                              borderColor: '#E5E5E5',
                              paddingLeft: 10,
                              marginLeft: 5,
                              fontFamily: 'NunitoSans-Regular',
                              fontSize: switchMerchant ? 10 : 16,
                            }}
                            placeholder="40150"
                            placeholderStyle={{
                              fontFamily: 'NunitoSans-Regular',
                              fontSize: switchMerchant ? 10 : 14,
                            }}
                            placeholderTextColor={Platform.select({
                              ios: '#a9a9a9',
                            })}
                            onChangeText={(text) => {
                              setCustomerEINPostcode(text);
                            }}
                            defaultValue={customerEINPostcode}

                          />
                        </View>

                        {/* city */}
                        <View
                          style={{
                            flexDirection: 'row',
                            width: '90%',
                            alignItems: 'center',
                            justifyContent: 'flex-start',
                          }}>
                          <Text
                            style={{
                              fontSize: switchMerchant ? 10 : 20,

                              fontFamily: 'Nunitosans-Bold',
                              width: '40%',
                            }}>
                            City
                          </Text>
                          <TextInput
                            style={{

                              width: switchMerchant ? 100 : 160,
                              height: switchMerchant ? 30 : 35,
                              borderRadius: 5,
                              borderBottomWidth: 1,
                              padding: 5,
                              marginVertical: 5,

                              borderColor: '#E5E5E5',
                              paddingLeft: 10,
                              marginLeft: 5,
                              fontFamily: 'NunitoSans-Regular',
                              fontSize: switchMerchant ? 10 : 16,
                            }}
                            placeholder="Shah Alam"
                            placeholderStyle={{
                              fontFamily: 'NunitoSans-Regular',
                              fontSize: switchMerchant ? 10 : 14,
                            }}
                            placeholderTextColor={Platform.select({
                              ios: '#a9a9a9',
                            })}
                            onChangeText={(text) => {
                              setCustomerEINCity(text);
                            }}
                            defaultValue={customerEINCity}

                          />
                        </View>

                        {/* State */}
                        <View style={{
                          flexDirection: 'row',
                          width: '90%',
                          alignItems: 'center',
                          justifyContent: 'flex-start',

                        }}>
                          <Text
                            style={{
                              fontSize: switchMerchant ? 10 : 20,

                              fontFamily: 'Nunitosans-Bold',
                              marginBottom: 20,
                              width: '40%',
                            }}>
                            State
                          </Text>
                          <RNPickerSelect
                            style={{
                              inputAndroidContainer: {
                                height: 35,
                                justifyContent: 'center',
                                backgroundColor: '#fafafa',
                                borderRadius: 4,
                                shadowColor: '#000',
                                shadowOffset: {
                                  width: 0,
                                  height: 2,
                                },
                                shadowOpacity: 0.22,
                                shadowRadius: 3.22,
                                elevation: 1,
                              },
                              inputAndroid: {

                                color: 'black',
                                fontFamily: 'NunitoSans-Regular',
                                fontSize: 16,
                                borderWidth: 1,
                                borderColor: Colors.primaryColor,
                                borderRadius: 5,
                                width: '100%',
                                paddingHorizontal: 10,
                                height: 40,
                                paddingLeft: 12,
                                textAlign: 'center',
                              },
                              inputIOS: {

                                color: 'black',
                                fontFamily: 'NunitoSans-Regular',
                                fontSize: 16,
                                borderWidth: 1,
                                borderColor: Colors.primaryColor,
                                borderRadius: 5,
                                width: '100%',
                                paddingHorizontal: 10,
                                height: 40,
                                paddingLeft: 12,
                                textAlign: 'center',
                              },
                              viewContainer: {
                                backgroundColor: Colors.fieldtBgColor,
                                borderRadius: 4,
                                height: 40,
                                width: switchMerchant ? 100 : 160,
                                justifyContent: 'center',
                                fontSize: switchMerchant ? 10 : 14,
                                fontFamily: 'NunitoSans-Regular',
                                paddingLeft: 5,
                                marginLeft: 10,
                                marginBottom: 20,
                                shadowColor: '#000',
                                shadowOffset: {
                                  width: 0,
                                  height: 2,
                                },
                                shadowOpacity: 0.22,
                                shadowRadius: 3.22,
                                elevation: 1,
                              },
                            }}
                            onValueChange={(value) => setCustomerEINState(value)}
                            items={[
                              { label: 'Johor', value: 'jhr' },
                              { label: 'Kedah', value: 'kdh' },
                              { label: 'Kelantan', value: 'ktn' },
                              { label: 'Melaka', value: 'mlk' },
                              { label: 'Negeri Sembilan', value: 'nsn' },
                              { label: 'Pahang', value: 'phg' },
                              { label: 'Perak', value: 'prk' },
                              { label: 'Perlis', value: 'pls' },
                              { label: 'Pulau Pinang', value: 'png' },
                              { label: 'Selangor', value: 'sgr' },
                              { label: 'Terengganu', value: 'trg' },
                              { label: 'Kuala Lumpur', value: 'kul' },
                              { label: 'Putra Jaya', value: 'pjy' },
                              { label: 'Sarawak', value: 'srw' },
                              { label: 'Sabah', value: 'sbh' },
                              { label: 'Labuan', value: 'lbn' },
                            ]}
                            placeholder={{
                              label: 'Select a State',
                              value: null,
                              color: '#000000',
                            }}
                            value={customerEINState}

                          />
                        </View>
                      </>
                      : <View style={{ height: windowHeight * 0.35 }} />}
                  </View>

                  <View
                    style={{
                      width:
                        Platform.OS === 'ios'
                          ? windowWidth * 0.42
                          : windowWidth * 0.43,
                      backgroundColor: Colors.whiteColor,
                      borderRadius: 5,
                    }}>
                    <View
                      style={{
                        alignItems: 'center',
                        justifyContent: 'center',
                        flex: 1,
                        paddingTop: Platform.OS === 'ios' ? 0 : 10,
                        marginTop: Platform.OS === 'ios' ? 10 : 0,
                      }}>
                      <Text
                        style={{
                          fontSize: switchMerchant ? 15 : 25,
                          fontFamily: 'NunitoSans-Bold',
                        }}>
                        {isEdit ? 'Your Phone Number' : 'Enter Your Phone Number'}
                      </Text>
                    </View>
                    <View
                      style={{
                        justifyContent: 'center',
                        alignItems: 'center',
                        flex: 1,
                      }}>
                      <Text style={{ fontSize: switchMerchant ? 22 : 45, fontFamily: 'NunitoSans-Bold' }}>
                        {customerPhone}
                      </Text>
                    </View>
                    <View>
                      <View
                        style={{
                          flexDirection: 'row',
                          flexWrap: 'wrap',
                          justifyContent: 'space-between',
                          alignItems: 'center',
                          alignSelf: 'center',
                          width: Platform.OS === 'ios' ? 270 : '45%',
                          paddingTop: switchMerchant ? 10 : 30,
                        }}>
                        <View
                          style={{
                            flexDirection: 'row',
                            justifyContent: 'space-between',
                            alignSelf: 'center',
                            alignItems: 'center',
                            width: '100%',
                          }}>
                          <TouchableOpacity
                            disabled={isEdit}
                            onPress={() => {
                              PhoneonNumPadBtn(1);
                            }}>
                            <View
                              style={[
                                styles.pinBtn,
                                switchMerchant
                                  ? {
                                    width: switchMerchant ? 35 : 70,
                                    height: switchMerchant ? 35 : 70,
                                    marginBottom: 7,
                                  }
                                  : { width: 70, height: 70, backgroundColor: isEdit ? Colors.fieldtBgColor : Colors.lightPrimary },
                              ]}>
                              <Text
                                style={[
                                  styles.pinNo,
                                  { fontSize: switchMerchant ? 10 : 20, color: isEdit ? Colors.descriptionColor : 'black' },
                                ]}>
                                1
                              </Text>
                            </View>
                          </TouchableOpacity>

                          <TouchableOpacity
                            disabled={isEdit}
                            onPress={() => {
                              PhoneonNumPadBtn(2);
                            }}>
                            <View
                              style={[
                                styles.pinBtn,
                                switchMerchant
                                  ? {
                                    width: switchMerchant ? 35 : 70,
                                    height: switchMerchant ? 35 : 70,
                                    marginBottom: 7,
                                  }
                                  : { width: 70, height: 70, backgroundColor: isEdit ? Colors.fieldtBgColor : Colors.lightPrimary },
                              ]}>
                              <Text
                                style={[
                                  styles.pinNo,
                                  { fontSize: switchMerchant ? 10 : 20, color: isEdit ? Colors.descriptionColor : 'black' },
                                ]}>
                                2
                              </Text>
                            </View>
                          </TouchableOpacity>

                          <TouchableOpacity
                            disabled={isEdit}
                            onPress={() => {
                              PhoneonNumPadBtn(3);
                            }}>
                            <View
                              style={[
                                styles.pinBtn,
                                switchMerchant
                                  ? {
                                    width: switchMerchant ? 35 : 70,
                                    height: switchMerchant ? 35 : 70,
                                    marginBottom: 7,
                                  }
                                  : { width: 70, height: 70, backgroundColor: isEdit ? Colors.fieldtBgColor : Colors.lightPrimary },
                              ]}>
                              <Text
                                style={[
                                  styles.pinNo,
                                  { fontSize: switchMerchant ? 10 : 20, color: isEdit ? Colors.descriptionColor : 'black' },
                                ]}>
                                3
                              </Text>
                            </View>
                          </TouchableOpacity>
                        </View>
                        <View
                          style={{
                            flexDirection: 'row',
                            justifyContent: 'space-between',
                            alignSelf: 'center',
                            alignItems: 'center',
                            width: '100%',
                          }}>
                          <TouchableOpacity
                            disabled={isEdit}
                            onPress={() => {
                              PhoneonNumPadBtn(4);
                            }}>
                            <View
                              style={[
                                styles.pinBtn,
                                switchMerchant
                                  ? {
                                    width: switchMerchant ? 35 : 70,
                                    height: switchMerchant ? 35 : 70,
                                    marginBottom: 7,
                                  }
                                  : { width: 70, height: 70, backgroundColor: isEdit ? Colors.fieldtBgColor : Colors.lightPrimary },
                              ]}>
                              <Text
                                style={[
                                  styles.pinNo,
                                  { fontSize: switchMerchant ? 10 : 20, color: isEdit ? Colors.descriptionColor : 'black' },
                                ]}>
                                4
                              </Text>
                            </View>
                          </TouchableOpacity>

                          <TouchableOpacity
                            disabled={isEdit}
                            onPress={() => {
                              PhoneonNumPadBtn(5);
                            }}>
                            <View
                              style={[
                                styles.pinBtn,
                                switchMerchant
                                  ? {
                                    width: switchMerchant ? 35 : 70,
                                    height: switchMerchant ? 35 : 70,
                                    marginBottom: 7,
                                  }
                                  : { width: 70, height: 70, backgroundColor: isEdit ? Colors.fieldtBgColor : Colors.lightPrimary },
                              ]}>
                              <Text
                                style={[
                                  styles.pinNo,
                                  { fontSize: switchMerchant ? 10 : 20, color: isEdit ? Colors.descriptionColor : 'black' },
                                ]}>
                                5
                              </Text>
                            </View>
                          </TouchableOpacity>

                          <TouchableOpacity
                            disabled={isEdit}
                            onPress={() => {
                              PhoneonNumPadBtn(6);
                            }}>
                            <View
                              style={[
                                styles.pinBtn,
                                switchMerchant
                                  ? {
                                    width: switchMerchant ? 35 : 70,
                                    height: switchMerchant ? 35 : 70,
                                    marginBottom: 7,
                                  }
                                  : { width: 70, height: 70, backgroundColor: isEdit ? Colors.fieldtBgColor : Colors.lightPrimary },
                              ]}>
                              <Text
                                style={[
                                  styles.pinNo,
                                  { fontSize: switchMerchant ? 10 : 20, color: isEdit ? Colors.descriptionColor : 'black' },
                                ]}>
                                6
                              </Text>
                            </View>
                          </TouchableOpacity>
                        </View>

                        <View
                          style={{
                            flexDirection: 'row',
                            justifyContent: 'space-between',
                            alignSelf: 'center',
                            alignItems: 'center',
                            width: '100%',
                          }}>
                          <TouchableOpacity
                            disabled={isEdit}
                            onPress={() => {
                              PhoneonNumPadBtn(7);
                            }}>
                            <View
                              style={[
                                styles.pinBtn,
                                switchMerchant
                                  ? {
                                    width: switchMerchant ? 35 : 70,
                                    height: switchMerchant ? 35 : 70,
                                    marginBottom: 7,
                                  }
                                  : { width: 70, height: 70, backgroundColor: isEdit ? Colors.fieldtBgColor : Colors.lightPrimary },
                              ]}>
                              <Text
                                style={[
                                  styles.pinNo,
                                  { fontSize: switchMerchant ? 10 : 20, color: isEdit ? Colors.descriptionColor : 'black' },
                                ]}>
                                7
                              </Text>
                            </View>
                          </TouchableOpacity>

                          <TouchableOpacity
                            onPress={() => {
                              disabled = { isEdit };
                              PhoneonNumPadBtn(8);
                            }}>
                            <View
                              style={[
                                styles.pinBtn,
                                switchMerchant
                                  ? {
                                    width: switchMerchant ? 35 : 70,
                                    height: switchMerchant ? 35 : 70,
                                    marginBottom: 7,
                                  }
                                  : { width: 70, height: 70, backgroundColor: isEdit ? Colors.fieldtBgColor : Colors.lightPrimary },
                              ]}>
                              <Text
                                style={[
                                  styles.pinNo,
                                  { fontSize: switchMerchant ? 10 : 20, color: isEdit ? Colors.descriptionColor : 'black' },
                                ]}>
                                8
                              </Text>
                            </View>
                          </TouchableOpacity>

                          <TouchableOpacity
                            disabled={isEdit}
                            onPress={() => {
                              PhoneonNumPadBtn(9);
                            }}>
                            <View
                              style={[
                                styles.pinBtn,
                                switchMerchant
                                  ? {
                                    width: switchMerchant ? 35 : 70,
                                    height: switchMerchant ? 35 : 70,
                                    marginBottom: 7,
                                  }
                                  : { width: 70, height: 70, backgroundColor: isEdit ? Colors.fieldtBgColor : Colors.lightPrimary },
                              ]}>
                              <Text
                                style={[
                                  styles.pinNo,
                                  { fontSize: switchMerchant ? 10 : 20, color: isEdit ? Colors.descriptionColor : 'black' },
                                ]}>
                                9
                              </Text>
                            </View>
                          </TouchableOpacity>
                        </View>
                        <View
                          style={{
                            flexDirection: 'row',
                            justifyContent: 'space-between',
                            alignSelf: 'center',
                            alignItems: 'center',
                            width: '100%',
                          }}>
                          <TouchableOpacity
                            disabled={isEdit}
                            onPress={() => {
                              if (customerPhone.includes('+')) {

                              }
                              else {
                                PhoneonNumPadBtn('+');
                              }
                            }}>
                            <View
                              style={[
                                styles.pinBtn,
                                switchMerchant
                                  ? {
                                    width: switchMerchant ? 35 : 70,
                                    height: switchMerchant ? 35 : 70,
                                    marginBottom: 7,
                                  }
                                  : { width: 70, height: 70, backgroundColor: isEdit ? Colors.fieldtBgColor : Colors.lightPrimary },
                              ]}>
                              <Text
                                style={[
                                  styles.pinNo,
                                  { fontSize: switchMerchant ? 10 : 20, color: isEdit ? Colors.descriptionColor : 'black' },
                                ]}>
                                +
                              </Text>
                            </View>
                          </TouchableOpacity>

                          <TouchableOpacity
                            disabled={isEdit}
                            onPress={() => {
                              PhoneonNumPadBtn(0);
                            }}>
                            <View
                              style={[
                                styles.pinBtn,
                                switchMerchant
                                  ? {
                                    width: switchMerchant ? 35 : 70,
                                    height: switchMerchant ? 35 : 70,
                                    marginBottom: 7,
                                  }
                                  : { width: 70, height: 70, backgroundColor: isEdit ? Colors.fieldtBgColor : Colors.lightPrimary },
                              ]}>
                              <Text
                                style={[
                                  styles.pinNo,
                                  { fontSize: switchMerchant ? 10 : 20, color: isEdit ? Colors.descriptionColor : 'black' },
                                ]}>
                                0
                              </Text>
                            </View>
                          </TouchableOpacity>

                          <TouchableOpacity
                            disabled={isEdit}
                            onPress={() => {
                              if (customerPhone != '+6') {
                                PhoneonNumPadBtn(-1);
                              }
                            }}>
                            <View
                              style={[
                                styles.pinBtn,
                                switchMerchant
                                  ? {
                                    width: switchMerchant ? 35 : 70,
                                    height: switchMerchant ? 35 : 70,
                                    marginBottom: 7,
                                  }
                                  : { width: 70, height: 70, backgroundColor: isEdit ? Colors.fieldtBgColor : Colors.lightPrimary },
                              ]}>
                              <Feather
                                name="chevron-left"
                                size={switchMerchant ? 13 : 30}
                                color={isEdit ? Colors.descriptionColor : 'black'}
                                style={{}}
                              />
                            </View>
                          </TouchableOpacity>
                        </View>
                      </View>
                    </View>
                  </View>
                </View>

              </View>
              <TouchableOpacity
                disabled={isLoading}
                style={styles.modalSaveButton}
                onPress={() => {
                  createCRMUser();

                }}>
                <Text
                  style={[
                    styles.modalDescText,
                    { color: Colors.primaryColor },
                  ]}>
                  {isLoading ? 'LOADING...' : (isEdit ? 'SAVE' : 'ADD')}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </ModalView>

        <DateTimePickerModal
          isVisible={takeawayDateModal}
          mode={'date'}
          onConfirm={(text) => {
            setTakeawayDate(moment(text).valueOf());

            setTakeawayDateModal(false);
            setTakeawayTimeModal(true)
            // setTakeawayTimeModal(false);
          }}
          onCancel={() => {
            setTakeawayDateModal(false);
          }}
          date={moment(takeawayDate).toDate()}
          minimumDate={moment(moment()).toDate()}
        />

        <DateTimePickerModal
          isVisible={takeawayTimeModal}
          mode={'time'}
          onConfirm={(text) => {
            setTakeawayTime(moment(text).valueOf());

            setTakeawayTimeModal(false);

            // Combine takeawayDate and the selected time
            const combinedDate = moment(takeawayDate).set({
              hour: moment(text).hour(),
              minute: moment(text).minute(),
            }).valueOf();

            setScheduleDateTime(combinedDate);
            // setTakeawayTimeModal(false);
          }}
          onCancel={() => {
            setTakeawayTimeModal(false);
          }}
          date={moment(takeawayTime).toDate()}
        />

        {(!isOnMenu && cartItems.length === 0 && selectedOutletItem) ?
          <View>
            <View>
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'center',
                  alignItems: 'center',
                  borderBottomWidth: 0.6,
                }}>
                <View style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  paddingLeft: 20,
                  //backgroundColor: 'blue',
                }}>
                  {(amountOffItemSkuDict[selectedOutletItem.sku] !== undefined ||
                    amountOffCategory !== undefined)
                    ?
                    <Text style={{
                      color: Colors.descriptionColor,
                      fontFamily: "NunitoSans-SemiBold",
                      paddingVertical: windowWidth * 0.01,
                      fontSize: switchMerchant ? 10 : 16,
                      // marginLeft: 5,
                      width: '100%',
                    }}>
                      {
                        amountOffItemSkuDict[selectedOutletItem.sku] !== undefined
                          ?
                          `Buy ${amountOffItemSkuDict[selectedOutletItem.sku].quantityMin} ~ ${amountOffItemSkuDict[selectedOutletItem.sku].quantityMax} pcs to enjoy RM${amountOffItemSkuDict[selectedOutletItem.sku].amountOff.toFixed(2)} off\n(Min purchases: RM${amountOffItemSkuDict[selectedOutletItem.sku].priceMin})`
                          :
                          `Buy ${amountOffCategory.quantityMin} ~ ${amountOffCategory.quantityMax} pcs to enjoy RM${amountOffCategory.amountOff.toFixed(0)} off\n(Min purchases: RM${amountOffCategory.priceMin})`
                      }
                    </Text>
                    :
                    <></>
                  }
                </View>

                <View style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  paddingLeft: 20,
                  //backgroundColor: 'blue',
                }}>
                  {(percentageOffItemSkuDict[selectedOutletItem.sku] !== undefined ||
                    percentageOffCategory !== undefined)
                    ?
                    <Text style={{
                      color: Colors.descriptionColor,
                      fontFamily: "NunitoSans-SemiBold",
                      paddingVertical: windowWidth * 0.01,
                      fontSize: switchMerchant ? 10 : 16,
                      // marginLeft: 5,
                      width: '100%',
                    }}>
                      {
                        percentageOffItemSkuDict[selectedOutletItem.sku] !== undefined
                          ?
                          `Buy ${percentageOffItemSkuDict[selectedOutletItem.sku].quantityMin} ~ ${percentageOffItemSkuDict[selectedOutletItem.sku].quantityMax} pcs to enjoy ${percentageOffItemSkuDict[selectedOutletItem.sku].percentageOff.toFixed(0)}% off\n(Min purchases: RM${percentageOffItemSkuDict[selectedOutletItem.sku].priceMin})`
                          :
                          `Buy ${percentageOffCategory.quantityMin} ~ ${percentageOffCategory.quantityMax} pcs to enjoy ${percentageOffCategory.percentageOff.toFixed(0)}% off\n(Min purchases: RM${percentageOffCategory.priceMin})`
                      }
                    </Text>
                    :
                    <></>
                  }
                </View>
              </View>
            </View>

            <View style={{
              flexDirection: 'row', alignSelf: 'center', width: '100%', alignItems: 'center',
              justifyContent: 'center', backgroundColor: Colors.fieldtBgColor, paddingVertical: 10, borderBottomWidth: 0.6, //marginTop: 20, borderWidth: 1.5, borderRadius: 10,
            }}>
              <TouchableOpacity
                testID='MoCartScreen.buttonDineIn'
                onPress={() => {
                  requestAnimationFrame(() => {
                    // setMethod('dinein');
                    //selectedOutletTableMo.uniqueId != undefined ? null : navigation.navigate('MoTable')                 

                    if (currOutletShiftStatus === OUTLET_SHIFT_STATUS.OPENED) {
                      CommonStore.update((s) => {
                        s.orderType = ORDER_TYPE.DINEIN;
                        s.orderTypeSub = ORDER_TYPE_SUB.NORMAL;

                        s.orderTypeMo = ORDER_TYPE.DINEIN;
                        s.orderTypeSubMo = ORDER_TYPE_SUB.NORMAL;

                        s.moMethod = 'dinein';
                      });
                    }
                    else {
                      Alert.alert('Info', 'Please open the shift first before proceed.')
                    }
                  });
                }}
                style={{ width: '33.3%' }}>

                <Text style={{
                  fontSize: switchMerchant ? windowWidth / 60 : 18, textAlign: 'center', fontFamily: 'NunitoSans-Bold',
                  // color: moMethod === 'dinein' ? Colors.primaryColor : Colors.blackColor,
                  color: orderTypeMo === ORDER_TYPE.DINEIN ? Colors.primaryColor : Colors.blackColor,
                }}>Dine In</Text>

              </TouchableOpacity>

              <View style={{ borderWidth: 0.5, borderRightWidth: 0, height: '170%' }} />

              <TouchableOpacity
                testID='MoCartScreen.buttonTakeaway'
                onPress={() => {
                  requestAnimationFrame(() => {
                    // setMethod('takeaway');

                    if (currOutletShiftStatus === OUTLET_SHIFT_STATUS.OPENED) {
                      CommonStore.update((s) => {
                        s.orderType = ORDER_TYPE.PICKUP;
                        s.orderTypeSub = ORDER_TYPE_SUB.NORMAL;

                        s.orderTypeMo = ORDER_TYPE.PICKUP;
                        s.orderTypeSubMo = ORDER_TYPE_SUB.NORMAL;

                        s.moMethod = 'takeaway';

                        // 2025-06-07 - to address issue when place takeaway orders, the last selected table still listen to snapshot, and causing side effects: table auto unseated
                        s.selectedOutletTable = {};
                      });

                      TableStore.update(s => {
                        s.orderDisplayIndividual = false;
                        s.orderDisplayProduct = false;
                        s.orderDisplaySummary = true;

                        s.viewTableOrderModal = false;
                        s.renderPaymentSummary = false;
                        s.renderReceipt = false;

                        s.displayQrModal = false;
                        s.displayQModal = false;
                        s.deleteTableModal = false;
                        s.updateTableModal = false;
                        s.joinTableModal = false;
                        s.moveOrderModal = false;
                        s.addSectionAreaModel = false;
                        s.addTableModal = false;
                        s.preventDeleteTableModal = false;
                        s.seatingModal = false;
                        s.showLoyaltyModal = false;
                        s.showAddLoyaltyModal = false;
                        s.cashbackModal = false;
                      });
                    }
                    else {
                      Alert.alert('Info', 'Please open the shift first before proceed.')
                    }
                  });
                }}
                style={{ width: '33.3%' }}>

                <Text style={{
                  fontSize: switchMerchant ? windowWidth / 60 : 18, textAlign: 'center', fontFamily: 'NunitoSans-Bold',
                  // color: moMethod === 'takeaway' ? Colors.primaryColor : Colors.blackColor,
                  color: (orderTypeMo === ORDER_TYPE.PICKUP && orderTypeSubMo === ORDER_TYPE_SUB.NORMAL) ? Colors.primaryColor : Colors.blackColor,
                }}>Takeaway</Text>

              </TouchableOpacity>

              <View style={{ borderWidth: 0.5, borderLeftWidth: 0, height: '170%' }} />

              <TouchableOpacity
                testID='MoCartScreen.buttonOtherD'
                onPress={() => {
                  requestAnimationFrame(() => {
                    // setMethod('takeaway');

                    if (currOutletShiftStatus === OUTLET_SHIFT_STATUS.OPENED) {
                      CommonStore.update((s) => {
                        s.orderType = ORDER_TYPE.PICKUP;
                        s.orderTypeSub = ORDER_TYPE_SUB.OTHER_DELIVERY;

                        s.orderTypeMo = ORDER_TYPE.PICKUP;
                        s.orderTypeSubMo = ORDER_TYPE_SUB.OTHER_DELIVERY;

                        s.moMethod = 'otherD';

                        // 2025-06-07 - to address issue when place takeaway orders, the last selected table still listen to snapshot, and causing side effects: table auto unseated
                        s.selectedOutletTable = {};
                      });

                      TableStore.update(s => {
                        s.orderDisplayIndividual = false;
                        s.orderDisplayProduct = false;
                        s.orderDisplaySummary = true;

                        s.viewTableOrderModal = false;
                        s.renderPaymentSummary = false;
                        s.renderReceipt = false;

                        s.displayQrModal = false;
                        s.displayQModal = false;
                        s.deleteTableModal = false;
                        s.updateTableModal = false;
                        s.joinTableModal = false;
                        s.moveOrderModal = false;
                        s.addSectionAreaModel = false;
                        s.addTableModal = false;
                        s.preventDeleteTableModal = false;
                        s.seatingModal = false;
                        s.showLoyaltyModal = false;
                        s.showAddLoyaltyModal = false;
                        s.cashbackModal = false;
                      });

                      CommonStore.update((s) => {
                        s.odpt = 'INHOUSE';
                        s.odpoi = '';
                      })
                      // setOdpoi('');
                    }
                    else {
                      Alert.alert('Info', 'Please open the shift first before proceed.')
                    }
                  });
                }}
                style={{ width: '33.3%' }}>

                <Text style={{
                  fontSize: switchMerchant ? windowWidth / 60 : 18, textAlign: 'center', fontFamily: 'NunitoSans-Bold',
                  // color: moMethod === 'takeaway' ? Colors.primaryColor : Colors.blackColor,
                  color: (orderTypeMo === ORDER_TYPE.PICKUP && orderTypeSubMo === ORDER_TYPE_SUB.OTHER_DELIVERY) ? Colors.primaryColor : Colors.blackColor,
                }}>Other D.</Text>

              </TouchableOpacity>

            </View>

            {moMethod === 'dinein' ?
              <View stlye={{ flexDirection: 'column' }}>
                <View style={{ flexDirection: 'row', marginTop: 20, marginBottom: 5, alignItems: 'center', justifyContent: 'flex-start', paddingLeft: 20, flexWrap: 'wrap', display: 'flex' }}>
                  <View>
                    <Text style={{
                      fontSize: switchMerchant ? windowWidth / 60 : 20,
                      fontFamily: 'NunitoSans-Bold',
                      color: Colors.blackColor,
                      // width: '90%',

                    }}
                      numberOfLines={4}
                    >Table: {selectedOutletTableMo.code ? selectedOutletTableMo.code : ''}</Text>
                    <Text style={{
                      fontSize: switchMerchant ? 10 : 20,
                      fontFamily: 'NunitoSans-Bold',
                      color: Colors.blackColor,
                    }}>{selectedOutletTableMo.capacity ? `(${selectedOutletTableMo.capacity} seats)` : ''}</Text>
                  </View>
                  <TouchableOpacity
                    testID='MoCartScreen.buttonSelectTable'
                    onPress={() => {
                      requestAnimationFrame(() => {
                        if (currOutletShiftStatus === OUTLET_SHIFT_STATUS.OPENED) {
                          navigation.navigate('MoTable');
                          // setPax(1);

                          CommonStore.update(s => {
                            s.moPax = '1';
                          });
                        }
                        else {
                          Alert.alert('Info', 'Please open the shift first before proceed.')
                        }
                      });
                    }
                    }
                    style={[
                      Styles.button,
                      {
                        marginLeft: 5, backgroundColor: Colors.primaryColor, paddingVertical: 12,
                        // width: windowWidth * 0.1,
                        paddingHorizontal: 30,
                        // borderRadius: 4,
                      }
                    ]}>
                    <Text style={{
                      color: Colors.whiteColor,
                      fontFamily: 'NunitoSans-SemiBold',
                      fontSize: switchMerchant ? windowWidth / 60 : 18,

                    }}>
                      {selectedOutletTableMo.uniqueId === undefined ? 'SELECT' : 'CHANGE'}
                    </Text>
                  </TouchableOpacity>

                </View>
                <View style={{ flexDirection: 'row', alignItems: 'center', marginTop: 15, marginBottom: 5, justifyContent: 'flex-start', paddingLeft: 20, }}>
                  <Text style={{
                    fontSize: switchMerchant ? windowWidth / 60 : 22,
                    fontFamily: 'NunitoSans-Bold',
                  }}>Pax:</Text>
                  <View style={{ flexDirection: 'row', paddingLeft: 10, }}>
                    <TouchableOpacity
                      onPress={() => {
                        requestAnimationFrame(() => {
                          // setPax(pax - 1 >= 0 ? pax - 1 : 0);

                          var moPaxParsed = !isNaN(parseInt(moPax)) ? parseInt(moPax) : 1;

                          CommonStore.update(s => {
                            s.moPax = (moPaxParsed - 1 >= 1 ? moPaxParsed - 1 : 1).toFixed(0);
                          });
                        });
                      }}>
                      <View
                        style={[
                          styles.addBtn,
                          { backgroundColor: Colors.descriptionColor },
                          switchMerchant
                            ? {
                              width: windowWidth * 0.03,
                              height: windowWidth * 0.03,
                            }
                            : {},
                        ]}>
                        <Text
                          style={{
                            fontSize: switchMerchant ? windowWidth / 60 : 30,
                            fontWeight: '500',
                            color: Colors.whiteColor,
                          }}>
                          -
                        </Text>
                      </View>
                    </TouchableOpacity>
                    <View
                      style={[
                        styles.addBtn,
                        {
                          backgroundColor: Colors.whiteColor,
                          borderWidth: StyleSheet.hairlineWidth,
                          borderColor: Colors.descriptionColor,
                          borderWidth: 1.5,

                          width: 50,

                          marginLeft: -1,
                        },
                        switchMerchant
                          ? {
                            width: windowWidth * 0.03,
                            height: windowWidth * 0.03,
                          }
                          : {},
                      ]}>
                      <TextInput
                        // editable={editQuantity}
                        style={[
                          {
                            fontSize: switchMerchant ? windowWidth / 80 : 25,
                            fontWeight: 'bold',
                            color: Colors.primaryColor,
                            //paddingTop: 5,
                            paddingBottom: 5,
                          },
                          switchMerchant ? {
                            paddingBottom: Platform.OS === 'ios' ? 5 : 0,
                          } : {

                          },
                        ]}
                        // defaultValue={quantity.toFixed(0)}
                        // onChangeText={(text) => {
                        //   // setQuantity(isNaN(text) ? quantity : parseInt(text))
                        //   text = text.replace(/[^0-9]/g, '');
                        //   setQuantity(text.length > 0 ? parseInt(text) : 0);
                        //   setEditQuantity(false);
                        // }}
                        // onSubmitEditing={(text) => {
                        //   setQuantity(isNaN(text) ? quantity : parseInt(text))                          
                        // }}
                        // onEndEditing={() => {
                        //   setEditQuantity(false);
                        // }}
                        //iOS
                        placeholder={'0'}
                        placeholderTextColor={Platform.select({
                          ios: '#a9a9a9',
                        })}
                        // clearTextOnFocus
                        selectTextOnFocus
                        //////////////////////////////////////////////
                        //Android
                        // onFocus={() => {
                        //   setTemp(moPax);
                        //   CommonStore.update(s => {
                        //     s.moPax = '';
                        //   });
                        // }}
                        ///////////////////////////////////////////////
                        //When textinput is not selected
                        // onEndEditing={() => {
                        //   if (moPax == '') {
                        //     CommonStore.update(s => {
                        //       s.moPax = temp;
                        //     });
                        //   }
                        // }}
                        onChangeText={(text) => {
                          // setState({ itemPrice: text });

                          var value = parseValidIntegerText(text);

                          var moPaxParsed = !isNaN(parseInt(value)) ? parseInt(value) : 1;

                          if (moPaxParsed < selectedOutletTableMo.capacity) {
                            CommonStore.update(s => {
                              s.moPax = moPaxParsed.toFixed(0);
                            });
                          }
                          else if (selectedOutletTableMo.capacity) {
                            CommonStore.update(s => {
                              s.moPax = selectedOutletTableMo.capacity.toFixed(0);
                            });
                          }
                          else {
                            CommonStore.update(s => {
                              s.moPax = '1';
                            });
                          }

                          // setEditQuantity(false);
                        }}
                        value={moPax}
                        // autoFocus={editQuantity}
                        keyboardType={'decimal-pad'}
                        // keyboardType={'default'}
                        // placeholder="0"
                        underlineColorAndroid={Colors.fieldtBgColor}
                      />
                      {/* <TouchableOpacity onPress={() => setEditPax(true)}>
                        {editPax ?
                          <TextInput
                            style={[
                              {
                                fontSize: switchMerchant ? 10 : 25,
                                fontWeight: 'bold',
                                color: Colors.primaryColor,
                                paddingTop: 5,
                                paddingBottom: 0,
                              },
                            ]}
                            defaultValue={moPax.toFixed(0)}
                            onChangeText={(text) => {
                              // setPax(text.length > 0 ? parseInt(text) : 0)
                              setPax(isNaN(text) ? 0 : parseInt(text));
                              setEditPax(false)
                            }}
                            // onEndEditing={() => {
                            //   setEditPax(false)
                            // }}
                            autoFocus={editPax}
                            keyboardType={'decimal-pad'}
                            // placeholder="0"
                            underlineColorAndroid={Colors.fieldtBgColor}
                          />
                          :
                          <Text
                            style={[
                              {
                                fontSize: switchMerchant ? 10 : 25,
                                fontWeight: 'bold',
                                color: Colors.primaryColor,
                              },
                            ]}>
                            {moPax}
                          </Text>
                        }
                      </TouchableOpacity> */}

                    </View>
                    <TouchableOpacity
                      onPress={() => {

                        requestAnimationFrame(() => {
                          var moPaxParsed = !isNaN(parseInt(moPax)) ? parseInt(moPax) : 0;

                          if (moPaxParsed < selectedOutletTableMo.capacity)
                            // setPax(pax + 1);

                            CommonStore.update(s => {
                              s.moPax = (moPaxParsed + 1).toFixed(0);
                            });
                          else {
                            null
                          }
                        });
                      }}>
                      <View
                        style={[
                          styles.addBtn,
                          {
                            backgroundColor: Colors.primaryColor,
                            left: -1,
                          },
                          switchMerchant
                            ? {
                              width: windowWidth * 0.03,
                              height: windowWidth * 0.03,
                            }
                            : {},
                        ]}>
                        <Text
                          style={{
                            fontSize: switchMerchant ? windowWidth / 60 : 30,
                            fontWeight: '500',
                            color: Colors.whiteColor,
                          }}>
                          +
                        </Text>
                      </View>
                    </TouchableOpacity>
                  </View>

                </View>
              </View>

              //</View>

              : null}

            {/* <TouchableOpacity
              onPress={() => {
                setIsSelectinfo(false)
              }}>
              <View
                style={[
                  {
                    backgroundColor: Colors.primaryColor,
                    padding: 20,
                    paddingVertical: 12,
                    borderRadius: 10,
                    alignItems: 'center',

                    marginHorizontal: 48,
                    width: '50%',
                    marginTop: 20,
                    alignSelf: 'center',
                  },
                  switchMerchant
                    ? {
                      width: windowWidth * 0.2,
                      height: windowHeight * 0.1,
                      bottom: windowHeight * 0.02,
                      padding: 0,
                    }
                    : {},
                ]}>
                <Text
                  style={[
                    {
                      color: '#ffffff',
                      fontSize: switchMerchant ? 10 : 18,
                      // borderWidth: 1,
                      bottom: switchMerchant ? 2 : 0,
                      fontFamily: 'NunitoSans-SemiBold',
                    },
                    switchMerchant
                      ? {
                        height: '500%',
                        top: -5,
                      }
                      : {},
                  ]}>
                  CONFIRM
                </Text>
              </View>
            </TouchableOpacity> */}
          </View>
          :
          <View style={{
            flexDirection: 'column',
            // alignItems: 'center',
            // width: '100%',
          }}>
            {/* <TouchableOpacity
              style={{
                flexDirection: 'row',
                marginLeft: Platform.OS == 'android' ? 6 : 10,
                marginTop: 17,
                marginBottom: -15,
                //backgroundColor: 'blue'
              }}
              onPress={() => {
                setIsSelectinfo(true)
                CommonStore.update((s) => {
                  s.orderTypeMo = ORDER_TYPE.DINEIN;
                })
              }}>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'center',
                  alignContent: 'center',
                  marginBottom: 10,
                }}>
                <View>
                  {switchMerchant ? (
                    <Icons
                      name="chevron-left"
                      size={20}
                      color={Colors.primaryColor}
                      style={{ paddingLeft: '1%' }}
                    />
                  ) : (
                    <Icons
                      name="chevron-left"
                      size={30}
                      color={Colors.primaryColor}
                      style={{}}
                    />
                  )}
                </View>
                <Text
                  style={{
                    color: Colors.primaryColor,
                    fontSize: switchMerchant ? 14 : 17,
                    textAlign: 'center',
                    fontFamily: 'NunitoSans-Bold',
                    marginBottom: Platform.OS === 'ios' ? 0 : 2,
                    //top: -2,
                    //marginLeft: -3,
                  }}>
                  Back
                </Text>
              </View>
            </TouchableOpacity> */}
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'center',
                height: windowHeight * 0.07,
                width: windowWidth * 0.26,
                backgroundColor: Colors.whiteColor,
              }}>
              {customerName && customerPhone && customerUniqueId ?
                <View style={{ flexDirection: 'row', width: windowWidth * 0.26, alignItems: 'center', alignSelf: 'center', justifyContent: 'center', }}>
                  <View style={{ width: windowWidth * 0.07, paddingLeft: 5, }}>
                    <Text numberOfLines={1} style={{
                      fontSize: switchMerchant ? windowWidth / 60 : 16, textAlign: 'left', fontFamily: 'NunitoSans-Bold',
                    }}>
                      {'Member:'}
                    </Text>
                  </View>
                  <View style={{ width: windowWidth * 0.17, }}>
                    <Text numberOfLines={1} style={{
                      fontSize: switchMerchant ? windowWidth / 60 : 18, textAlign: 'center', fontFamily: 'NunitoSans-Bold',
                    }}>
                      {customerName}
                    </Text>
                  </View>
                  <View style={{ width: windowWidth * 0.02, alignItems: 'center', justifyContent: 'center', }}>
                    <TouchableOpacity onPress={() => {
                      TableStore.update(s => {
                        s.customerName = '';
                        s.customerPhone = '+60';
                        s.customerEmail = '';
                        s.customerUniqueId = '';
                        s.isCustomer = false;
                      });
                      CommonStore.update(s => {
                        s.selectedCustomerEdit = null;
                      });
                    }}
                      stlye={{ marginLeft: 5, }}>
                      <AIcon
                        name="closecircle"
                        size={25}
                        color={Colors.fieldtTxtColor}
                      />
                    </TouchableOpacity>
                  </View>
                </View>
                :
                <TouchableOpacity
                  style={[
                    {
                      justifyContent: 'center',
                      flexDirection: 'row',
                      borderWidth: 1,
                      borderColor: Colors.primaryColor,
                      backgroundColor: '#4E9F7D',
                      borderRadius: 5,
                      width: 145,
                      paddingHorizontal: 10,
                      height: 35,
                      alignItems: 'center',
                      shadowOffset: {
                        width: 0,
                        height: 2,
                      },
                      shadowOpacity: 0.22,
                      shadowRadius: 3.22,
                      elevation: 1,
                      zIndex: -1,
                    },
                    switchMerchant
                      ? {
                        width: windowWidth * 0.13,
                        height: windowHeight * 0.08,
                        left: windowWidth * 0.003,
                      }
                      : {},
                  ]}
                  onPress={() => {

                    TableStore.update(s => {
                      s.searchCustomer = '';
                      s.showCustomerList = true;
                    });
                  }}>
                  <Text
                    style={[
                      {
                        color: Colors.whiteColor,
                        marginLeft: 5,
                        fontSize: 16,
                        fontFamily: 'NunitoSans-Bold',
                        textTransform: 'uppercase',
                        width: '100%',
                        textAlign: 'center',
                      },
                      switchMerchant
                        ? {
                          fontSize: 10,
                        }
                        : {},
                    ]}
                    numberOfLines={1}>
                    MEMBER
                  </Text>
                </TouchableOpacity>
              }
            </View>
            <View
              style={{
                flexDirection: 'row',
                marginTop: 0,
                height: windowHeight * 0.53,
                borderTopWidth: 1,
              }}>
              <FlatList
                ref={flatListRef}
                style={{ marginBottom: 10 }}
                data={cartItemsProcessed}
                extraData={cartItemsProcessed}
                renderItem={renderItem}
                keyExtractor={(item, index) => String(index)}
                contentContainerStyle={{
                  paddingHorizontal: 15,
                  paddingBottom: 5,
                }}
              />
            </View>
            {/* container above total (JJ's comment) */}
            <View style={{ height: windowHeight * 0.4, width: windowWidth * 0.26, borderTopWidth: 1, }}>
              <ScrollView contentContainerStyle={{ paddingBottom: windowHeight * 0.3 }} scrollEnabled showsVerticalScrollIndicator={false}>
                {orderTypeMo === ORDER_TYPE.PICKUP && orderTypeSubMo === ORDER_TYPE_SUB.OTHER_DELIVERY ?
                  <View stlye={{ flexDirection: 'column', }}>
                    <View style={{ flexDirection: 'row', marginTop: 5, marginBottom: 5, alignItems: 'center', justifyContent: 'flex-start', paddingLeft: 10, }}>
                      <Text style={{
                        fontSize: switchMerchant ? windowWidth / 60 : 20,
                        fontFamily: 'NunitoSans-Bold',
                        color: Colors.blackColor,
                        marginRight: 10,
                      }}>
                        Courier:
                      </Text>

                      {/* <DropDownPicker
                        globalTextStyle={{
                          fontSize: switchMerchant ? 10 : 14,
                        }}
                        containerStyle={{
                          height: switchMerchant ? 35 : 40,
                        }}
                        arrowColor={'black'}
                        arrowSize={switchMerchant ? 17 : 20}
                        arrowStyle={{ fontWeight: 'bold' }}
                        labelStyle={{
                          fontFamily: 'NunitoSans-Regular',
                          fontSize: switchMerchant ? 10 : 14,
                        }}
                        style={[{
                          width: windowWidth * 0.15,
                          paddingVertical: 0,
                          backgroundColor: Colors.fieldtBgColor,
                          borderRadius: 10,
                        }, switchMerchant ? {
                          marginRight: 5,
                        } : {}]}
                        placeholderStyle={{ color: Colors.fieldtTxtColor }}
                        placeholder={'Select'}
                        items={OTHER_DELIVERY_PARTNER_TYPES_DROPDOWN_LIST}
                        itemStyle={{
                          justifyContent: 'flex-start',
                          paddingHorizontal:
                            windowWidth * 0.0079,
                          zIndex: 100,
                        }}
                        onChangeItem={(item) => {
                          setOdpt(item.value);
                        }}
                        defaultValue={odpt}
                        dropDownStyle={{
                          width: windowWidth * 0.15,
                          height: 80,
                          backgroundColor: Colors.fieldtBgColor,
                          borderRadius: 10,
                          borderWidth: 1,
                          textAlign: 'left',
                          zIndex: 1000,
                        }}
                      /> */}
                      <View style={{
                        backgroundColor: Colors.fieldtBgColor,
                        borderWidth: 1,
                        borderColor: '#E5E5E5',
                        width: windowWidth * 0.15,
                        borderRadius: 5,
                        height: 40,
                        justifyContent: 'center',
                      }}>
                        <RNPickerSelect
                          placeholder={{}}
                          useNativeAndroidPickerStyle={false}
                          style={{
                            inputIOS: {
                              fontSize: switchMerchant ? 10 : 14,
                              fontFamily: 'NunitoSans-Regular',
                              textAlign: 'center',
                            },
                            inputAndroid: {
                              fontSize: switchMerchant ? 10 : 14,
                              fontFamily: 'NunitoSans-Regular',
                              justifyContent: 'center',
                              textAlign: 'center',
                              height: 40,
                              color: 'black',
                            },
                            inputAndroidContainer: { width: '100%' },
                            //backgroundColor: 'red',
                            height: 35,

                            chevronContainer: {
                              display: 'none',
                            },
                            chevronDown: {
                              display: 'none',
                            },
                            chevronUp: {
                              display: 'none',
                            },
                          }}
                          items={OTHER_DELIVERY_PARTNER_TYPES_DROPDOWN_LIST}
                          value={odpt}
                          onValueChange={(value) => {
                            // setOdpt(value);

                            CommonStore.update((s) => {
                              s.odpt = value;
                            })
                          }}
                        />
                      </View>
                    </View>

                    <View style={{ flexDirection: "row", alignItems: 'center', marginTop: 15, marginBottom: 5, justifyContent: 'flex-start', paddingLeft: 10, }}>

                      <Text style={{
                        fontSize: switchMerchant ? windowWidth / 60 : 20,
                        fontFamily: 'NunitoSans-Bold',
                        color: Colors.blackColor,
                        marginRight: 10,
                      }}>
                        D. Order ID:
                      </Text>

                      <TextInput
                        style={{
                          backgroundColor: Colors.fieldtBgColor,
                          width: windowWidth * 0.15,
                          height: switchMerchant ? 35 : 40,
                          borderRadius: 5,
                          padding: 5,
                          marginVertical: 5,
                          borderWidth: 1,
                          borderColor: '#E5E5E5',
                          paddingLeft: 10,
                          marginLeft: 5,
                          fontFamily: 'NunitoSans-Regular',
                          fontSize: switchMerchant ? 10 : 14,
                        }}
                        placeholder="D. Order ID"
                        placeholderStyle={{
                          fontFamily: 'NunitoSans-Regular',
                          fontSize: switchMerchant ? 10 : 14,
                        }}
                        placeholderTextColor={Platform.select({
                          ios: '#a9a9a9',
                        })}
                        onChangeText={(text) => {
                          // setOdpoi(text);

                          CommonStore.update((s) => {
                            s.odpoi = text;
                          })
                        }}
                        defaultValue={odpoi}
                      />
                    </View>
                    <View
                      style={{
                        height: 1.5,
                        left: '5%',
                        width: '90%',
                        backgroundColor: '#C2C1C0',
                        opacity: 0.2,
                        marginBottom: 4,
                        marginTop: 10,

                        shadowColor: '#000',
                        shadowOffset: {
                          width: 0,
                          height: 1,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 2.22,
                        elevation: 3,
                      }} />
                  </View>
                  : null}
                <View
                  style={{
                    flexDirection: 'row',
                    width: '100%',
                    marginTop: 0,
                    height: '100%',
                    // backgroundColor: 'blue',
                    paddingHorizontal: 20,
                    paddingTop: switchMerchant ? 0 : 10,
                  }}>
                  {/* subtotal..... (JJ's comment) */}
                  <View style={{ width: '48%', }}>
                    <Text
                      style={[
                        styles.description,
                        {
                          marginBottom: 5,
                        },
                        switchMerchant ? { fontSize: windowWidth / 60 } : {},
                      ]}>
                      Subtotal
                    </Text>
                    <Text
                      style={[
                        styles.description,
                        {
                          marginBottom: 3,
                        },
                        switchMerchant ? { fontSize: windowWidth / 60, height: 35, textAlignVertical: 'center' } : {},
                      ]}>
                      Promo Code
                    </Text>
                    <Text
                      style={[
                        styles.description,
                        switchMerchant ? { fontSize: windowWidth / 60 } : {},
                      ]}>
                      Discount
                    </Text>
                    {
                      checkToApplyTaxOrNot(currOutlet, orderTypeMo, orderTypeSubMo)
                        ?
                        <Text
                          style={[
                            styles.description,
                            switchMerchant ? { fontSize: windowWidth / 60 } : {},
                          ]}>
                          {`Tax (${(currOutlet.taxRate * 100).toFixed(0)}%)`}
                        </Text>
                        :
                        <></>
                    }


                    {
                      !scOtherDApplied
                        ?
                        <>
                          {
                            (checkToApplyScOrNot(currOutlet, orderTypeMo, orderTypeSubMo))
                              ?
                              <Text
                                style={[
                                  styles.description,
                                  switchMerchant ? { fontSize: windowWidth / 60 } : {},
                                ]}>
                                {`${currOutlet.scName ? currOutlet.scName : 'Service Charge'} (${(currOutlet.scRate * 100).toFixed(0)}%)`}
                              </Text>
                              :
                              <></>
                          }
                        </>
                        :
                        <>
                          {
                            (currOutlet.scActiveOtherD && orderTypeMo === ORDER_TYPE.PICKUP &&
                              orderTypeSubMo === ORDER_TYPE_SUB.OTHER_DELIVERY)
                              ?
                              <Text
                                style={[
                                  styles.description,
                                  switchMerchant ? { fontSize: windowWidth / 60 } : {},
                                ]}>
                                {`${currOutlet.scNameOtherD ? currOutlet.scNameOtherD : 'Service Charge'} (${(currOutlet.scRateOtherD * 100).toFixed(0)}%)`}
                              </Text>
                              :
                              <></>
                          }
                        </>
                    }

                    {
                      (currOutlet.deliveryPackagingFee && orderTypeMo === ORDER_TYPE.DELIVERY)
                        ?
                        <Text
                          style={[
                            styles.description,
                            switchMerchant ? { fontSize: windowWidth / 60 } : {},
                          ]}>
                          {`Delivery Packaging Fee`}
                        </Text>
                        :
                        <></>
                    }

                    {
                      (currOutlet.pickupPackagingFee && orderTypeMo === ORDER_TYPE.PICKUP && orderTypeSubMo === ORDER_TYPE_SUB.OTHER_DELIVERY)
                        ?
                        <View>
                          <Text
                            style={[
                              styles.description,
                              switchMerchant ? { fontSize: windowWidth / 60 } : {},
                            ]}>
                            {`Takeaway Packaging Fee`}
                          </Text>

                        </View>
                        :
                        <></>
                    }
                    {(orderTypeMo === ORDER_TYPE.PICKUP && orderTypeSubMo === ORDER_TYPE_SUB.NORMAL) ? <Text
                      style={[
                        styles.description,
                        switchMerchant ? { fontSize: 10 } : {},
                      ]}>
                      {`Takeaway Time`}
                    </Text> : <></>}

                    {type == 1 ? (
                      <View>
                        <Text
                          style={[
                            styles.description,
                            switchMerchant ? { fontSize: windowWidth / 60 } : {},
                          ]}>
                          Delivery Fees
                        </Text>
                        <Text
                          style={[
                            styles.smallDescription,
                            switchMerchant ? { fontSize: windowWidth / 60 } : {},
                          ]}>
                          (This fees is quoted by 3rd party logistic and it might be
                          different as it depends on the exact distance)
                        </Text>
                      </View>
                    ) : null}
                    {/* <Text
                    style={[
                      styles.description,
                      switchMerchant ? { fontSize: windowWidth / 60 } : {},
                    ]} /> */}
                  </View>
                  {/* <View style={{ width: windowWidth * 0.06 }} /> */}
                  <View style={{ width: 0 }} />

                  <View style={{ width: '50%', }}>
                    <View
                      style={{
                        flexDirection: 'row',
                        //width: '70%',
                        justifyContent: 'space-between',
                      }}>
                      <Text
                        style={[styles.price, switchMerchant ? { fontSize: windowWidth / 60, height: 35, } : {}]}>
                        RM
                      </Text>
                      <Text
                        style={[styles.price, switchMerchant ? { fontSize: windowWidth / 60, height: 35, } : {}]}>
                        {(totalPrice + discountPromotionsTotal)
                          .toFixed(2)
                          .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
                      </Text>
                    </View>
                    {/* Select menu (JJ's comment) */}
                    <View
                      style={[{
                        width: '100%',
                        alignContent: 'center',
                      }, switchMerchant ? { height: 35, } : {}]}>
                      {
                        promoCodePromotionDropdownList.find(item => item.value === selectedPromoCodePromotionId)
                          ?
                          <DropDownPicker
                            globalTextStyle={{
                              fontSize: switchMerchant ? windowWidth / 60 : 14,
                            }}
                            containerStyle={{
                              height: switchMerchant ? 35 : 40,
                            }}
                            arrowColor={'black'}
                            arrowSize={switchMerchant ? 17 : 20}
                            arrowStyle={{ fontWeight: 'bold' }}
                            labelStyle={{
                              fontFamily: 'NunitoSans-Regular',
                              fontSize: switchMerchant ? 10 : 14,
                            }}
                            style={[{
                              // width: 200,
                              // width: windowWidth * 0.11,
                              paddingVertical: 0,
                              backgroundColor: Colors.fieldtBgColor,
                              borderRadius: 10,
                              //zIndex: index ++ ? 1 : 0,
                              //elevation: 1000
                            },
                            switchMerchant ? {
                              marginRight: 5,
                            } : {}
                            ]}
                            placeholderStyle={{ color: Colors.fieldtTxtColor }}
                            placeholder={'Select'}
                            // items={[
                            //   {
                            //     label: 'Select Voucher',
                            //     value: 'SELECT_VOUCHER',
                            //   },
                            //   ...selectedVoucherUserList,
                            // ]}
                            items={promoCodePromotionDropdownList}
                            itemStyle={{
                              justifyContent: 'flex-start',
                              //marginLeft: 5,
                              paddingHorizontal:
                                windowWidth * 0.003,
                              zIndex: 100,
                            }}
                            onChangeItem={(item) => {
                              // setSelectedVoucherUserItem(item.value);

                              setSelectedPromoCodePromotionId(item.value);

                              CommonStore.update(s => {
                                s.selectedPromoCodePromotion = availablePromoCodePromotions.find(promotion => promotion.uniqueId === item.value) || {};
                              });
                            }}
                            defaultValue={selectedPromoCodePromotionId}
                            dropDownMaxHeight={100}
                            dropDownStyle={{
                              // width: 200,
                              // width: windowWidth * 0.11,
                              height: 150,
                              backgroundColor: Colors.fieldtBgColor,
                              borderRadius: 10,
                              borderWidth: 1,
                              textAlign: 'left',
                              marginRight: 10,
                            }}
                          />
                          :
                          <></>
                      }
                    </View>

                    <View
                      style={{
                        flexDirection: 'row',
                        //width: '70%',
                        justifyContent: 'space-between',
                        zIndex: -10,
                      }}>
                      <Text
                        style={[styles.price, switchMerchant ? { fontSize: windowWidth / 60, height: 35, } : {}]}>
                        RM
                      </Text>
                      <Text
                        style={[styles.price, switchMerchant ? { fontSize: windowWidth / 60, height: 35, } : {}]}>
                        {(
                          totalDiscount +
                          // (usePointsToRedeem ? pointsToRedeemDiscount : 0) +
                          // (usePointsToRedeemLCC ? pointsToRedeemDiscountLCC : 0) +
                          discountPromotionsTotal
                        ).toFixed(2)}
                      </Text>
                    </View>
                    {
                      checkToApplyTaxOrNot(currOutlet, orderTypeMo, orderTypeSubMo)
                        ?
                        <View
                          style={{
                            flexDirection: 'row',
                            //width: '70%',
                            justifyContent: 'space-between',
                            zIndex: -10,
                          }}>
                          <Text
                            style={[styles.price, switchMerchant ? { fontSize: windowWidth / 60 } : {}]}>
                            RM
                          </Text>
                          <Text
                            style={[styles.price, switchMerchant ? { fontSize: windowWidth / 60 } : {}]}>
                            {totalTax.toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
                          </Text>
                        </View>
                        :
                        <></>
                    }

                    {
                      (checkToApplyScOrNot(currOutlet, orderTypeMo, orderTypeSubMo))
                        ?
                        <View
                          style={{
                            flexDirection: 'row',
                            //width: '70%',
                            justifyContent: 'space-between',
                            zIndex: -10,
                          }}>
                          <Text
                            style={[styles.price, switchMerchant ? { fontSize: windowWidth / 60 } : {}]}>
                            RM
                          </Text>
                          <Text
                            style={[styles.price, switchMerchant ? { fontSize: windowWidth / 60 } : {}]}>
                            {totalSc.toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
                          </Text>
                        </View>
                        :
                        <></>
                    }

                    {
                      (currOutlet.deliveryPackagingFee && orderTypeMo === ORDER_TYPE.DELIVERY)
                        ?
                        <View
                          style={{
                            flexDirection: 'row',
                            ///width: '70%',
                            justifyContent: 'space-between',
                            zIndex: -10,
                          }}>
                          <Text
                            style={[styles.price, switchMerchant ? { fontSize: windowWidth / 60 } : {}]}>
                            RM
                          </Text>
                          <Text
                            style={[styles.price, switchMerchant ? { fontSize: windowWidth / 60 } : {}]}>
                            {currOutlet.deliveryPackagingFee.toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
                          </Text>
                        </View>
                        :
                        <></>
                    }

                    {
                      (currOutlet.pickupPackagingFee && orderTypeMo === ORDER_TYPE.PICKUP && orderTypeSubMo == ORDER_TYPE_SUB.NORMAL)
                        ?
                        <View
                          style={{
                            flexDirection: 'row',
                            //width: '70%',
                            justifyContent: 'space-between',
                            zIndex: -10,
                          }}>
                          <Text
                            style={[styles.price, switchMerchant ? { fontSize: windowWidth / 60 } : {}]}>
                            RM
                          </Text>
                          <Text
                            style={[styles.price, switchMerchant ? { fontSize: windowWidth / 60 } : {}]}>
                            {currOutlet.pickupPackagingFee.toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
                          </Text>
                        </View>
                        :
                        <></>
                    }
                    {/* takeaway time (JJ's comment) */}
                    {
                      (orderTypeMo === ORDER_TYPE.PICKUP && orderTypeSubMo === ORDER_TYPE_SUB.NORMAL)
                        ?
                        <View style={{
                          width: '100%',
                        }}>
                          <View style={{
                            width: '100%',
                            alignContent: 'center',
                          }}>
                            <DropDownPicker
                              globalTextStyle={{
                                fontSize: switchMerchant ? 10 : 14,
                              }}
                              containerStyle={{
                                height: switchMerchant ? 35 : 40,
                              }}
                              arrowColor={'black'}
                              arrowSize={switchMerchant ? 17 : 20}
                              arrowStyle={{ fontWeight: 'bold' }}
                              labelStyle={{
                                fontFamily: 'NunitoSans-Regular',
                                fontSize: switchMerchant ? 10 : 14,
                              }}
                              style={[{
                                // width: 200,
                                paddingVertical: 0,
                                backgroundColor: Colors.fieldtBgColor,
                                borderRadius: 10,
                                //zIndex: index ++ ? 1 : 0,
                                //elevation: 1000
                              }, switchMerchant ? {
                                marginRight: 5,
                              } : {}]}
                              placeholderStyle={{ color: Colors.fieldtTxtColor }}
                              placeholder={'Select'}
                              items={takeawayTimeOption}
                              itemStyle={{
                                justifyContent: 'flex-start',
                                //marginLeft: 5,
                                paddingHorizontal:
                                  windowWidth * 0.003,
                                zIndex: 100,
                              }}
                              onChangeItem={(item) => {
                                setIsLater(item.value);
                              }}
                              defaultValue={isLater}
                              dropDownStyle={{
                                // width: windowWidth * 0.11,
                                height: 80,
                                backgroundColor: Colors.fieldtBgColor,
                                borderRadius: 10,
                                borderWidth: 1,
                                textAlign: 'left',
                                marginRight: 10,
                              }}
                            />
                          </View>
                          {/* is later time picker (JJ's comment) */}
                          {isLater == 'LATER' ?
                            <View style={{
                              flexDirection: 'row',
                              width: '100%',
                              marginTop: 10,
                              marginBottom: 20,
                              zIndex: -1,
                            }}>
                              <TouchableOpacity onPress={() => {
                                setTakeawayDateModal(true);
                              }}
                                style={[{
                                  width: '100%',
                                  backgroundColor: Colors.fieldtBgColor,
                                  // width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 90 : 115,
                                  // width: windowWidth * 0.11,
                                  // padding: 5,
                                  padding: windowWidth * 0.003,
                                  // height: 40,
                                  borderRadius: 5,
                                  // marginVertical: 5,
                                  marginTop: 5,
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                  borderWidth: 1,
                                  borderColor: '#E5E5E5',
                                  fontFamily: 'NunitoSans-Regular',
                                  fontSize: 14,
                                  //marginRight: 10,
                                }, switchMerchant ? {
                                  fontSize: 10,
                                  width: windowWidth * 0.11,
                                  height: 35,
                                } : {}]}
                              >
                                <Text style={[{ fontVariant: ['tabular-nums'], fontFamily: 'NunitoSans-Regular', fontSize: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 12 : 14 }, switchMerchant ? {
                                  fontSize: 10,
                                } : {}]}>{`${scheduleDateTime ? `${moment(scheduleDateTime).format('DD MMM YYYY')}\n${moment(scheduleDateTime).format('hh:mm A')}` : 'Select DateTime'}`}</Text>
                              </TouchableOpacity>
                              {/* <Text>Takeaway Date</Text>
                          <TouchableOpacity onPress={() => {
                            setTakeawayDateModal(true);
                          }}
                            style={[{
                              width: '100%',
                              backgroundColor: Colors.fieldtBgColor,
                              // width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 90 : 115,
                              // width: windowWidth * 0.11,
                              // padding: 5,
                              padding: windowWidth * 0.003,
                              // height: 40,
                              borderRadius: 5,
                              // marginVertical: 5,
                              marginTop: 5,
                              alignItems: 'center',
                              justifyContent: 'center',
                              borderWidth: 1,
                              borderColor: '#E5E5E5',
                              fontFamily: 'NunitoSans-Regular',
                              fontSize: 14,
                              //marginRight: 10,
                            }, switchMerchant ? {
                              fontSize: 10,
                              width: windowWidth * 0.11,
                              height: 35,
                            } : {}]}
                          >
                            <Text style={[{ fontVariant: ['tabular-nums'], fontFamily: 'NunitoSans-Regular', fontSize: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 12 : 14 }, switchMerchant ? {
                              fontSize: 10,
                            } : {}]}>{`${takeawayDate ? `${moment(takeawayDate).format('DD MMM YYYY')}` : 'Select Date'}`}</Text>
                          </TouchableOpacity>
                          <Text>Takeaway Time</Text>
                          <TouchableOpacity onPress={() => {
                            setTakeawayTimeModal(true);
                          }}
                            style={[{
                              width: '100%',
                              backgroundColor: Colors.fieldtBgColor,
                              // width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 90 : 115,
                              // width: windowWidth * 0.11,
                              // padding: 5,
                              padding: windowWidth * 0.003,
                              // height: 40,
                              borderRadius: 5,
                              // marginVertical: 5,
                              marginTop: 5,
                              alignItems: 'center',
                              justifyContent: 'center',
                              borderWidth: 1,
                              borderColor: '#E5E5E5',
                              fontFamily: 'NunitoSans-Regular',
                              fontSize: 14,
                              //marginRight: 10,
                            }, switchMerchant ? {
                              fontSize: 10,
                              width: windowWidth * 0.11,
                              height: 35,
                            } : {}]}
                          >
                            <Text style={[{ fontVariant: ['tabular-nums'], fontFamily: 'NunitoSans-Regular', fontSize: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 12 : 14 }, switchMerchant ? {
                              fontSize: 10,
                            } : {}]}>{`${takeawayTime ? `${moment(takeawayTime).format('hh:mm A')}` : 'Select DateTime'}`}</Text>
                          </TouchableOpacity> */}
                            </View>
                            :
                            <></>
                          }
                        </View>
                        :
                        <></>
                    }

                    {type == 1 ? (
                      <Text
                        style={[styles.price, switchMerchant ? { fontSize: windowWidth / 60 } : {}]}>
                        RM{' '}
                        {deliveryQuotation == null
                          ? '0.00'
                          : parseFloat(deliveryQuotation.totalFee)
                            .toFixed(2)
                            .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
                      </Text>
                    ) : null}
                    {/* <Text style={styles.price}></Text>
            <Text style={styles.price}></Text> */}
                  </View>
                </View>
              </ScrollView>

              {/* Total horizontal container (JJ's comment) */}
              <View
                style={{
                  position: 'absolute',
                  bottom: windowHeight * 0.04,
                  flexDirection: 'row',
                  width: windowWidth * 0.26,
                  // backgroundColor: 'red',
                  paddingHorizontal: 10,
                  backgroundColor: Colors.whiteColor,
                  height: windowHeight * 0.11,
                  alignItems: 'center',
                  justifyContent: 'center',
                  // zIndex: -10,
                }}>
                {/* <View style={{ width: '40%' }}>
                <Text style={[styles.total, switchMerchant ? { fontSize: windowWidth / 60, paddingVertical: 0, } : {}]}>
                  TOTAL
                </Text>
              </View> */}
                {/* <View style={{ width: windowWidth * 0.06 }} /> */}
                {/* <View style={{ width: 0 }} />
              <View style={{ width: '50%' }}>
                <View
                  style={{
                    flexDirection: 'row',
                    //width: '70%',
                    justifyContent: 'space-between',
                  }}>
                  <Text
                    style={[
                      styles.totalPrice,
                      {},
                      switchMerchant ? { fontSize: windowWidth / 60, paddingVertical: 0, } : {},
                    ]}>
                    RM
                  </Text>
                  <Text
                    style={[
                      styles.totalPrice,
                      {},
                      switchMerchant ? { fontSize: windowWidth / 60, paddingVertical: 0, } : {},
                    ]}>
                    {(Math.round(
                      (
                        totalPrice +
                        (checkToApplyTaxOrNot(currOutlet, orderTypeMo, orderTypeSubMo) ? totalTax : 0) +
                        (checkToApplyScOrNot(currOutlet, orderTypeMo, orderTypeSubMo) ? totalSc : 0) +
                        (currOutlet.deliveryPackagingFee && orderTypeMo === ORDER_TYPE.DELIVERY ? currOutlet.deliveryPackagingFee : 0) +
                        (currOutlet.pickupPackagingFee && orderTypeMo === ORDER_TYPE.PICKUP && orderTypeSubMo === ORDER_TYPE_SUB.NORMAL ? currOutlet.pickupPackagingFee : 0)
                      )
                      * 20) / 20)
                      .toFixed(2)
                      .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
                  </Text>
                </View>
              </View> */}
                <TouchableOpacity
                  disabled={isLoading}
                  onPress={handlePlaceOrder}>
                  <View
                    style={[
                      {
                        backgroundColor: Colors.primaryColor,
                        padding: 18,
                        paddingVertical: 12,
                        borderRadius: 10,
                        paddingHorizontal: 10,
                        // alignItems: 'center',

                        // marginHorizontal: 48,

                        // paddingTop: 32,
                        // marginBottom: 24,
                        width: windowWidth * 0.245,
                        marginTop: 0,
                        alignSelf: 'center',
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'space-between',

                        // backgroundColor: 'red',
                      },
                      switchMerchant
                        ? {
                          // width: windowWidth * 0.2,
                          // height: windowHeight * 0.1,
                          // bottom: windowHeight * 0.02,
                          padding: 0,
                        }
                        : {},
                    ]}>
                    {cartItemsProcessed.length > 0 ?
                      <View style={{ flexDirection: 'row' }}>
                        <View style={{ width: '40%', flexDirection: 'row', }}>
                          <MaterialIcons name='shopping-cart-checkout' size={28} color='white'></MaterialIcons>
                          <Text
                            style={[
                              {
                                color: '#ffffff',
                                fontSize: switchMerchant ? windowWidth / 60 : 21,
                                // borderWidth: 1,
                                bottom: switchMerchant
                                  ? windowHeight * 0.023
                                  : 0,
                                fontFamily: 'NunitoSans-SemiBold',
                                paddingLeft: 5,
                              },
                              switchMerchant
                                ? {
                                  height: '500%',
                                  top: -5,
                                }
                                : {},
                            ]}>
                            TOTAL
                          </Text>
                        </View>
                        <View style={{ width: '60%', }}>
                          <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                            <Text
                              style={[
                                {
                                  color: '#ffffff',
                                  fontSize: switchMerchant ? windowWidth / 60 : 21,
                                  // borderWidth: 1,
                                  bottom: switchMerchant
                                    ? windowHeight * 0.023
                                    : 0,
                                  fontFamily: 'NunitoSans-SemiBold',
                                  paddingLeft: 5,
                                },
                                switchMerchant
                                  ? {
                                    height: '500%',
                                    top: -5,
                                  }
                                  : {},
                              ]}>RM
                            </Text>
                            <Text
                              style={[
                                {
                                  color: '#ffffff',
                                  fontSize: switchMerchant ? windowWidth / 60 : 21,
                                  // borderWidth: 1,
                                  bottom: switchMerchant
                                    ? windowHeight * 0.023
                                    : 0,
                                  fontFamily: 'NunitoSans-SemiBold',
                                  paddingLeft: 5,
                                },
                                switchMerchant
                                  ? {
                                    height: '500%',
                                    top: -5,
                                  }
                                  : {},
                              ]}>
                              {`${(Math.round(
                                (
                                  totalPrice +
                                  (checkToApplyTaxOrNot(currOutlet, orderTypeMo, orderTypeSubMo) ? totalTax : 0) +
                                  (checkToApplyScOrNot(currOutlet, orderTypeMo, orderTypeSubMo) ? totalSc : 0) +
                                  (currOutlet.deliveryPackagingFee && orderTypeMo === ORDER_TYPE.DELIVERY ? currOutlet.deliveryPackagingFee : 0) +
                                  (currOutlet.pickupPackagingFee && orderTypeMo === ORDER_TYPE.PICKUP && orderTypeSubMo === ORDER_TYPE_SUB.NORMAL ? currOutlet.pickupPackagingFee : 0)
                                )
                                * 20) / 20)
                                .toFixed(2)
                                .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}`}
                            </Text>
                          </View>
                        </View>
                      </View>
                      :
                      <Text
                        style={[
                          {
                            color: '#ffffff',
                            fontSize: switchMerchant ? windowWidth / 60 : 21,
                            // borderWidth: 1,
                            bottom: switchMerchant
                              ? windowHeight * 0.023
                              : 0,
                            fontFamily: 'NunitoSans-SemiBold',
                            paddingLeft: 5,
                          },
                          switchMerchant
                            ? {
                              height: '500%',
                              top: -5,
                            }
                            : {},
                        ]}>
                        {isLoading ? 'LOADING...' : ' PLACE ORDER'}
                      </Text>
                    }
                  </View>
                </TouchableOpacity>
              </View>
            </View>

            {
              Cart.getVoucher() && (
                <View
                  style={{
                    width: '100%',
                    display: 'flex',
                    flexDirection: 'row',
                    alignItems: 'center',
                    marginTop: 20,
                    justifyContent: 'space-around',
                  }}>
                  <View style={[styles.pic, {}]}>
                    {Cart.getVoucher().outlet && Cart.getVoucher().outlet.cover && (
                      <Image
                        source={{ uri: Cart.getVoucher().outlet.cover }}
                        style={{
                          width: 90,
                          height: Platform.OS == 'ios' ? 90 : 90,
                          borderRadius: 10,
                        }}
                      />
                    )}
                    {!(
                      Cart.getVoucher().outlet && Cart.getVoucher().outlet.cover
                    ) && (
                        <Image
                          source={require('../assets/image/extend.png')}
                          style={{
                            width: 90,
                            height: Platform.OS == 'ios' ? 90 : 90,
                            borderRadius: 10,
                          }}
                        />
                      )}
                  </View>

                  <Text
                    style={[
                      styles.text,
                      {
                        width: '60%',
                      },
                      switchMerchant ? { fontSize: 10 } : {},
                    ]}>
                    {Cart.getVoucher().GiftCard.description}
                  </Text>
                </View>
              )
            }


            {
              type == 1 ? (
                <View
                  style={{
                    width: '100%',
                    // height: 60,
                    paddingVertical: 16,
                    backgroundColor: '#ddebe5',
                    justifyContent: 'center',
                    paddingHorizontal: 28,
                    marginTop: 20,
                  }}>
                  <Text
                    style={{
                      color: Colors.primaryColor,
                      marginLeft: 4,
                      fontSize: switchMerchant ? windowWidth / 60 : 17,
                      fontFamily: 'NunitoSans-SemiBold',
                    }}>
                    Deliver To
                  </Text>
                </View>
              ) : null
            }

            {
              type == 1 ? (
                <View
                  style={{
                    // height: 100,
                    justifyContent: 'center',
                    // borderBottomWidth: StyleSheet.hairlineWidth,
                    paddingHorizontal: 16,
                    paddingVertical: 25,
                  }}>
                  <View
                    style={{
                      flexDirection: 'row',
                      marginLeft: 10,
                      display: 'flex',
                      alignItems: 'center',
                    }}>
                    <View style={{ width: '15%' }}>
                      <Ionicons name="location-sharp" size={34} color={'red'} />
                    </View>
                    <View style={{ width: '60%' }}>
                      <Text
                        style={{
                          fontSize: switchMerchant ? windowWidth / 60 : 14,
                          color: Colors.mainTxtColor,
                          fontFamily: 'NunitoSans-SemiBold',
                        }}>
                        {Cart.getDeliveryAddress() != null ||
                          Cart.getDeliveryAddress() != undefined
                          ? Cart.getDeliveryAddress().address
                          : 'Select an address'}
                      </Text>
                    </View>
                    <View style={{ width: '10%' }} />

                    <TouchableOpacity
                      onPress={() => {
                        setRouteFrom(1);
                        props.navigation.navigate('Address', { testing: 1 });
                      }}
                      style={{
                        marginLeft: 15,
                      }}>
                      {switchMerchant ? (
                        <Icons name="edit" size={18} color={Colors.primaryColor} />
                      ) : (
                        <Icons name="edit" size={24} color={Colors.primaryColor} />
                      )}
                    </TouchableOpacity>
                  </View>
                </View>
              ) : null
            }

            {/* <View
              style={{
                height: 1.5,
                left: '5%',
                width: '90%',
                backgroundColor: '#C2C1C0',
                opacity: 0.2,
                marginBottom: 4,
                marginTop: 20,

                shadowColor: '#000',
                shadowOffset: {
                  width: 0,
                  height: 0.5,
                },
                shadowOpacity: 0.22,
                shadowRadius: 2.22,
                elevation: 3,
              }} /> */}

            {
              type == 1 ? (
                <View
                  style={{
                    // height: 100,
                    justifyContent: 'center',
                    paddingHorizontal: 16,
                    paddingVertical: 25,
                  }}>
                  <View
                    style={{
                      flexDirection: 'row',
                      marginLeft: 16,
                      alignItems: 'center',
                    }}>
                    <TouchableOpacity
                      style={{
                        width: 22,
                        height: 22,
                        backgroundColor: Colors.whiteColor,
                        // marginTop: 10,
                        borderRadius: 20,
                        borderWidth: 1,
                        justifyContent: 'center',
                        borderColor: Colors.primaryColor,
                      }}
                      onPress={() => { }}>
                      <View
                        style={{
                          width: '70%',
                          height: '70%',
                          backgroundColor: Colors.primaryColor,
                          borderRadius: 20,
                          alignSelf: 'center',
                        }} />
                    </TouchableOpacity>
                    <View style={{ width: '5%' }} />
                    <View style={{ width: '15%' }}>
                      {/* <MaterialIcons name="delivery-dining" size={40} color={Colors.primaryColor} /> */}
                      <Image
                        source={require('../assets/image/delivery.png')}
                        style={{
                          width: 34,
                          height: 34,
                          resizeMode: 'contain',
                        }} />
                    </View>
                    <View style={{ width: '55%' }}>
                      <View>
                        <Text
                          style={{
                            color: Colors.primaryColor,
                            // fontWeight: '700',
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: switchMerchant ? 10 : 16,
                          }}>
                          Delivery
                        </Text>
                        <Text
                          style={{
                            color: '#9c9c9c',
                            fontSize: switchMerchant ? 10 : 14,
                            colors: Colors.descriptionColor,
                          }}>
                          Deliver now (45mins)
                        </Text>
                      </View>
                    </View>
                    <View
                      style={{
                        width: '20%',
                      }}>
                      <TouchableOpacity
                        onPress={() => {
                          setState({ showDateTimePicker: true }),
                            generateScheduleTime();
                        }}>
                        <Text
                          style={{
                            color: Colors.primaryColor,
                            // fontWeight: "700",
                            // marginTop: 10
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: switchMerchant ? 10 : 14,
                            textAlign: 'left',
                          }}>
                          {rev_date === undefined
                            ? 'Schedule'
                            : `${rev_date.split(',')[0]} ${rev_date.split(',')[1]}`}
                        </Text>
                      </TouchableOpacity>
                    </View>

                    <ModalView
                      supportedOrientations={['landscape', 'portrait']}
                      style={{ flex: 1 }}
                      visible={showDateTimePicker}
                      transparent
                      animationType="slide">
                      <View
                        style={{
                          backgroundColor: 'rgba(0,0,0,0.5)',
                          flex: 1,
                          justifyContent: 'center',
                          alignItems: 'center',
                        }}>
                        <View style={styles.scheduleBox}>
                          <TouchableOpacity
                            onPress={() => {
                              setState({ showDateTimePicker: false });
                            }}>
                            <View
                              style={{
                                alignSelf: 'flex-end',
                                padding: 16,
                              }}>
                              {/* <Close name="closecircle" size={25} /> */}
                              <AntDesign
                                name="closecircle"
                                size={25}
                                color={Colors.fieldtTxtColor}
                              />
                            </View>
                          </TouchableOpacity>
                          <View>
                            <Text
                              style={{
                                //textAlign: "center",
                                //fontWeight: "bold",
                                fontSize: switchMerchant ? 10 : 11,
                                marginBottom: 5,
                                marginLeft: '20%',
                                color: Colors.fieldtTxtColor,
                              }}>
                              Select delivery time
                            </Text>
                            <Text
                              style={{
                                //textAlign: "center",
                                fontWeight: 'bold',
                                fontSize: switchMerchant ? 10 : 16,
                                marginBottom: 5,
                                marginLeft: '20%',
                                color: Colors.primaryColor,
                              }}>
                              {schedulteTimeSelected.split(',')[0]}{' '}
                              {schedulteTimeSelected == 'TODAY,ASAP' ? ' ' : ', '}{' '}
                              {schedulteTimeSelected.split(',')[1]}
                            </Text>
                          </View>
                          <View
                            style={{
                              justifyContent: 'center',
                              alignItems: 'center',
                              width: '100%',
                              alignContent: 'center',
                              marginBottom: '6%',
                              height: '50%',
                            }}>
                            <View
                              style={{
                                alignItems: 'center',
                                marginBottom: '2%',
                                marginTop: '35%',
                              }}>
                              <SpinPicker
                                data={schedulteTimeList}
                                value={schedulteTimeList[0]}
                                onValueChange={(selectedItem) =>
                                  setState({ schedulteTimeSelected: selectedItem })
                                }
                                keyExtractor={(number) => number.toString()}
                                //showArrows
                                //onInputValueChanged={console.log("hit")}
                                renderItem={renderSchedule}
                              />
                            </View>
                            <View
                              style={{
                                alignItems: 'center',
                                flexDirection: 'row',
                                justifyContent: 'space-between',
                              }}>
                              <TouchableOpacity
                                onPress={() => {
                                  // console.log(
                                  //   'schedulteTimeSelected',
                                  //   schedulteTimeSelected,
                                  // );
                                  setState(
                                    {
                                      rev_date: schedulteTimeSelected,
                                      showDateTimePicker: false,
                                    },
                                    () => {
                                      // console.log(
                                      //   'rev_date: schedulteTimeSelected',
                                      //   rev_date,
                                      // );
                                      var splitDate = rev_date.split(',');
                                      var year = splitDate[2];
                                      var time = splitDate[1];
                                      var splitDay = splitDate[0].split(' ');
                                      var month = splitDay[2];
                                      var date = splitDay[1];
                                      const UTCRevDate = new Date(
                                        `${month
                                        } ${date
                                        } ${year
                                        } ${time
                                        } ${splitDate[3]}`,
                                      );
                                      // console.log('UTCRevDate', UTCRevDate);
                                      setState({ UTCRevDate });
                                    },
                                  );
                                }}
                                style={{
                                  backgroundColor: Colors.primaryColor,
                                  width: '80%',
                                  justifyContent: 'center',
                                  alignItems: 'center',
                                  alignContent: 'center',
                                  borderRadius: 10,
                                  height: '70%',
                                }}>
                                <Text
                                  style={{
                                    fontSize: switchMerchant ? 10 : 24,
                                    color: Colors.whiteColor,
                                  }}>
                                  SCHEDULE
                                </Text>
                              </TouchableOpacity>
                            </View>
                          </View>
                        </View>
                      </View>
                    </ModalView>
                  </View>
                </View>
              ) : null
            }

            {/* <View style={{
              width: '100%',
              // backgroundColor: 'blue',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',

              marginLeft: -(windowWidth * 0.36) / 7,
            }}>
              
            </View> */}
          </View>
        }
      </ScrollView >
    </View>)
  );
});

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
    marginRight: 2,

    flexDirection: 'row',
  },
  description: {
    paddingVertical: 5,
    fontSize: 16,
    color: Colors.descriptionColor,
    fontFamily: 'NunitoSans-Regular',
    fontWeight: '400',
  },
  smallDescription: {
    paddingVertical: 5,
    fontSize: 14,
    color: '#9c9c9c',
    fontFamily: 'NunitoSans-Regular',
    fontWeight: '400',
  },
  payment: {
    color: Colors.descriptionColor,
    paddingVertical: 5,
    fontSize: 18,
    marginTop: 20,
  },
  total: {
    paddingVertical: 5,
    fontSize: 18,
    fontWeight: '700',
    marginTop: 5,
    fontFamily: 'NunitoSans-Regular',
  },
  price: {
    paddingVertical: 5,
    fontSize: 16,
    alignSelf: 'flex-end',
    fontFamily: 'NunitoSans-Regular',
    fontWeight: '400',
  },
  textInput: {
    height: 50,
    paddingHorizontal: 20,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    marginTop: 10,
    marginBottom: 10,
  },
  textInput1: {
    height: 50,
    paddingHorizontal: 20,
    backgroundColor: Colors.secondaryColor,
    borderRadius: 5,
    marginTop: 20,
    justifyContent: 'center',
  },
  totalPrice: {
    color: Colors.primaryColor,
    paddingVertical: 5,
    fontSize: 18,
    fontWeight: '700',
    marginTop: 5,
  },
  confirmBox: {
    width: '60%',
    height: '30%',
    borderRadius: 12,
    backgroundColor: Colors.whiteColor,
  },
  textFieldInput: {
    height: '100%',
    width: '100%',
    paddingHorizontal: 20,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 10,
  },
  sidebar: {
    width: Dimensions.get('window').width * Styles.sideBarWidth,
    // shadowColor: '#000',
    // shadowOffset: {
    //   width: 0,
    //   height: 8,
    // },
    // shadowOpacity: 0.44,
    // shadowRadius: 10.32,

    // elevation: 16,
  },
  headerLogo: {
    width: 112,
    height: 25,
    marginLeft: 10,
  },
  addBtn: {
    backgroundColor: Colors.primaryColor,
    width: 45,
    height: 45,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalTitle: {
    alignItems: 'center',
  },
  modalBody: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalTitleText: {
    fontFamily: 'NunitoSans-Bold',
    marginBottom: 10,
    fontSize: 22,
  },
  modalSaveButton: {
    width: Dimensions.get('window').width * 0.15,
    backgroundColor: Colors.fieldtBgColor,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 8,

    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 1,
  },
  pinBtn: {
    backgroundColor: Colors.lightPrimary,

    width: Dimensions.get('window').width * 0.05,
    height: Dimensions.get('window').width * 0.05,
    marginBottom: 16,
    alignContent: 'center',
    justifyContent: 'center',
    borderColor: Colors.fieldtTxtColor,
    alignItems: 'center',
    borderRadius: 10,
  },
  pinNo: {
    fontSize: 0.28 * (Dimensions.get('window').width * 0.05),
    fontFamily: 'NunitoSans-Bold',
  },
  modalBodyText: {
    flex: 1,
    fontFamily: 'NunitoSans-SemiBold',
    fontSize: 16,
    width: '20%',
  },
  modalView: {
    height: Dimensions.get('window').width * 0.4,
    width: Dimensions.get('window').width * 0.4,
    backgroundColor: Colors.whiteColor,

    borderRadius: 12,
    padding: Dimensions.get('window').width * 0.03,
    paddingHorizontal: Dimensions.get('window').width * 0.015,
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: Colors.modalBgColor,
    alignItems: 'center',
    justifyContent: 'center',
  },
  closeButton: {
    position: 'absolute',
    right: Dimensions.get('window').width * 0.04,
    top: Dimensions.get('window').width * 0.04,

    elevation: 1000,
    zIndex: 1000,
  },
  modalDescText: {
    fontFamily: 'NunitoSans-SemiBold',
    fontSize: 16,
    color: Colors.fieldtTxtColor,
  },
});
export default MoCartScreen;
