import { Text } from "react-native-fast-text";
import React, {
  Component,
  useReducer,
  useState,
  useEffect,
  useRef,
  useCallback,
  useMemo
} from 'react';
import {
  StyleSheet,
  Image,
  View,
  Alert,
  TouchableOpacity,
  Dimensions,
  Switch,
  Modal as ModalComponent,

  KeyboardAvoidingView,
  TextInput,
  ActivityIndicator,
  useWindowDimensions,
} from 'react-native';
import { ModalView as ModalViewComponent } from 'react-native-multiple-modals';
import firestore from '@react-native-firebase/firestore';
import { Collections } from '../constant/firebase';
import Colors from '../constant/Colors';
import SideBar from './SideBar';
import Icon from 'react-native-vector-icons/Feather';
import Ionicons from 'react-native-vector-icons/Ionicons';
import AntDesign from 'react-native-vector-icons/AntDesign';
import Entypo from 'react-native-vector-icons/Entypo';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import EvilIcons from 'react-native-vector-icons/EvilIcons';
import GCalendar from '../assets/svg/GCalendar';
import { FlatList, ScrollView } from 'react-native-gesture-handler';
import API from '../constant/API';
import ApiClient from '../util/ApiClient';
import * as User from '../util/User';
import AsyncStorage from '@react-native-async-storage/async-storage';
import CheckBox from 'react-native-check-box';
import moment, { months } from 'moment';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import Styles from '../constant/Styles';
import { getOrderDiscountInfo, getOrderDiscountInfoInclOrderBased, isTablet, getTransformForModalInsideNavigation, getTransformForScreenInsideNavigation, getCartItemPriceWithoutAddOn, getAddOnChoiceQuantity, getAddOnChoicePrice, logEventAnalytics, } from '../util/common';
import { OutletStore } from '../store/outletStore';
import { MerchantStore } from '../store/merchantStore';
import {
  EMAIL_REPORT_TYPE,
  ORDER_TYPE,
  ORDER_TYPE_PARSED,
  REPORT_SORT_FIELD_TYPE,
  TABLE_PAGE_SIZE_DROPDOWN_LIST,
  EXPAND_TAB_TYPE,
  PRODUCT_PRICE_TYPE,
  UNIT_TYPE_SHORT,
  APP_TYPE,
  ORDER_TYPE_SUB
} from '../constant/common';
import { UserStore } from '../store/userStore';
import Upload from '../assets/svg/Upload';
import Download from '../assets/svg/Download';
import {
  convertArrayToCSV,
  generateEmailReport,
  sortReportDataList,
} from '../util/common';
import RNFetchBlob from 'rn-fetch-blob';
import XLSX from 'xlsx';
import { useKeyboard } from '../hooks';
import 'react-native-get-random-values';
import { v4 as uuidv4 } from 'uuid';
import { CommonStore } from '../store/commonStore';
const { nanoid } = require('nanoid');
const RNFS = require('@dr.pogodin/react-native-fs');
import RNPickerSelect from 'react-native-picker-select';
import AsyncImage from '../components/asyncImage';
import Feather from 'react-native-vector-icons/Feather';
import Tooltip from 'react-native-walkthrough-tooltip';
import { useFocusEffect } from '@react-navigation/native';
import UserIdleWrapper from '../components/userIdleWrapper';
import DropDownPicker from 'react-native-dropdown-picker';
import { ANALYTICS, ANALYTICS_PARSED } from '../constant/analytics';
import { TempStore } from "../store/tempStore";
const ModalView = Platform.OS === 'android' ? ModalComponent : ModalViewComponent;

const ReportSalesTransaction = (props) => {
  const { navigation } = props;

  ///////////////////////////////////////////////////////////

  const [isMounted, setIsMounted] = useState(true);

  useFocusEffect(
    useCallback(() => {
      setIsMounted(true);
      return () => {
        setIsMounted(false);
      };
    }, []),
  );

  ///////////////////////////////////////////////////////////

  const { width: windowWidth, height: windowHeight } = useWindowDimensions();

  const outletSelectDropdownView = CommonStore.useState(
    (s) => s.outletSelectDropdownView,
  );
  const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
  const expandTab = CommonStore.useState((s) => s.expandTab);
  const currPageStack = CommonStore.useState((s) => s.currPageStack);

  // navigation.dangerouslyGetParent().setOptions({
  //     tabBarVisible: false,
  // });

  navigation.setOptions({
    headerLeft: () => (
      <TouchableOpacity
        onPress={() => {
          if (isAlphaUser || true) {
            navigation.navigate('MenuOrderingScreen');

            CommonStore.update((s) => {
              s.currPage = 'MenuOrderingScreen';
              s.currPageStack = [...currPageStack, 'MenuOrderingScreen'];
            });
          }
          else {
            navigation.navigate('Table');

            CommonStore.update((s) => {
              s.currPage = 'Table';
              s.currPageStack = [...currPageStack, 'Table'];
            });
          }
          if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
            CommonStore.update((s) => {
              s.expandTab = EXPAND_TAB_TYPE.OPERATION;
            });
          }

          logEventAnalytics({
            eventName: ANALYTICS.MODULE_REPORT_CHA_C_LOGO,
            eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_CHA_C_LOGO
          })
        }}
        style={styles.headerLeftStyle}>
        <Image
          style={[{
            width: 124,
            height: 26,
          }, switchMerchant ? {
            transform: [
              { scaleX: 0.7 },
              { scaleY: 0.7 }
            ],
          } : {}]}
          resizeMode="contain"
          source={require('../assets/image/logo.png')}
        />
      </TouchableOpacity>
    ),
    headerTitle: () => (
      <View
        style={[
          {
            // justifyContent: Platform.OS === 'android' ? 'center' : 'flex-start',
            // alignItems: Platform.OS === 'android' ? 'center' : 'flex-start',
            // marginRight: Platform.OS === 'ios' ? "27%" : 0,
            // bottom: switchMerchant ? '2%' : 0,
            ...global.getHeaderTitleStyle(),
          },
          // windowWidth >= 768 && switchMerchant
          //   ? { right: windowWidth * 0.1 }
          //   : {},
          // windowWidth <= 768
          //   ? { right: 20 }
          //   : {},
        ]}>
        <Text
          style={{
            fontSize: switchMerchant ? 20 : 24,
            // lineHeight: 25,
            textAlign: 'left',
            alignItems: 'flex-start',
            justifyContent: 'flex-start',
            fontFamily: 'NunitoSans-Bold',
            color: Colors.whiteColor,
            opacity: 1,
            paddingLeft: Platform.OS === 'ios' ? "1%" : 0,
          }}>
          Orders Channel Report
        </Text>
      </View>
    ),
    headerRight: () => (
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}>
        {outletSelectDropdownView()}
        <View
          style={{
            backgroundColor: 'white',
            width: 0.5,
            height: windowHeight * 0.025,
            opacity: 0.8,
            marginHorizontal: 15,
            bottom: -1,
          }} />
        <TouchableOpacity
          onPress={() => {
            if (global.currUserRole === 'admin') {
              navigation.navigate('Setting');
            }

            logEventAnalytics({
              eventName: ANALYTICS.MODULE_REPORT_CHA_C_PROFILE,
              eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_CHA_C_PROFILE
            })
          }}
          style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Text
            style={[{
              fontFamily: 'NunitoSans-SemiBold',
              fontSize: switchMerchant ? 10 : 16,
              color: Colors.secondaryColor,
              marginRight: 15,
            }, switchMerchant ? { width: windowWidth / 8 } : {}]}
            numberOfLines={switchMerchant ? 1 : 1}
          >
            {userName}
          </Text>
          <View
            style={{
              marginRight: 30,
              width: windowHeight * 0.05,
              height: windowHeight * 0.05,
              borderRadius: windowHeight * 0.05 * 0.5,
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: 'white',
            }}>
            <Image
              style={{
                width: windowHeight * 0.035,
                height: windowHeight * 0.035,
                alignSelf: 'center',
              }}
              source={require('../assets/image/profile-pic.jpg')}
            />
          </View>
        </TouchableOpacity>
      </View>
    ),
  });

  const [keyboardHeight] = useKeyboard();
  const [oriList, setOriList] = useState([]);
  const [list, setList] = useState([]);
  const [page, setPage] = useState(0);
  const [name, setName] = useState('Orders Channel');
  const [visible, setVisible] = useState(false);
  const [visible1, setVisible1] = useState(false);
  const [isChecked, setIsChecked] = useState(false);
  const [isChecked1, setIsChecked1] = useState(false);
  const [endDate, setEndDate] = useState(new Date());
  const [startDate, setStartDate] = useState(new Date());
  const [offset, setOffset] = useState(0);
  const [perPage, setPerPage] = useState(10);
  const [pageCount, setPageCount] = useState(0);
  const [detailsPageCount, setDetailsPageCount] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [currentDetailsPage, setCurrentDetailsPage] = useState(1);
  const [pageReturn, setPageReturn] = useState(1);
  const [day, setDay] = useState(false);
  const [pick, setPick] = useState(null);
  const [pick1, setPick1] = useState(null);
  const [search, setSearch] = useState('');
  const [list1, setList1] = useState(true);
  const [searchList, setSearchList] = useState(false);
  const [lists, setLists] = useState([]);

  const [loading, setLoading] = useState(false);
  const [pushPagingToTop, setPushPagingToTop] = useState(false);
  const [switchMerchant, setSwitchMerchant] = useState(
    isTablet() ? false : true,
  );

  const [showDateTimePicker, setShowDateTimePicker] = useState(false);
  const [showDateTimePicker1, setShowDateTimePicker1] = useState(false);
  const [pickerMode, setPickerMode] = useState('datetime');
  const [rev_date, setRev_date] = useState(
    moment().subtract(6, 'days').startOf('day'),
  );
  const [rev_date1, setRev_date1] = useState(
    moment().endOf(Date.now()).endOf('day'),
  );

  const reportOutletShifts = OutletStore.useState((s) => s.reportOutletShifts);
  const reportDisplayType = OutletStore.useState((s) => s.reportDisplayType);

  const historyStartDate = CommonStore.useState(s => s.historyStartDate);
  const historyEndDate = CommonStore.useState(s => s.historyEndDate);

  const [transactionTypeSales, setTransactionTypeSales] = useState([]);

  const [expandDetailsDict, setExpandDetailsDict] = useState({});
  const crmUsers = OutletStore.useState((s) => s.crmUsers);

  // 2023-04-14
  const payoutTransactions = OutletStore.useState(s => s.payoutTransactions.filter(p => p.v >= '3')); // only check for v3
  const payoutTransactionsExtend = OutletStore.useState(s => s.payoutTransactionsExtend.filter(p => p.v >= '3')); // only check for v3

  const ptTimestamp = OutletStore.useState(s => s.ptTimestamp);
  const pteTimestamp = OutletStore.useState(s => s.pteTimestamp);

  const allOutletsUserOrdersDone = TempStore.useState((s) => s.allOutletUserOrderDoneProcessed);
  const allOutletsUserOrdersDoneRaw = OutletStore.useState((s) => s.allOutletsUserOrdersDone,);
  // const [allOutletsUserOrdersDone, setAllOutletsUserOrdersDone] = useState([]);

  // const allOutletsUserOrdersDone = OutletStore.useState(
  //   (s) => s.allOutletsUserOrdersDone,
  // );

  const currOutletId = MerchantStore.useState((s) => s.currOutletId);
  const currOutlet = MerchantStore.useState(s => s.currOutlet);
  const allOutletsRaw = MerchantStore.useState((s) => s.allOutlets);

  const userName = UserStore.useState((s) => s.name);
  const merchantName = MerchantStore.useState((s) => s.name);

  const [exportEmail, setExportEmail] = useState('');
  const [CsvData, setCsvData] = useState([]);

  const [showDetails, setShowDetails] = useState(false);
  const [transactionTypeSalesDetails, setTransactionTypeSalesDetails] =
    useState([]);

  const [selectedItemSummary, setSelectedItemSummary] = useState({});

  const [exportModalVisibility, setExportModalVisibility] = useState(false);

  const [currReportSummarySort, setCurrReportSummarySort] = useState('');
  const [currReportDetailsSort, setCurrReportDetailsSort] = useState('');

  const merchantId = UserStore.useState((s) => s.merchantId);
  const isLoading = CommonStore.useState((s) => s.isLoading);
  const [isCsv, setIsCsv] = useState(false);
  const [isExcel, setIsExcel] = useState(false);

  const [saleTip, setSaleTip] = useState(false);
  const [netSaleTip, setNetSaleTip] = useState(false);

  const [filterAppType, setFilterAppType] = useState([APP_TYPE.WEB_ORDER, APP_TYPE.MERCHANT, APP_TYPE.USER, APP_TYPE.WAITER]);

  const isMasterAccount = UserStore.useState(s => s.isMasterAccount);
  const [allOutlets, setAllOutlets] = useState([]);
  const selectedOutletList = CommonStore.useState(s => s.reportOutletIdList);
  const [outletDropdownList, setOutletDropdownList] = useState([]);
  const [selectedOutletId, setSelectedOutletId] = useState("");

  useEffect(() => {
    setAllOutlets(allOutletsRaw.filter(outlet => {
      if (outlet.uniqueId === currOutletId || isMasterAccount) {
        return true;
      }
      else {
        return false;
      }
    }));
  }, [allOutletsRaw, currOutletId, isMasterAccount]);

  useEffect(() => {
    setOutletDropdownList(
      allOutlets.map((item) => ({
        label: item.name,
        value: item.uniqueId,
      })),
    );
    if (selectedOutletId === "" && allOutlets.length > 0 && currOutletId) {
      setSelectedOutletId(currOutletId);

      // // setSelectedOutletList([currOutletId]);
      // CommonStore.update((s) => {
      //   s.reportOutletIdList = [currOutletId];
      // })
    }
  }, [allOutlets, currOutletId]);

  const [isTableComputing, setIsTableComputing] = useState(true);

  const isTableLoading = useMemo(() => {
    return isTableComputing || global.isLoadingReportData;
  }, [isTableComputing, global.isLoadingReportData]);

  useEffect(() => {
    if (global.reportSalesTransactionTableTimerId) {
      clearTimeout(global.reportSalesTransactionTableTimerId);
    }

    global.reportSalesTransactionTableTimerId = setTimeout(() => {
      if (isMounted) {
        setIsTableComputing(true);

        if (currOutletId !== '' && allOutlets.length > 0) {
          var transactionTypeSalesDict = {
            [ORDER_TYPE.DELIVERY]: {
              summaryId: nanoid(),
              orderType: ORDER_TYPE_PARSED[ORDER_TYPE.DELIVERY],
              totalTransactions: 0,
              totalSales: 0,
              totalDiscount: 0,
              discount: 0,
              tax: 0,
              serviceCharge: 0,
              gp: 0,
              netSales: 0,
              averageNetSales: 0,
              detailsList: [],
            },
            [ORDER_TYPE.PICKUP]: {
              summaryId: nanoid(),
              orderType: ORDER_TYPE_PARSED[ORDER_TYPE.PICKUP],
              totalTransactions: 0,
              totalSales: 0,
              totalDiscount: 0,
              discount: 0,
              tax: 0,
              serviceCharge: 0,
              gp: 0,
              netSales: 0,
              averageNetSales: 0,
              detailsList: [],
            },
            [ORDER_TYPE.DINEIN]: {
              summaryId: nanoid(),
              orderType: ORDER_TYPE_PARSED[ORDER_TYPE.DINEIN],
              totalTransactions: 0,
              totalSales: 0,
              totalDiscount: 0,
              discount: 0,
              tax: 0,
              serviceCharge: 0,
              gp: 0,
              netSales: 0,
              averageNetSales: 0,
              detailsList: [],
            },
            [ORDER_TYPE.PRE_ORDER]: {
              summaryId: nanoid(),
              orderType: ORDER_TYPE_PARSED[ORDER_TYPE.PRE_ORDER],
              totalSales: 0,
              totalTransactions: 0,
              totalDiscount: 0,
              discount: 0,
              tax: 0,
              serviceCharge: 0,
              gp: 0,
              netSales: 0,
              averageNetSales: 0,
              detailsList: [],
            },
            [ORDER_TYPE_SUB.OTHER_DELIVERY]: {
              summaryId: nanoid(),
              orderType: ORDER_TYPE_SUB.OTHER_DELIVERY,
              totalSales: 0,
              totalTransactions: 0,
              totalDiscount: 0,
              discount: 0,
              tax: 0,
              serviceCharge: 0,
              gp: 0,
              netSales: 0,
              averageNetSales: 0,
              detailsList: [],
            },
            //[ORDER_TYPE.TAKEAWAY]: {
            //  summaryId: nanoid(),
            //  orderType: ORDER_TYPE_PARSED[ORDER_TYPE.TAKEAWAY],
            //  totalSales: 0,
            //  totalTransactions: 0,
            //  totalDiscount: 0,
            //  discount: 0,
            //  tax: 0,
            //  serviceCharge: 0,
            //  gp: 0,
            // netSales: 0,
            //  averageNetSales: 0,
            //  detailsList: [],
            //},
          };

          for (var i = 0; i < allOutletsUserOrdersDone.length; i++) {
            if (
              (moment(historyStartDate).isSameOrBefore(
                allOutletsUserOrdersDone[i].createdAt,
              ) &&
                moment(historyEndDate).isAfter(
                  allOutletsUserOrdersDone[i].createdAt
                )) &&
              moment(historyEndDate).isAfter(
                allOutletsUserOrdersDone[i].createdAt
              ) &&
              allOutletsUserOrdersDone[i].finalPrice > 0
              // &&
              // (filterAppType.includes(allOutletsUserOrdersDone[i].appType) || filterAppType.length === 0)
            ) {
              if (allOutletsUserOrdersDone[i].orderType === ORDER_TYPE.PICKUP && allOutletsUserOrdersDone[i].orderTypeSub === ORDER_TYPE_SUB.OTHER_DELIVERY) {
                transactionTypeSalesDict[allOutletsUserOrdersDone[i].orderTypeSub].totalTransactions += 1

                transactionTypeSalesDict[
                  allOutletsUserOrdersDone[i].orderTypeSub
                ].totalSales += (allOutletsUserOrdersDone[i].finalPriceBefore
                  ? allOutletsUserOrdersDone[i].finalPriceBefore
                  : allOutletsUserOrdersDone[i].finalPrice) + getOrderDiscountInfo(allOutletsUserOrdersDone[i]);

                transactionTypeSalesDict[
                  allOutletsUserOrdersDone[i].orderTypeSub
                ].totalDiscount += getOrderDiscountInfoInclOrderBased(allOutletsUserOrdersDone[i]);

                // transactionTypeSalesDict[allOutletsUserOrdersDone[i].orderType].discount += allOutletsUserOrdersDone[i].discount / allOutletsUserOrdersDone[i].totalPrice * 100;
                transactionTypeSalesDict[
                  allOutletsUserOrdersDone[i].orderTypeSub
                ].tax += allOutletsUserOrdersDone[i].tax;

                transactionTypeSalesDict[
                  allOutletsUserOrdersDone[i].orderTypeSub
                ].serviceCharge += allOutletsUserOrdersDone[i].sc || 0;

                transactionTypeSalesDict[
                  allOutletsUserOrdersDone[i].orderTypeSub
                ].gp += 0;

                transactionTypeSalesDict[
                  allOutletsUserOrdersDone[i].orderTypeSub
                ].netSales +=
                  (allOutletsUserOrdersDone[i].finalPriceBefore
                    ? allOutletsUserOrdersDone[i].finalPriceBefore
                    : allOutletsUserOrdersDone[i].finalPrice) -
                  allOutletsUserOrdersDone[i].tax -
                  (allOutletsUserOrdersDone[i].sc
                    ? allOutletsUserOrdersDone[i].sc
                    : 0);

                const calculatedDiscount = getOrderDiscountInfoInclOrderBased(allOutletsUserOrdersDone[i]);

                transactionTypeSalesDict[
                  allOutletsUserOrdersDone[i].orderTypeSub
                ].detailsList.push({
                  // ...allOutletsUserOrdersDone[i],
                  id: allOutletsUserOrdersDone[i].uniqueId,
                  discountPercentage: parseFloat(
                    isFinite(
                      calculatedDiscount /
                      // allOutletsUserOrdersDone[i].totalPrice,
                      (allOutletsUserOrdersDone[i].finalPrice +
                        calculatedDiscount),
                    )
                      ? (calculatedDiscount /
                        // allOutletsUserOrdersDone[i].totalPrice) *
                        (allOutletsUserOrdersDone[i].finalPrice +
                          calculatedDiscount)) *
                      100
                      : 0,
                  ),
                });
              }
              else {
                if (transactionTypeSalesDict) {
                  if (transactionTypeSalesDict[allOutletsUserOrdersDone[i].orderType]) {
                    transactionTypeSalesDict[
                      allOutletsUserOrdersDone[i].orderType
                    ].totalTransactions += 1;

                    transactionTypeSalesDict[
                      allOutletsUserOrdersDone[i].orderType
                    ].totalSales += (allOutletsUserOrdersDone[i].finalPriceBefore
                      ? allOutletsUserOrdersDone[i].finalPriceBefore
                      : allOutletsUserOrdersDone[i].finalPrice) + getOrderDiscountInfo(allOutletsUserOrdersDone[i]);

                    transactionTypeSalesDict[
                      allOutletsUserOrdersDone[i].orderType
                    ].totalDiscount += getOrderDiscountInfoInclOrderBased(allOutletsUserOrdersDone[i]);

                    // transactionTypeSalesDict[allOutletsUserOrdersDone[i].orderType].discount += allOutletsUserOrdersDone[i].discount / allOutletsUserOrdersDone[i].totalPrice * 100;
                    transactionTypeSalesDict[
                      allOutletsUserOrdersDone[i].orderType
                    ].tax += allOutletsUserOrdersDone[i].tax;

                    transactionTypeSalesDict[
                      allOutletsUserOrdersDone[i].orderType
                    ].serviceCharge += allOutletsUserOrdersDone[i].sc || 0;

                    transactionTypeSalesDict[
                      allOutletsUserOrdersDone[i].orderType
                    ].gp += 0;

                    transactionTypeSalesDict[
                      allOutletsUserOrdersDone[i].orderType
                    ].netSales +=
                      (allOutletsUserOrdersDone[i].finalPriceBefore
                        ? allOutletsUserOrdersDone[i].finalPriceBefore
                        : allOutletsUserOrdersDone[i].finalPrice) -
                      allOutletsUserOrdersDone[i].tax -
                      (allOutletsUserOrdersDone[i].sc
                        ? allOutletsUserOrdersDone[i].sc
                        : 0);

                    const calculatedDiscount = getOrderDiscountInfoInclOrderBased(allOutletsUserOrdersDone[i]);

                    // transactionTypeSalesDict[allOutletsUserOrdersDone[i].orderType].detailsList.push(allOutletsUserOrdersDone[i]);
                    transactionTypeSalesDict[
                      allOutletsUserOrdersDone[i].orderType
                    ].detailsList.push({
                      // ...allOutletsUserOrdersDone[i],
                      id: allOutletsUserOrdersDone[i].uniqueId,
                      discountPercentage: parseFloat(
                        isFinite(
                          calculatedDiscount /
                          // allOutletsUserOrdersDone[i].totalPrice,
                          (allOutletsUserOrdersDone[i].finalPrice +
                            calculatedDiscount),
                        )
                          ? (calculatedDiscount /
                            // allOutletsUserOrdersDone[i].totalPrice) *
                            (allOutletsUserOrdersDone[i].finalPrice +
                              calculatedDiscount)) *
                          100
                          : 0,
                      ),
                    });
                  }
                }
              }
            }

            /*  transactionTypeSalesDict[
               allOutletsUserOrdersDone[i].orderType
             ].netSales =
               Math.ceil(
                 transactionTypeSalesDict[allOutletsUserOrdersDone[i].orderType]
                   .netSales *
                 20 -
                 0.05,
               ) / 20; */
          }

          transactionTypeSalesDict[ORDER_TYPE.DELIVERY].discount = isFinite(
            (transactionTypeSalesDict[ORDER_TYPE.DELIVERY].totalDiscount /
              transactionTypeSalesDict[ORDER_TYPE.DELIVERY].totalSales) *
            100,
          )
            ? (transactionTypeSalesDict[ORDER_TYPE.DELIVERY].totalDiscount /
              transactionTypeSalesDict[ORDER_TYPE.DELIVERY].totalSales) *
            100
            : 0;

          transactionTypeSalesDict[ORDER_TYPE.DELIVERY].averageNetSales =
            isFinite(
              transactionTypeSalesDict[ORDER_TYPE.DELIVERY].netSales /
              transactionTypeSalesDict[ORDER_TYPE.DELIVERY].totalTransactions,
            )
              ? transactionTypeSalesDict[ORDER_TYPE.DELIVERY].netSales /
              transactionTypeSalesDict[ORDER_TYPE.DELIVERY].totalTransactions
              : 0;

          transactionTypeSalesDict[ORDER_TYPE.PICKUP].discount = isFinite(
            (transactionTypeSalesDict[ORDER_TYPE.PICKUP].totalDiscount /
              transactionTypeSalesDict[ORDER_TYPE.PICKUP].totalSales) *
            100,
          )
            ? (transactionTypeSalesDict[ORDER_TYPE.PICKUP].totalDiscount /
              transactionTypeSalesDict[ORDER_TYPE.PICKUP].totalSales) *
            100
            : 0;

          transactionTypeSalesDict[ORDER_TYPE.PICKUP].averageNetSales = isFinite(
            transactionTypeSalesDict[ORDER_TYPE.PICKUP].netSales /
            transactionTypeSalesDict[ORDER_TYPE.PICKUP].totalTransactions,
          )
            ? transactionTypeSalesDict[ORDER_TYPE.PICKUP].netSales /
            transactionTypeSalesDict[ORDER_TYPE.PICKUP].totalTransactions
            : 0;

          transactionTypeSalesDict[ORDER_TYPE.DINEIN].discount = isFinite(
            (transactionTypeSalesDict[ORDER_TYPE.DINEIN].totalDiscount /
              transactionTypeSalesDict[ORDER_TYPE.DINEIN].totalSales) *
            100,
          )
            ? (transactionTypeSalesDict[ORDER_TYPE.DINEIN].totalDiscount /
              transactionTypeSalesDict[ORDER_TYPE.DINEIN].totalSales) *
            100
            : 0;

          transactionTypeSalesDict[ORDER_TYPE.DINEIN].averageNetSales = isFinite(
            transactionTypeSalesDict[ORDER_TYPE.DINEIN].netSales /
            transactionTypeSalesDict[ORDER_TYPE.DINEIN].totalTransactions,
          )
            ? transactionTypeSalesDict[ORDER_TYPE.DINEIN].netSales /
            transactionTypeSalesDict[ORDER_TYPE.DINEIN].totalTransactions
            : 0;

          transactionTypeSalesDict[ORDER_TYPE.PRE_ORDER].discount = isFinite(
            (transactionTypeSalesDict[ORDER_TYPE.PRE_ORDER].totalDiscount /
              transactionTypeSalesDict[ORDER_TYPE.PRE_ORDER].totalSales) *
            100,
          )
            ? (transactionTypeSalesDict[ORDER_TYPE.DELIVERY].totalDiscount /
              transactionTypeSalesDict[ORDER_TYPE.PRE_ORDER].totalSales) *
            100
            : 0;

          transactionTypeSalesDict[ORDER_TYPE.PRE_ORDER].averageNetSales =
            isFinite(
              transactionTypeSalesDict[ORDER_TYPE.PRE_ORDER].netSales /
              transactionTypeSalesDict[ORDER_TYPE.PRE_ORDER].totalTransactions,
            )
              ? transactionTypeSalesDict[ORDER_TYPE.DELIVERY].netSales /
              transactionTypeSalesDict[ORDER_TYPE.PRE_ORDER].totalTransactions
              : 0;

          transactionTypeSalesDict[ORDER_TYPE_SUB.OTHER_DELIVERY].discount = isFinite(
            (transactionTypeSalesDict[ORDER_TYPE_SUB.OTHER_DELIVERY].totalDiscount /
              transactionTypeSalesDict[ORDER_TYPE_SUB.OTHER_DELIVERY].totalSales) *
            100,
          )
            ? (transactionTypeSalesDict[ORDER_TYPE_SUB.OTHER_DELIVERY].totalDiscount /
              transactionTypeSalesDict[ORDER_TYPE_SUB.OTHER_DELIVERY].totalSales) *
            100
            : 0;

          transactionTypeSalesDict[ORDER_TYPE_SUB.OTHER_DELIVERY].averageNetSales = isFinite(
            transactionTypeSalesDict[ORDER_TYPE_SUB.OTHER_DELIVERY].netSales /
            transactionTypeSalesDict[ORDER_TYPE_SUB.OTHER_DELIVERY].totalTransactions,
          )
            ? transactionTypeSalesDict[ORDER_TYPE_SUB.OTHER_DELIVERY].netSales /
            transactionTypeSalesDict[ORDER_TYPE_SUB.OTHER_DELIVERY].totalTransactions
            : 0;

          const transactionTypeSalesTemp = Object.entries(
            transactionTypeSalesDict,
          ).map(([key, value]) => ({ ...value }));

          const dummyData = ['Download Test'];

          setState({ CsvData: dummyData });

          setTransactionTypeSales(transactionTypeSalesTemp);

          setCurrentPage(1);
          setPageCount(Math.ceil(transactionTypeSalesTemp.length / perPage));

          setShowDetails(false);
        }

        setIsTableComputing(false);
      }
    }, 2000);
  }, [
    allOutletsUserOrdersDone,
    currOutletId,
    allOutlets,
    historyStartDate,
    historyEndDate,
    perPage,

    isMounted,
    // filterAppType,
  ]);

  useEffect(() => {
    if (showDetails && selectedItemSummary.detailsList) {
      setTransactionTypeSalesDetails(selectedItemSummary.detailsList.map(details => {
        const findOrder = allOutletsUserOrdersDone.find(order => order.uniqueId === details.id);

        return {
          ...findOrder,
          ...details,
        };
      }));
      setPageReturn(currentPage);
      // console.log('currentPage value is');
      // console.log(currentPage);
      setCurrentDetailsPage(1);

      setPageCount(Math.ceil(selectedItemSummary.detailsList.length / perPage));
    }
  }, [showDetails, selectedItemSummary, perPage, filterAppType,]);

  useEffect(() => {
    var allOutletsUserOrdersDoneTemp = [];

    if (isMounted) {
      var currDateTime = moment().valueOf();

      if (
        // global.payoutTransactions.length > 0
        true
      ) {
        for (var j = 0; j < global.payoutTransactions.length; j++) {
          allOutletsUserOrdersDoneTemp = allOutletsUserOrdersDoneTemp.concat(
            (filterAppType && filterAppType.length > 0)
              ?
              (global.payoutTransactions[j].userOrdersFigures ? global.payoutTransactions[j].userOrdersFigures : [])
              :
              (global.payoutTransactions[j].userOrdersFigures ? global.payoutTransactions[j].userOrdersFigures : []).filter((item) =>
                //filterChartItems(item, appliedChartFilterQueriesLineChart),
                (filterAppType.includes(item.appType))
              )
          );
        }

        for (var j = 0; j < global.payoutTransactionsExtend.length; j++) {
          allOutletsUserOrdersDoneTemp = allOutletsUserOrdersDoneTemp.concat(
            (filterAppType && filterAppType.length > 0)
              ?
              (global.payoutTransactionsExtend[j].userOrdersFigures ? global.payoutTransactionsExtend[j].userOrdersFigures : [])
              :
              (global.payoutTransactionsExtend[j].userOrdersFigures ? global.payoutTransactionsExtend[j].userOrdersFigures : []).filter((item) =>
                //filterChartItems(item, appliedChartFilterQueriesLineChart),
                (filterAppType.includes(item.appType))
              )
          );
        }

        const startTime = moment().set({ hour: 0, minute: 0, second: 0 }); // Set the start time to 12:00am
        const endTime = moment().set({ hour: 5, minute: 55, second: 0 }); // Set the end time to 05:55am

        for (var i = 0; i < allOutletsUserOrdersDoneRaw.length; i++) {
          if (
            moment(allOutletsUserOrdersDoneRaw[i].createdAt).isSame(currDateTime, 'day')
            ||
            (
              moment(currDateTime).isBetween(startTime, endTime)
              &&
              moment(currDateTime).add(-1, 'day').isSame(allOutletsUserOrdersDoneRaw[i].createdAt, 'day')
            )
          ) {
            if (filterAppType.includes(allOutletsUserOrdersDoneRaw[i].appType)) {
              if (!allOutletsUserOrdersDoneTemp.find(order => order.uniqueId === allOutletsUserOrdersDoneRaw[i].uniqueId)) {
                allOutletsUserOrdersDoneTemp.push(allOutletsUserOrdersDoneRaw[i]);
              }
            }
          }
        }
      }
      else {
        allOutletsUserOrdersDoneTemp = allOutletsUserOrdersDoneRaw.filter((item) =>
          (filterAppType.includes(item.appType))
        );
      }
    }

    // setAllOutletsUserOrdersDone(allOutletsUserOrdersDoneTemp);
  }, [
    allOutletsUserOrdersDoneRaw,

    // payoutTransactions,
    // payoutTransactionsExtend,

    ptTimestamp,
    pteTimestamp,

    isMounted,
    reportOutletShifts,
    reportDisplayType,

    filterAppType,
  ]);


  // componentDidMount = () => {
  //     moment()
  // }

  const setState = () => { };

  const searchBarItem = () => {
    ApiClient.GET(
      `${API.salesByTransactionChannelSearchBar +
      1
      }&queryName=${search
      }&startDate=${startDate
      }&endDate=${endDate}`,
    ).then((result) => {
      setState({ lists: result });
    });
  };

  /* const email = () => {
        var body = {
            email: '<EMAIL>',
            data: list
        }
        ApiClient.POST(API.emailReportPdf, body, false).then((result) => {
            try {
                if (result != null) {
                    Alert.alert(
                        'Congratulation!',
                        'You Have Successfull',
                        [
                            {
                                text: 'OK',
                                onPress: () => { setState({ visible1: false }) },
                            },
                        ],
                        { cancelable: false },
                    );
                }
            } catch (error) {
                Alert.alert('Failed', [{ text: 'OK', onPress: () => { } }], {
                    cancelable: false,
                });
            }
        })
    }

    const download = () => {
        var body = {
            data: list
        }
        ApiClient.POST(API.generateReportPDF, body, false).then((result) => {
            try {
                if (result != null) {
                    Alert.alert(
                        'Congratulation!',
                        'You Have Successfull',
                        [
                            {
                                text: 'OK',
                                onPress: () => { setState({ visible: false }) },
                            },
                        ],
                        { cancelable: false },
                    );
                }
            } catch (error) {
                Alert.alert('Failed', [{ text: 'OK', onPress: () => { } }], {
                    cancelable: false,
                });
            }
        })
    } */

  const add = async () => {
    if (page + 1 < pageCount) {
      await setState({ page: page + 1, currentPage: currentPage + 1 });
      // console.log(page);
      var e = page;
      next(e);
    }
  };

  const next = (e) => {
    const offset = e * perPage; //10
    setState({ offset });
    loadMoreData();
  };

  const less = async () => {
    if (page > 0) {
      await setState({ page: page - 1, currentPage: currentPage - 1 });
      // console.log(page);
      var y = page;
      pre(y);
    }
  };

  const pre = (y) => {
    const offset = y * perPage;
    setState({ offset });
    loadMoreData();
  };

  const nextPage = () => {
    setCurrentPage(currentPage + 1 > pageCount ? currentPage : currentPage + 1);
  };

  const prevPage = () => {
    setCurrentPage(currentPage - 1 < 1 ? currentPage : currentPage - 1);
  };

  const nextDetailsPage = () => {
    setCurrentDetailsPage(
      currentDetailsPage + 1 > pageCount
        ? currentDetailsPage
        : currentDetailsPage + 1,
    );
  };

  const prevDetailsPage = () => {
    setCurrentDetailsPage(
      currentDetailsPage - 1 < 1 ? currentDetailsPage : currentDetailsPage - 1,
    );
  };

  const loadMoreData = () => {
    const data = oriList;
    const slice = data.slice(offset, offset + perPage); //30
    setState({ list: slice, pageCount: Math.ceil(data.length / perPage) });
  };

  // moment = async () => {
  //     const today = new Date();
  //     const day = new Date(today.getTime() - (7 * 24 * 60 * 60 * 1000));
  //     await setState({ startDate: moment(day).format('YYYY-MM-DD'), endDate: moment(today).format('YYYY-MM-DD') })
  //     getDetail()
  // }

  const getDetail = () => {
    ApiClient.GET(
      `${API.salesByTransactionCategory +
      1
      }&startDate=${startDate
      }&endDate=${endDate}`,
    ).then((result) => {
      var data = result;
      var slice = data.slice(offset, offset + perPage);
      setState({
        list: slice,
        oriList: data,
        pageCount: Math.ceil(data.length / perPage),
      });
    });
  };

  const decimal = (value) => {
    return value.toFixed(2);
  };

  const renderSearchItem = ({ item, index }) =>
    (index + 1) % 2 == 0 ? (
      <View style={{ backgroundColor: Colors.whiteColor, padding: 12 }}>
        <View style={{ flexDirection: 'row' }}>
          <Text
            style={{
              flex: 3,
              fontSize: 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'center',
            }}>
            {item.category == null || item.category == ''
              ? 'DELIVERY'
              : item.category}
          </Text>
          <Text
            style={{
              flex: 3,
              fontSize: 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'center',
            }}>
            {parseFloat(item.finalPrice).toFixed(2)}
          </Text>
          <Text
            style={{
              flex: 3,
              fontSize: 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'center',
            }}>
            {item.count}
          </Text>
          <Text
            style={{
              flex: 2,
              fontSize: 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'center',
            }}>
            {parseFloat(item.discount).toFixed(2)}
          </Text>
          <Text
            style={{
              flex: 2,
              fontSize: 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'center',
            }}>
            {parseFloat(item.discount).toFixed(2)}
          </Text>
          <Text
            style={{
              flex: 2,
              fontSize: 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'center',
            }}>
            {parseFloat(item.tax).toFixed(2)}
          </Text>
          <Text
            style={{
              flex: 2,
              fontSize: 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'center',
            }}>
            {parseFloat(item.serviceCharge).toFixed(2)}
          </Text>
          <Text
            style={{
              flex: 1,
              fontSize: 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'center',
            }}>
            {parseFloat(item.gp).toFixed(2)}
          </Text>
          <Text
            style={{
              flex: 2,
              fontSize: 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'center',
            }}>
            {parseFloat(item.netSale).toFixed(2)}
          </Text>
          <Text
            style={{
              flex: 3,
              fontSize: 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'center',
            }}>
            {parseFloat(item.averageNetSale).toFixed(2)}
          </Text>
        </View>
      </View>
    ) : (
      <View style={{ backgroundColor: Colors.fieldtBgColor, padding: 12 }}>
        <View style={{ flexDirection: 'row' }}>
          <Text
            style={{
              flex: 3,
              fontSize: 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'center',
            }}>
            {item.category == null || item.category == ''
              ? 'DELIVERY'
              : item.category}
          </Text>
          <Text
            style={{
              flex: 3,
              fontSize: 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'center',
            }}>
            {parseFloat(item.finalPrice).toFixed(2)}
          </Text>
          <Text
            style={{
              flex: 3,
              fontSize: 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'center',
            }}>
            {item.count}
          </Text>
          <Text
            style={{
              flex: 2,
              fontSize: 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'center',
            }}>
            {parseFloat(item.discount).toFixed(2)}
          </Text>
          <Text
            style={{
              flex: 2,
              fontSize: 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'center',
            }}>
            {parseFloat(item.discount).toFixed(2)}
          </Text>
          <Text
            style={{
              flex: 2,
              fontSize: 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'center',
            }}>
            {parseFloat(item.tax).toFixed(2)}
          </Text>
          <Text
            style={{
              flex: 2,
              fontSize: 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'center',
            }}>
            {parseFloat(item.serviceCharge).toFixed(2)}
          </Text>
          <Text
            style={{
              flex: 1,
              fontSize: 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'center',
            }}>
            {parseFloat(item.gp).toFixed(2)}
          </Text>
          <Text
            style={{
              flex: 2,
              fontSize: 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'center',
            }}>
            {parseFloat(item.netSale).toFixed(2)}
          </Text>
          <Text
            style={{
              flex: 3,
              fontSize: 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'center',
            }}>
            {parseFloat(item.averageNetSale)
              .toFixed(2)
              .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
          </Text>
        </View>
      </View>
    );

  const onItemSummaryClicked = (item) => {
    // setTransactionTypeSalesDetails(item.detailsList);
    setSelectedItemSummary(item);
    setShowDetails(true);

    // setCurrentPage(1);
    // setPageCount(Math.ceil(item.detailsList.length / perPage));

    // console.log('item.detailsList');
    // console.log(item.detailsList);
  };

  const renderItem = ({ item, index }) => (
    <TouchableOpacity
      onPress={() => onItemSummaryClicked(item)}
      style={{
        backgroundColor:
          (index + 1) % 2 == 0 ? Colors.whiteColor : Colors.highlightColor,
        paddingVertical: 10,
        //paddingHorizontal: 3,
        //paddingLeft: 1,
        borderColor: '#BDBDBD',
        borderTopWidth: (index + 1) % 2 == 0 ? 0 : 0.5,
        borderBottomWidth: (index + 1) % 2 == 0 ? 0 : 0.5,
      }}>
      <View style={{ flexDirection: 'row' }}>
        <Text
          style={{
            width: '6%',
            fontSize: switchMerchant ? 10 : 13,
            fontFamily: 'NunitoSans-Regular',
            textAlign: 'left',
            paddingLeft: 10,
          }}>
          {((currentPage - 1) * perPage) + index + 1}
        </Text>
        <Text
          style={{
            width: '18%',
            fontSize: switchMerchant ? 10 : 13,
            fontFamily: 'NunitoSans-Regular',
            textAlign: 'left',
            paddingLeft: 10,
          }}>
          {item.orderType}
        </Text>
        <Text
          style={{
            width: '10%',
            fontSize: switchMerchant ? 10 : 13,
            fontFamily: 'NunitoSans-Regular',
            textAlign: 'left',
            paddingLeft: 10,
          }}>
          {item.totalTransactions}
        </Text>
        <Text
          style={{
            width: '10%',
            fontSize: switchMerchant ? 10 : 13,
            fontFamily: 'NunitoSans-Regular',
            textAlign: 'left',
            paddingLeft: 10,
          }}>
          {item.totalSales.toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
        </Text>
        <Text
          style={{
            width: '8%',
            fontSize: switchMerchant ? 10 : 13,
            fontFamily: 'NunitoSans-Regular',
            textAlign: 'left',
            paddingLeft: 10,
          }}>
          {item.totalDiscount
            .toFixed(2)
            .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
        </Text>
        <Text
          style={{
            width: '8%',
            fontSize: switchMerchant ? 10 : 13,
            fontFamily: 'NunitoSans-Regular',
            textAlign: 'left',
            paddingLeft: 10,
          }}>
          {(item.totalDiscount != 0
            ? (item.totalDiscount / item.totalSales) * 100
            : 0
          ).toFixed(2)}
        </Text>

        <Text
          style={{
            width: '8%',
            fontSize: switchMerchant ? 10 : 13,
            fontFamily: 'NunitoSans-Regular',
            textAlign: 'left',
            paddingLeft: 10,
          }}>
          {(item.tax || 0).toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
        </Text>
        <Text
          style={{
            width: '10%',
            fontSize: switchMerchant ? 10 : 13,
            fontFamily: 'NunitoSans-Regular',
            textAlign: 'left',
            paddingLeft: 10,
          }}>
          {item.serviceCharge
            .toFixed(2)
            .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
        </Text>
        <Text
          style={{
            width: '10%',
            fontSize: switchMerchant ? 10 : 13,
            fontFamily: 'NunitoSans-Regular',
            textAlign: 'left',
            paddingLeft: 10,
          }}>
          {(item.salesReturn || 0)
            .toFixed(2)
            .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
        </Text>
        {/* <Text style={{ flex: 1, fontSize: 13, fontFamily: 'NunitoSans-Regular', textAlign: 'left' }}>{item.gp}</Text> */}
        <Text
          style={{
            width: '12%',
            fontSize: switchMerchant ? 10 : 13,
            fontFamily: 'NunitoSans-Regular',
            textAlign: 'right',
            paddingRight: 20,
          }}>
          {item.netSales.toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
        </Text>
        {/* <Text style={{ flex: 2, fontSize: 13, fontFamily: 'NunitoSans-Regular', textAlign: 'left' }}>{item.averageNetSales.toFixed(2)}</Text> */}
        {/* <Text
            style={{
              flex: 1.5,
              fontSize: switchMerchant ? 10 : 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'right',
              paddingRight: 20,
            }}>
            {item.averageNetSales
              .toFixed(2)
              .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
          </Text> */}
      </View>
    </TouchableOpacity>
  );

  const onClickItemDetails = async (item) => {
    const userOrderSnapshot = await firestore()
      .collection(Collections.UserOrder)
      .where('uniqueId', '==', item.uniqueId)
      .limit(1)
      .get();

    var userOrder = null;
    if (!userOrderSnapshot.empty) {
      userOrder = userOrderSnapshot.docs[0].data();
    }

    if (userOrder) {
      setExpandDetailsDict({
        ...expandDetailsDict,
        [item.uniqueId]: expandDetailsDict[item.uniqueId] ? false : userOrder,
      });
    }
  };

  const renderItemDetails = ({ item, index }) => {
    var record = null;
    if (item && expandDetailsDict[item.uniqueId] && expandDetailsDict[item.uniqueId].uniqueId) {
      record = expandDetailsDict[item.uniqueId];
    }

    ///////////////////////////

    // console.log('order id');
    // console.log(item.orderId);

    // calculate longest

    var longestStr = 5;

    for (var i = 0; i < item.cartItems.length; i++) {
      const cartItemPriceWIthoutAddOn = getCartItemPriceWithoutAddOn(item.cartItems[i]);

      if (cartItemPriceWIthoutAddOn.toFixed(0).length > longestStr) {
        longestStr = cartItemPriceWIthoutAddOn.toFixed(0).length;
      }

      for (var j = 0; j < item.cartItems[i].addOns.length; j++) {
        if (
          item.cartItems[i].addOns[j].prices.reduce((accumAddOn, priceAddOn) => accumAddOn + priceAddOn, 0).toFixed(0).length > longestStr
        ) {
          longestStr = item.cartItems[i].addOns[j].prices.reduce((accumAddOn, priceAddOn) => accumAddOn + priceAddOn, 0).toFixed(0).length;
        }
      }
    }

    if (item.totalPrice.toFixed(0).length > longestStr) {
      longestStr = item.totalPrice.toFixed(0).length;
    }

    if (item.discount.toFixed(0).length > longestStr) {
      longestStr = item.discount.toFixed(0).length;
    }

    if (item.tax.toFixed(0).length > longestStr) {
      longestStr = item.tax.toFixed(0).length;
    }

    if (item.finalPrice.toFixed(0).length > longestStr) {
      longestStr = item.finalPrice.toFixed(0).length;
    }

    // console.log(longestStr);

    ///////////////////////////

    // calculate spacing

    // var cartItemPriceWIthoutAddOnSpacingList = [];
    // var addOnsSpacingList = [];

    // for (var i = 0; i < item.cartItems.length; i++) {
    //   const cartItemPriceWIthoutAddOn =
    //     item.cartItems[i].price -
    //     item.cartItems[i].addOns.reduce(
    //       (accum, addOn) => accum + addOn.prices.reduce((accumAddOn, priceAddOn) => accumAddOn + priceAddOn, 0),
    //       0,
    //     );

    //   cartItemPriceWIthoutAddOnSpacingList.push(
    //     Math.max(longestStr - cartItemPriceWIthoutAddOn.toFixed(0).length, 0) +
    //     1,
    //   );

    //   for (var j = 0; j < item.cartItems[i].addOns.length; j++) {
    //     addOnsSpacingList.push(
    //       Math.max(
    //         longestStr -
    //         item.cartItems[i].addOns[j].prices.reduce((accumAddOn, priceAddOn) => accumAddOn + priceAddOn, 0).toFixed(0).length,
    //         0,
    //       ) + 1,
    //     );
    //   }
    // }

    var totalPriceSpacing =
      Math.max(longestStr - item.totalPrice.toFixed(0).length, 0) + 1;
    var discountSpacing =
      Math.max(longestStr - item.discount.toFixed(0).length, 0) + 1;
    var taxSpacing = Math.max(longestStr - item.tax.toFixed(0).length, 0) + 1;
    var finalPriceSpacing =
      Math.max(longestStr - item.finalPrice.toFixed(0).length, 0) + 1;

    ///////////////////////////
    return (
      <TouchableOpacity
        onPress={() => onClickItemDetails(item)}
        style={{
          backgroundColor:
            (index + 1) % 2 == 0 ? Colors.whiteColor : Colors.highlightColor,
          paddingVertical: 10,
          //paddingHorizontal: 3,
          //paddingLeft: 1,
          borderColor: '#BDBDBD',
          borderTopWidth: (index + 1) % 2 == 0 ? 0 : 0.5,
          borderBottomWidth: (index + 1) % 2 == 0 ? 0 : 0.5,
        }}>
        <View style={{ flexDirection: 'row' }}>
          <Text
            style={{
              width: '6%',
              fontSize: switchMerchant ? 10 : 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'left',
              paddingLeft: 10,
            }}>
            {((currentDetailsPage - 1) * perPage) + index + 1}
          </Text>
          <Text
            style={{
              width: '12%',
              fontSize: switchMerchant ? 10 : 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'left',
              paddingLeft: 10,
            }}>
            {ORDER_TYPE_PARSED[item.orderType]}
          </Text>

          <View style={{ width: '18%' }}>
            <Text
              style={{
                fontSize: switchMerchant ? 10 : 13,
                fontFamily: 'NunitoSans-Regular',
                textAlign: 'left',
                paddingLeft: 10,
              }}>
              {moment(item.createdAt).format('DD MMM YY hh:mm A')}
            </Text>
          </View>
          <Text
            style={{
              width: '10%',
              fontSize: switchMerchant ? 10 : 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'left',
              paddingLeft: 10,
            }}>
            {((item.finalPriceBefore ? item.finalPriceBefore : item.finalPrice) + getOrderDiscountInfo(item))
              .toFixed(2)
              .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
          </Text>
          <Text
            style={{
              width: '8%',
              fontSize: switchMerchant ? 10 : 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'left',
              paddingLeft: 10,
            }}>
            {(getOrderDiscountInfoInclOrderBased(item)).toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
          </Text>
          <Text
            style={{
              width: '8%',
              fontSize: switchMerchant ? 10 : 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'left',
              paddingLeft: 10,
            }}>
            {parseFloat(item.discountPercentage)
              .toFixed(2)
              .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
          </Text>
          <Text
            style={{
              width: '8%',
              fontSize: switchMerchant ? 10 : 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'left',
              paddingLeft: 10,
            }}>
            {item.tax.toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
          </Text>
          <Text
            style={{
              width: '9%',
              fontSize: switchMerchant ? 10 : 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'left',
              paddingLeft: 10,
            }}>
            {parseFloat(item.sc || 0)
              .toFixed(2)
              .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
          </Text>
          <Text
            style={{
              width: '9%',
              fontSize: switchMerchant ? 10 : 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'left',
              paddingLeft: 10,
            }}>
            {(item.salesReturn || 0)
              .toFixed(2)
              .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
          </Text>
          {/* <Text style={{ flex: 1.6, fontSize: 13, fontFamily: 'NunitoSans-Regular', textAlign: 'left' }}>{item.discountPercentage ? item.discountPercentage.toFixed(2) : '0.00'}</Text> */}

          {/* <Text style={{ flex: 2, fontSize: 13, fontFamily: 'NunitoSans-Regular', textAlign: 'left' }}>{item.serviceCharge.toFixed(2)}</Text> */}
          {/* <Text style={{ flex: 1, fontSize: 13, fontFamily: 'NunitoSans-Regular', textAlign: 'left' }}>{(0).toFixed(2)}</Text> */}
          <View
            style={{
              width: '12%',
              flexDirection: 'row',
              justifyContent: 'space-between',
              paddingLeft: 10,
              paddingRight: switchMerchant ? '2.1%' : '1.8%',
            }}>
            <Text style={{}} />
            <Text
              style={{
                fontSize: switchMerchant ? 10 : 13,
                fontFamily: 'NunitoSans-Regular',
              }}>
              {/* <Text style={{
                            opacity: 0,
                            ...Platform.OS === 'android' && {
                                color: 'transparent',
                            },
                        }}>
                            {'0'.repeat((finalPriceSpacing * 0.6) + (item.finalPrice.toFixed(0).length === 1 ? 1 : 0))}
                        </Text> */}
              {(item.finalPriceBefore != 0 || item.finalPrice != 0
                ? (item.finalPriceBefore
                  ? item.finalPriceBefore
                  : item.finalPrice) -
                item.tax -
                (item.sc || 0)
                : 0
              )
                .toFixed(2)
                .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
            </Text>
          </View>
        </View>

        {expandDetailsDict[item.uniqueId] ? (
          <View
            style={{
              minheight: windowHeight * 0.35,
              marginTop: 30,
              paddingBottom: 20,
            }}>
            {record.cartItems.map((cartItem, index) => {
              const cartItemPriceWIthoutAddOn = getCartItemPriceWithoutAddOn(cartItem);

              return (
                <View
                  style={{
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}>
                  <View
                    style={{
                      width: '100%',
                      alignItems: 'flex-start',
                      flexDirection: 'row',
                      marginBottom: Platform.OS == 'ios' ? 10 : 10,
                      minHeight: 80,
                      //backgroundColor: 'yellow',
                    }}>
                    <View
                      style={{
                        flexDirection: 'row',
                        width: '100%',
                        //backgroundColor: 'blue',
                      }}>
                      {index == 0 ? (
                        <View
                          style={{
                            marginHorizontal: 1,
                            width: Platform.OS == 'ios' ? '8%' : '8%',
                            //justifyContent: 'center',
                            alignItems: 'center',
                            //backgroundColor: 'blue',
                          }}>
                          <TouchableOpacity
                            style={{
                              alignItems: 'center',
                              marginTop: 0,
                            }}
                            onPress={() => {
                              var crmUser = null;

                              if (record.crmUserId !== undefined) {
                                for (var i = 0; i < crmUsers.length; i++) {
                                  if (record.crmUserId === crmUsers[i].uniqueId) {
                                    crmUser = crmUsers[i];
                                    break;
                                  }
                                }
                              }

                              if (!crmUser) {
                                for (var i = 0; i < crmUsers.length; i++) {
                                  if (record.userId === crmUsers[i].firebaseUid) {
                                    crmUser = crmUsers[i];
                                    break;
                                  }
                                }
                              }

                              if (crmUser) {
                                CommonStore.update(
                                  (s) => {
                                    s.selectedCustomerEdit = crmUser;
                                    // s.selectedCustomerEdit = userReservations[item.userId] && crmUsers[item.userId] ? crmUsers[item.userId] : null ;

                                    s.routeParams = {
                                      pageFrom: 'Reservation',
                                    };
                                  },
                                  () => {
                                    navigation.navigate('NewCustomer');
                                  },
                                );
                              }
                            }}>
                            <Image
                              style={{
                                width: switchMerchant ? 30 : 60,
                                height: switchMerchant ? 30 : 60,
                              }}
                              resizeMode="contain"
                              source={require('../assets/image/default-profile.png')}
                            />

                            <View
                              style={{
                                alignItems: 'center',
                                justifyContent: 'center',
                              }}>
                              <Text
                                style={[
                                  {
                                    fontFamily: 'NunitoSans-Bold',
                                    marginTop: 0,
                                    fontSize: 13,
                                    textAlign: 'center',
                                  },
                                  switchMerchant
                                    ? {
                                      fontFamily: 'NunitoSans-Bold',
                                      marginTop: 0,
                                      fontSize: 10,
                                      textAlign: 'center',
                                    }
                                    : {},
                                ]}
                                numberOfLines={1}>
                                {record.userName ? record.userName : 'Guest'}
                              </Text>
                            </View>
                          </TouchableOpacity>
                        </View>
                      ) : (
                        <View
                          style={{
                            marginHorizontal: 1,
                            width: Platform.OS == 'ios' ? '8%' : '8%',
                            justifyContent: 'center',
                            alignItems: 'center',
                          }}
                        />
                      )}

                      <View
                        style={{
                          // flex: 0.3,
                          width: '5%',
                          //justifyContent: 'center',
                          alignItems: 'center',
                          //backgroundColor: 'red',
                          //paddingLeft: '1.2%',
                        }}>
                        <Text
                          style={[
                            {
                              fontFamily: 'NunitoSans-Bold',
                              fontSize: 13,
                            },
                            switchMerchant
                              ? {
                                fontFamily: 'NunitoSans-Bold',
                                fontSize: 10,
                              }
                              : {},
                          ]}>
                          {index + 1}.
                        </Text>
                      </View>

                      <View
                        style={{
                          //flex: 0.5,
                          width: '10%',
                          //backgroundColor: 'green',
                          alignItems: 'center',
                        }}>
                        {cartItem.image ? (
                          <AsyncImage
                            source={{ uri: cartItem.image }}
                            // item={cartItem}
                            style={{
                              width: switchMerchant ? 30 : 60,
                              height: switchMerchant ? 30 : 60,
                              borderWidth: 1,
                              borderColor: '#E5E5E5',
                              borderRadius: 5,
                            }}
                          />
                        ) : (
                          <View
                            style={{
                              justifyContent: 'center',
                              alignItems: 'center',
                              width: switchMerchant ? 30 : 60,
                              height: switchMerchant ? 30 : 60,
                              borderWidth: 1,
                              borderColor: '#E5E5E5',
                              borderRadius: 5,
                            }}>
                            <Ionicons
                              name="fast-food-outline"
                              size={switchMerchant ? 25 : 35}
                            />
                          </View>
                        )}
                      </View>
                      <View style={{ width: '75%' }}>
                        <View
                          style={{
                            marginLeft: Platform.OS == 'ios' ? 14 : 14,
                            marginBottom: 10,
                            //backgroundColor: 'blue',
                            width: '100%',
                            flexDirection: 'row',
                          }}>
                          <View style={{ width: '69%' }}>
                            <Text
                              style={[
                                {
                                  fontFamily: 'NunitoSans-Bold',
                                  fontSize: 13,
                                },
                                switchMerchant
                                  ? {
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: 10,
                                  }
                                  : {},
                              ]}>
                              {cartItem.name}{cartItem.priceType === PRODUCT_PRICE_TYPE.UNIT ? ` (${UNIT_TYPE_SHORT[cartItem.unitType]})` : ''}
                            </Text>
                          </View>

                          <View
                            style={{
                              width: '13%',
                            }}>
                            <View
                              style={{
                                alignItems: 'center',
                                //backgroundColor: 'yellow',
                              }}>
                              <Text
                                style={[
                                  {
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: 13,
                                  },
                                  // Platform.OS === 'android'
                                  //   ? {
                                  //       width: '200%',
                                  //     }
                                  //   : {},
                                  switchMerchant
                                    ? {
                                      fontFamily: 'NunitoSans-Bold',
                                      fontSize: 10,
                                    }
                                    : {},
                                ]}>
                                x{cartItem.quantity}
                              </Text>
                            </View>
                          </View>
                          <View
                            style={{
                              flexDirection: 'row',
                              justifyContent: 'space-between',
                              width: '18.75%',
                            }}>
                            <Text
                              style={
                                switchMerchant
                                  ? {
                                    fontSize: 10,
                                  }
                                  : { fontSize: 13 }
                              }>
                              RM
                            </Text>
                            <Text
                              style={
                                switchMerchant
                                  ? {
                                    fontSize: 10,
                                    paddingRight: 20,
                                    fontFamily: 'NunitoSans-Regular',
                                  }
                                  : {
                                    fontSize: 13,
                                    paddingRight: 20,
                                    fontFamily: 'NunitoSans-Regular',
                                  }
                              }>
                              {cartItemPriceWIthoutAddOn
                                .toFixed(2)
                                .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
                            </Text>
                          </View>
                        </View>

                        {cartItem.remarks && cartItem.remarks.length > 0 ? (
                          <View
                            style={{
                              alignItems: 'center',
                              flexDirection: 'row',
                              marginLeft: Platform.OS == 'ios' ? 14 : 14,
                            }}>
                            <View style={{ justifyContent: 'center' }}>
                              <Text
                                style={[
                                  {
                                    fontFamily: 'NunitoSans-SemiBold',
                                    fontSize: 13,
                                  },
                                  switchMerchant
                                    ? {
                                      fontFamily: 'NunitoSans-SemiBold',
                                      fontSize: 10,
                                    }
                                    : {},
                                ]}>
                                {cartItem.remarks}
                              </Text>
                            </View>
                          </View>
                        ) : (
                          <></>
                        )}

                        {cartItem.addOns.map((addOnChoice, i) => {
                          const addOnChoices = addOnChoice.choiceNames.join(", ");
                          return (
                            <View
                              style={{
                                flexDirection: 'row',
                                // marginLeft: -5,
                                width: '100%',
                              }}>
                              <View
                                style={{
                                  width: '69%',
                                  flexDirection: 'row',
                                  marginLeft: Platform.OS == 'ios' ? 14 : 14,
                                }}>
                                <Text
                                  style={[
                                    {
                                      fontFamily: 'NunitoSans-Bold',
                                      fontSize: 13,
                                      color: Colors.descriptionColor,
                                      width: '25%',
                                      // marginLeft: 5,
                                    },
                                    switchMerchant
                                      ? {
                                        fontFamily: 'NunitoSans-Bold',
                                        fontSize: 10,
                                        color: Colors.descriptionColor,
                                        width: '25%',
                                      }
                                      : {},
                                  ]}>
                                  {`${addOnChoice.name}:`}
                                </Text>
                                <Text
                                  style={[
                                    {
                                      fontFamily: 'NunitoSans-Bold',
                                      fontSize: 13,
                                      color: Colors.descriptionColor,
                                      width: '75%',
                                      // marginLeft: 5,
                                    },
                                    switchMerchant
                                      ? {
                                        fontFamily: 'NunitoSans-Bold',
                                        fontSize: 10,
                                        color: Colors.descriptionColor,
                                        width: '75%',
                                      }
                                      : {},
                                  ]}>
                                  {`${addOnChoices}`}
                                </Text>
                              </View>

                              <View
                                style={[
                                  {
                                    width: '13%',
                                    flexDirection: 'row',
                                    justifyContent: 'center',
                                    //backgroundColor: 'blue',
                                  },
                                  switchMerchant
                                    ? {
                                      width: '13%',
                                      flexDirection: 'row',
                                      justifyContent: 'center',
                                    }
                                    : {},
                                ]}>
                                <Text
                                  style={[
                                    {
                                      fontFamily: 'NunitoSans-Bold',
                                      fontSize: 13,
                                      color: Colors.descriptionColor,
                                      width: '28%',
                                      // right: 38,
                                      //backgroundColor: 'green',
                                      textAlign: 'center',
                                    },
                                    switchMerchant
                                      ? {
                                        fontFamily: 'NunitoSans-Bold',
                                        fontSize: 10,
                                        color: Colors.descriptionColor,
                                        width: '28%',
                                        textAlign: 'center',
                                      }
                                      : {},
                                  ]}>
                                  {`${addOnChoice.quantities
                                    ? `x${getAddOnChoiceQuantity(addOnChoice, cartItem)}`
                                    : ''
                                    }`}
                                </Text>
                              </View>

                              <View
                                style={{
                                  flexDirection: 'row',
                                  justifyContent: 'space-between',
                                  width: '18.75%',
                                  alignItems: 'center',
                                }}>
                                <Text
                                  style={[
                                    switchMerchant
                                      ? {
                                        color: Colors.descriptionColor,
                                        fontSize: 10,
                                      }
                                      : {
                                        color: Colors.descriptionColor,
                                        fontSize: 13,
                                      },
                                  ]}>
                                  RM
                                </Text>
                                <Text
                                  style={
                                    switchMerchant
                                      ? {
                                        color: Colors.descriptionColor,
                                        paddingRight: 20,
                                        fontSize: 10,
                                        fontFamily: 'NunitoSans-Regular',
                                      }
                                      : {
                                        color: Colors.descriptionColor,
                                        paddingRight: 20,
                                        fontSize: 13,
                                        fontFamily: 'NunitoSans-Regular',
                                      }
                                  }>
                                  {(getAddOnChoicePrice(addOnChoice, cartItem))
                                    .toFixed(2)
                                    .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
                                </Text>
                              </View>
                            </View>
                          );
                        })}
                      </View>
                    </View>
                  </View>
                  <View style={{ flexDirection: 'row', width: '100%' }}>
                    <View style={{ width: '70%' }} />
                    <View style={{ width: 15 }} />
                    {index === record.cartItems.length - 1 ? (
                      <View
                        style={{
                          flexDirection: 'row',
                          //backgroundColor: 'yellow',
                          width: '28.65%',
                        }}>
                        <View
                          style={{
                            justifyContent: 'center',
                            width: '100%',
                          }}>
                          <View
                            style={{
                              flexDirection: 'row',
                            }}>
                            <Text
                              style={
                                switchMerchant
                                  ? {
                                    fontSize: 10,
                                    width: '50.9%',
                                    fontFamily: 'Nunitosans-Bold',
                                  }
                                  : {
                                    fontSize: 13,
                                    width: '50.9%',
                                    fontFamily: 'Nunitosans-Bold',
                                  }
                              }>
                              Subtotal:
                            </Text>
                            <View
                              style={{
                                flexDirection: 'row',
                                justifyContent: 'space-between',
                                width: '49.1%',
                              }}>
                              <Text
                                style={
                                  switchMerchant
                                    ? { fontSize: 10 }
                                    : { fontSize: 13 }
                                }>
                                RM
                              </Text>
                              <Text
                                style={
                                  switchMerchant
                                    ? {
                                      fontSize: 10,
                                      paddingRight: 20,
                                      fontFamily: 'NunitoSans-Regular',
                                    }
                                    : {
                                      fontSize: 13,
                                      paddingRight: 20,
                                      fontFamily: 'NunitoSans-Regular',
                                    }
                                }>
                                {((record.isRefundOrder && record.finalPrice <= 0)
                                  ? 0
                                  : record.totalPrice +
                                  getOrderDiscountInfo(record)
                                )
                                  .toFixed(2)
                                  .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
                              </Text>
                            </View>
                          </View>

                          {cartItem.orderType === ORDER_TYPE.DELIVERY ? (
                            <View
                              style={{
                                flexDirection: 'row',
                              }}>
                              <Text
                                style={
                                  switchMerchant
                                    ? {
                                      fontSize: 10,
                                      width: '50.9%',
                                      fontFamily: 'Nunitosans-Bold',
                                    }
                                    : {
                                      fontSize: 13,
                                      width: '50.9%',
                                      fontFamily: 'Nunitosans-Bold',
                                    }
                                }>
                                Delivery Fee:
                              </Text>
                              <View
                                style={{
                                  flexDirection: 'row',
                                  justifyContent: 'space-between',
                                  width: '49.1%',
                                }}>
                                <Text
                                  style={
                                    switchMerchant
                                      ? { fontSize: 10 }
                                      : { fontSize: 13 }
                                  }>
                                  RM
                                </Text>
                                <Text
                                  style={
                                    switchMerchant
                                      ? {
                                        fontSize: 10,
                                        paddingRight: 20,
                                        fontFamily: 'NunitoSans-Regular',
                                      }
                                      : {
                                        fontSize: 13,
                                        paddingRight: 20,
                                        fontFamily: 'NunitoSans-Regular',
                                      }
                                  }>
                                  {record.deliveryFee
                                    .toFixed(2)
                                    .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
                                </Text>
                              </View>
                            </View>
                          ) : (
                            <></>
                          )}

                          <View
                            style={{
                              flexDirection: 'row',
                            }}>
                            <Text
                              style={
                                switchMerchant
                                  ? {
                                    fontSize: 10,
                                    width: '50.9%',
                                    fontFamily: 'Nunitosans-Bold',
                                  }
                                  : {
                                    fontSize: 13,
                                    width: '50.9%',
                                    fontFamily: 'Nunitosans-Bold',
                                  }
                              }>
                              Discount:
                            </Text>
                            <View
                              style={{
                                flexDirection: 'row',
                                justifyContent: 'space-between',
                                width: '49.1%',
                              }}>
                              <Text
                                style={
                                  switchMerchant
                                    ? { fontSize: 10 }
                                    : { fontSize: 13 }
                                }>
                                RM
                              </Text>
                              <Text
                                style={
                                  switchMerchant
                                    ? {
                                      fontSize: 10,
                                      paddingRight: 20,
                                      fontFamily: 'NunitoSans-Regular',
                                    }
                                    : {
                                      fontSize: 13,
                                      paddingRight: 20,
                                      fontFamily: 'NunitoSans-Regular',
                                    }
                                }>
                                {' '}
                                {((record.isRefundOrder && record.finalPrice <= 0)
                                  ? 0
                                  :
                                  // record.discount +
                                  getOrderDiscountInfoInclOrderBased(record)
                                )
                                  .toFixed(2)
                                  .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
                              </Text>
                            </View>
                          </View>

                          <View
                            style={{
                              flexDirection: 'row',
                            }}>
                            <Text
                              style={
                                switchMerchant
                                  ? {
                                    fontSize: 10,
                                    width: '50.9%',
                                    fontFamily: 'Nunitosans-Bold',
                                  }
                                  : {
                                    fontSize: 13,
                                    width: '50.9%',
                                    fontFamily: 'Nunitosans-Bold',
                                  }
                              }>
                              Tax:
                            </Text>
                            <View
                              style={{
                                flexDirection: 'row',
                                justifyContent: 'space-between',
                                width: '49.1%',
                              }}>
                              <Text
                                style={
                                  switchMerchant
                                    ? {
                                      fontSize: 10,
                                      paddingRight: 20,
                                    }
                                    : { fontSize: 13, paddingRight: 20 }
                                }>
                                RM
                              </Text>
                              <Text
                                style={
                                  switchMerchant
                                    ? {
                                      fontSize: 10,
                                      paddingRight: 20,
                                      fontFamily: 'NunitoSans-Regular',
                                    }
                                    : {
                                      fontSize: 13,
                                      paddingRight: 20,
                                      fontFamily: 'NunitoSans-Regular',
                                    }
                                }>
                                {record.tax
                                  .toFixed(2)
                                  .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
                              </Text>
                            </View>
                          </View>

                          <View
                            style={{
                              flexDirection: 'row',
                            }}>
                            <Text
                              style={
                                switchMerchant
                                  ? {
                                    fontSize: 10,
                                    width: '50.85%',
                                    fontFamily: 'Nunitosans-Bold',
                                  }
                                  : {
                                    fontSize: 13,
                                    width: '50.85%',
                                    fontFamily: 'Nunitosans-Bold',
                                  }
                              }>
                              Service Charge:
                            </Text>
                            <View
                              style={{
                                flexDirection: 'row',
                                justifyContent: 'space-between',
                                width: switchMerchant ? '49.15%' : '49.1%',
                              }}>
                              <Text
                                style={
                                  switchMerchant
                                    ? {
                                      fontSize: 10,
                                    }
                                    : { fontSize: 13 }
                                }>
                                RM
                              </Text>
                              <Text
                                style={
                                  switchMerchant
                                    ? {
                                      fontSize: 10,
                                      paddingRight: 20,
                                      fontFamily: 'NunitoSans-Regular',
                                    }
                                    : {
                                      fontSize: 13,
                                      paddingRight: 20,
                                      fontFamily: 'NunitoSans-Regular',
                                    }
                                }>
                                {(record.sc || 0)
                                  .toFixed(2)
                                  .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
                              </Text>
                            </View>
                          </View>
                          <View
                            style={{
                              flexDirection: 'row',
                            }}>
                            <Text
                              style={
                                switchMerchant
                                  ? {
                                    fontSize: 10,
                                    width: '50.9%',
                                    fontFamily: 'Nunitosans-Bold',
                                  }
                                  : {
                                    fontSize: 13,
                                    width: '50.9%',
                                    fontFamily: 'Nunitosans-Bold',
                                  }
                              }>
                              Rounding:
                            </Text>
                            <View
                              style={{
                                flexDirection: 'row',
                                justifyContent: 'space-between',
                                width: '49.1%',
                              }}>
                              <Text
                                style={
                                  switchMerchant
                                    ? { fontSize: 10 }
                                    : { fontSize: 13 }
                                }>
                                RM
                              </Text>
                              <Text
                                style={
                                  switchMerchant
                                    ? {
                                      fontSize: 10,
                                      paddingRight: 20,
                                      fontFamily: 'NunitoSans-Regular',
                                    }
                                    : {
                                      fontSize: 13,
                                      paddingRight: 20,
                                      fontFamily: 'NunitoSans-Regular',
                                    }
                                }>
                                {(record.finalPrice
                                  ? record.finalPrice - record.finalPriceBefore
                                  : 0
                                )
                                  .toFixed(2)
                                  .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
                              </Text>
                            </View>
                          </View>

                          <View
                            style={{
                              flexDirection: 'row',
                            }}>
                            <Text
                              style={
                                switchMerchant
                                  ? {
                                    fontSize: 10,
                                    width: '50.9%',
                                    fontFamily: 'Nunitosans-Bold',
                                  }
                                  : {
                                    fontSize: 13,
                                    width: '50.9%',
                                    fontFamily: 'Nunitosans-Bold',
                                  }
                              }>
                              Total:
                            </Text>
                            <View
                              style={{
                                flexDirection: 'row',
                                justifyContent: 'space-between',
                                width: '49.1%',
                              }}>
                              <Text
                                style={
                                  switchMerchant
                                    ? { fontSize: 10 }
                                    : { fontSize: 13 }
                                }>
                                RM
                              </Text>
                              <Text
                                style={
                                  switchMerchant
                                    ? {
                                      fontSize: 10,
                                      paddingRight: 20,
                                      fontFamily: 'NunitoSans-Regular',
                                    }
                                    : {
                                      fontSize: 13,
                                      paddingRight: 20,
                                      fontFamily: 'NunitoSans-Regular',
                                    }
                                }>
                                {record.finalPrice
                                  .toFixed(2)
                                  .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
                              </Text>
                            </View>
                          </View>
                        </View>
                      </View>
                    ) : (
                      <></>
                    )}
                  </View>

                  {/* <View style={{alignItems:'flex-end'}}>
                        <View style={{ flexDirection: 'row' }}>
                          <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: 16 }}>Subtotal: {(Math.ceil(cartItem.price * 20-0.05) /20).toFixed(2)}</Text>
                        </View>
                      </View> */}
                  {/* {(cartItem.remarks && cartItem.remarks.length > 0) ?
                      <View style={{ alignItems: 'center', flexDirection: 'row' }}>
                        
                        <View style={{ flex: 1, justifyContent: 'center', }}>
                          <Text style={{ fontFamily: 'NunitoSans-SemiBold', fontSize: 15 }}>{cartItem.remarks}</Text>
                        </View>
                        
                      </View>
                      : <></>
                    } */}
                </View>
              );
            })}
          </View>
        ) : null}
      </TouchableOpacity>
    );
  };

  const downloadCsv = () => {
    //if (productSales && productSales.dataSource && productSales.dataSource.data) {
    //const csvData = convertArrayToCSV(productSales.dataSource.data);
    const csvData = convertArrayToCSV(CsvData);

    const pathToWrite = `${RNFetchBlob.fs.dirs.DownloadDir
      }/koodoo-report-Product-Sales${moment().format('YYYY-MM-DD-HH-mm-ss')}.csv`;
    // console.log('PATH', pathToWrite);
    RNFetchBlob.fs
      .writeFile(pathToWrite, csvData, 'utf8')
      .then(() => {
        // console.log(`wrote file ${pathToWrite}`);
        // wrote file /storage/emulated/0/Download/data.csv
        Alert.alert(
          'Success',
          `Send to ${pathToWrite}`,
          [{ text: 'OK', onPress: () => { } }],
          { cancelable: false },
        );
      })
      .catch((error) => console.error(error));
    //}
  };

  const convertDataToExcelFormat = () => {
    var excelData = [];

    if (!showDetails) {
      for (var i = 0; i < transactionTypeSales.length; i++) {
        var excelRow = {
          'Transaction Category': transactionTypeSales[i].orderType,
          'Order (Qty)': transactionTypeSales[i].totalTransactions,
          'Sales (RM)': +parseFloat(
            transactionTypeSales[i].totalSales,
          ).toFixed(2),
          'Disc (RM)': +parseFloat(
            transactionTypeSales[i].totalDiscount,
          ).toFixed(2),
          'Disc (%)': +parseFloat(transactionTypeSales[i].totalDiscount != 0
            ? (transactionTypeSales[i].totalDiscount / transactionTypeSales[i].totalSales) * 100
            : 0
          ).toFixed(
            2,
          ),
          'Tax (RM)': +parseFloat(transactionTypeSales[i].tax).toFixed(2),
          'Service Charge (RM)': +parseFloat(
            transactionTypeSales[i].serviceCharge,
          ).toFixed(2),
          'Sales Return (RM)': +parseFloat(transactionTypeSales[i].salesReturn || 0).toFixed(2),
          //'GP (%)': parseFloat(transactionTypeSales[i].gp).toFixed(2),
          'Net Sales (RM)': +parseFloat(
            transactionTypeSales[i].netSales,
          ).toFixed(2),
          // 'Average Net Sales (RM)': +parseFloat(
          //   transactionTypeSales[i].averageNetSales,
          // ).toFixed(2),
        };

        excelData.push(excelRow);
      }
    } else {
      for (var i = 0; i < transactionTypeSalesDetails.length; i++) {
        const calculatedDiscount = getOrderDiscountInfoInclOrderBased(transactionTypeSalesDetails[i]);

        var excelRow = {
          'Transaction Category':
            ORDER_TYPE_PARSED[transactionTypeSalesDetails[i].orderType],
          'Transaction Date': moment(
            transactionTypeSalesDetails[i].createdAt,
          ).format('DD MMM YY hh:mm A'),
          'Sales (RM)': +parseFloat((transactionTypeSalesDetails[i].finalPriceBefore ? transactionTypeSalesDetails[i].finalPriceBefore : transactionTypeSalesDetails[i].finalPrice) + getOrderDiscountInfo(transactionTypeSalesDetails[i])).toFixed(2),
          'Disc (RM)': +parseFloat(
            getOrderDiscountInfoInclOrderBased(transactionTypeSalesDetails[i]),
          ).toFixed(2),
          'Disc (%)': +parseFloat(transactionTypeSalesDetails[i].discountPercentage).toFixed(2),
          'Tax (RM)': +parseFloat(transactionTypeSalesDetails[i].tax).toFixed(
            2,
          ),
          'Service Charge (RM)': +parseFloat(transactionTypeSalesDetails[i].sc || 0)
            .toFixed(2),
          'Sales Return (RM)': +parseFloat(transactionTypeSalesDetails[i].salesReturn || 0),
          //'Tax (RM)': parseFloat(0).toFixed(2),
          //'GP (%)': parseFloat(0).toFixed(2),
          'Net Sales (RM)': +parseFloat(transactionTypeSalesDetails[i].finalPriceBefore != 0 || transactionTypeSalesDetails[i].finalPrice != 0
            ? (transactionTypeSalesDetails[i].finalPriceBefore
              ? transactionTypeSalesDetails[i].finalPriceBefore
              : transactionTypeSalesDetails[i].finalPrice) -
            transactionTypeSalesDetails[i].tax -
            (transactionTypeSalesDetails[i].sc || 0)
            : 0
          ).toFixed(2),
        };

        excelData.push(excelRow);
      }
    }

    // console.log('excelData');
    // console.log(excelData);

    return excelData;
  };

  const convertDataToCSVFormat = () => {
    var csvData = [];

    if (!showDetails) {
      csvData.push(
        `Transaction Category,Order (Qty),Sales (RM),Disc (RM),Disc (%),Tax (RM),Service Charge (RM),Sales Return (RM),Net Sales (RM)`,
      );

      for (var i = 0; i < transactionTypeSales.length; i++) {
        var csvRow = `${transactionTypeSales[i].orderType},${transactionTypeSales[i].totalTransactions
          },${transactionTypeSales[i].totalSales.toFixed(2)},${+parseFloat(
            transactionTypeSales[i].totalDiscount,
          ).toFixed(2)},${(transactionTypeSales[i].totalDiscount != 0
            ? (transactionTypeSales[i].totalDiscount /
              transactionTypeSales[i].totalSales) *
            100
            : 0
          ).toFixed(2)},${(transactionTypeSales[i].tax || 0).toFixed(
            2,
          )},${transactionTypeSales[i].serviceCharge.toFixed(2)},${(
            transactionTypeSales[i].salesReturn || 0
          ).toFixed(2)},${transactionTypeSales[i].netSales.toFixed(2)}`;

        csvData.push(csvRow);
      }
    } else {
      csvData.push(
        `Transaction Category,Transaction Date,Sales (RM),Disc (RM),Disc (%),Tax (RM),Service Charge (RM),Sales Return (RM),Net Sales (RM)`,
      );

      for (var i = 0; i < transactionTypeSalesDetails.length; i++) {
        var csvRow = `${ORDER_TYPE_PARSED[transactionTypeSalesDetails[i].orderType]
          },${moment(transactionTypeSalesDetails[i].createdAt).format(
            'DD MMM YY hh:mm A',
          )},${(transactionTypeSalesDetails[i].finalPriceBefore
            ? transactionTypeSalesDetails[i].finalPriceBefore
            : transactionTypeSalesDetails[i].finalPrice + getOrderDiscountInfo(transactionTypeSalesDetails[i])
          ).toFixed(2)},${+parseFloat(
            getOrderDiscountInfoInclOrderBased(transactionTypeSalesDetails[i]),
          ).toFixed(2)},${+parseFloat(
            transactionTypeSalesDetails[i].discountPercentage,
          ).toFixed(2)},${(transactionTypeSalesDetails[i].tax || 0).toFixed(
            2,
          )},${(transactionTypeSalesDetails[i].sc || 0).toFixed(2)},${(
            transactionTypeSalesDetails[i].salesReturn || 0
          ).toFixed(2)},${(transactionTypeSalesDetails[i].finalPriceBefore != 0 ||
            transactionTypeSalesDetails[i].finalPrice != 0
            ? (transactionTypeSalesDetails[i].finalPriceBefore
              ? transactionTypeSalesDetails[i].finalPriceBefore
              : transactionTypeSalesDetails[i].finalPrice) -
            transactionTypeSalesDetails[i].tax -
            (transactionTypeSalesDetails[i].sc || 0)
            : 0
          ).toFixed(2)}`;

        csvData.push(csvRow);
      }
    }

    // console.log('excelData');
    // console.log(excelData);

    return csvData.join('\r\n');
  };

  const downloadExcel = () => {
    const excelData = convertDataToExcelFormat();

    var excelFile = `${Platform.OS === 'ios'
      ? RNFS.DocumentDirectoryPath
      : RNFS.DownloadDirectoryPath
      }/koodoo-report-Product-Sales${moment().format(
        'YYYY-MM-DD-HH-mm-ss',
      )}.xlsx`;
    var excelWorkSheet = XLSX.utils.json_to_sheet(excelData);
    var excelWorkBook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(
      excelWorkBook,
      excelWorkSheet,
      'Product Sales Report',
    );

    const workBookData = XLSX.write(excelWorkBook, {
      type: 'binary',
      bookType: 'xlsx',
    });

    RNFS.writeFile(excelFile, workBookData, 'ascii')
      .then((success) => {
        // console.log(`wrote file ${excelFile}`);

        Alert.alert(
          'Success',
          `Send to ${excelFile}`,
          [{ text: 'OK', onPress: () => { } }],
          { cancelable: false },
        );
      })
      .catch((err) => {
        // console.log(err.message);
      });

    // XLSX.writeFileAsync(excelFile, excelWorkBook, () => {
    //     Alert.alert(
    //         'Success',
    //         `Send to ${excelFile}`,
    //         [{ text: 'OK', onPress: () => { } }],
    //         { cancelable: false },
    //     );
    // });

    // const csvData = convertArrayToCSV(CsvData);

    // const pathToWrite = `${RNFetchBlob.fs.dirs.DownloadDir}/koodoo-report-Product-Sales${moment().format('YYYY-MM-DD-HH-mm-ss')}.csv`;
    // // console.log("PATH", excelFile);
    // RNFetchBlob.fs
    //     .writeFile(excelFile, excelWorkBook, 'utf8')
    //     .then(() => {
    //         // console.log(`wrote file ${excelFile}`);
    //         Alert.alert(
    //             'Success',
    //             `Send to ${excelFile}`,
    //             [{ text: 'OK', onPress: () => { } }],
    //             { cancelable: false },
    //         );
    //     })
    //     .catch(error => console.error(error));
  };

  // const emailTransaction = () => {
  //     var body = {
  //         data: CsvData,
  //         //data: convertArrayToCSV(productSales.dataSource.data),
  //         data: convertArrayToCSV(CsvData),
  //         email: exportEmail,
  //     };

  //     ApiClient.POST(API.emailDashboard, body, false).then((result) => {
  //         if (result !== null) {
  //             Alert.alert(
  //                 'Success',
  //                 'Email sent to your inbox',
  //                 [{ text: 'OK', onPress: () => { } }],
  //                 { cancelable: false },
  //             );
  //         }
  //     });

  //     setVisible(false);
  // };

  const emailTransaction = () => {
    const excelData = convertDataToExcelFormat();

    var body = {
      // data: CsvData,
      //data: convertArrayToCSV(todaySalesChart.dataSource.data),
      data: JSON.stringify(excelData),
      //data: convertDataToExcelFormat(),
      email: exportEmail,
    };

    ApiClient.POST(API.emailDashboard, body, false).then((result) => {
      if (result !== null) {
        Alert.alert(
          'Success',
          'Email has been sent',
          [{ text: 'OK', onPress: () => { } }],
          { cancelable: false },
        );
      }
    });

    setVisible(false);
  };

  var leftSpacing = '0%';

  if (windowWidth >= 1280) {
    leftSpacing = '-4%';
  }

  const leftSpacingScale = {
    marginLeft: leftSpacing,
  };

  const flatListRef = useRef();

  const ScrollToTop = () => {
    flatListRef.current.scrollToOffset({ animated: true, offset: 0 });
  };

  const ScrollToBottom = () => {
    flatListRef.current.scrollToOffset({ animated: true, offset: 100 });
  };

  return (
    // <View style={styles.container}>
    //     <View style={styles.sidebar}>
    (<UserIdleWrapper disabled={!isMounted}>
      <View
        style={[
          styles.container,
          !isTablet()
            ? {
              transform: [{ scaleX: 1 }, { scaleY: 1 }],
            }
            : {},
          {
            ...getTransformForScreenInsideNavigation(),
          }
        ]}>
        {/* <View
          style={[
            styles.sidebar,
            !isTablet()
              ? {
                width: windowWidth * 0.08,
              }
              : {},
            switchMerchant
              ? {
                // width: '10%'
              }
              : {},
            {
              width: windowWidth * 0.08,
            }
          ]}>
          <SideBar
            navigation={props.navigation}
            selectedTab={8}
            expandReport
          />
        </View> */}
        <ScrollView
          showsVerticalScrollIndicator={false}
          style={{}}
          contentContainerStyle={{
            paddingBottom: windowHeight * 0.1,
            backgroundColor: Colors.highlightColor,
          }}>
          <ScrollView horizontal>
            <ModalView
              style={{}}
              visible={exportModalVisibility}
              supportedOrientations={['portrait', 'landscape']}
              transparent
              animationType={'fade'}>
              <View
                style={{
                  flex: 1,
                  backgroundColor: Colors.modalBgColor,
                  alignItems: 'center',
                  justifyContent: 'center',
                  top:
                    Platform.OS === 'android'
                      ? 0
                      : keyboardHeight > 0
                        ? -keyboardHeight * 0.45
                        : 0,
                }}>
                <View
                  style={{
                    height: windowWidth * 0.3,
                    width: windowWidth * 0.4,
                    backgroundColor: Colors.whiteColor,
                    borderRadius: 12,
                    padding: windowWidth * 0.03,
                    alignItems: 'center',
                    justifyContent: 'center',
                    ...getTransformForModalInsideNavigation(),
                  }}>
                  <TouchableOpacity
                    disabled={isLoading}
                    style={{
                      position: 'absolute',
                      right: windowWidth * 0.02,
                      top: windowWidth * 0.02,

                      elevation: 1000,
                      zIndex: 1000,
                    }}
                    onPress={() => {
                      setExportModalVisibility(false);
                    }}>
                    <AntDesign
                      name="closecircle"
                      size={switchMerchant ? 15 : 25}
                      color={Colors.fieldtTxtColor}
                    />
                  </TouchableOpacity>
                  <View
                    style={{
                      alignItems: 'center',
                      top: '20%',
                      position: 'absolute',
                    }}>
                    <Text
                      style={{
                        fontFamily: 'NunitoSans-Bold',
                        textAlign: 'center',
                        fontSize: switchMerchant ? 16 : 24,
                      }}>
                      Download Report
                    </Text>
                  </View>
                  <View style={{ top: switchMerchant ? '14%' : '10%' }}>
                    <Text
                      style={{
                        fontSize: switchMerchant ? 10 : 20,
                        fontFamily: 'NunitoSans-Bold',
                      }}>
                      Email Address:
                    </Text>
                    <TextInput
                      underlineColorAndroid={Colors.fieldtBgColor}
                      style={{
                        backgroundColor: Colors.fieldtBgColor,
                        width: switchMerchant ? 240 : 370,
                        height: switchMerchant ? 35 : 50,
                        borderRadius: 5,
                        padding: 5,
                        marginVertical: 5,
                        borderWidth: 1,
                        borderColor: '#E5E5E5',
                        paddingLeft: 10,
                        fontSize: switchMerchant ? 10 : 14,
                      }}
                      autoCapitalize="none"
                      placeholderStyle={{ padding: 5 }}
                      placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                      placeholder="Enter your email"
                      onChangeText={(text) => {
                        setExportEmail(text);
                      }}
                      value={exportEmail}
                    />
                    <Text
                      style={{
                        fontSize: switchMerchant ? 10 : 20,
                        fontFamily: 'NunitoSans-Bold',
                        marginTop: 15,
                      }}>
                      Send As:
                    </Text>

                    <View
                      style={{
                        alignItems: 'center',
                        justifyContent: 'center',
                        flexDirection: 'row',
                        marginTop: 10,
                      }}>
                      <TouchableOpacity
                        disabled={isLoading}
                        style={{
                          justifyContent: 'center',
                          flexDirection: 'row',
                          borderWidth: 1,
                          borderColor: Colors.primaryColor,
                          backgroundColor: '#4E9F7D',
                          borderRadius: 5,
                          width: switchMerchant ? 100 : 100,
                          paddingHorizontal: 10,
                          height: switchMerchant ? 35 : 40,
                          alignItems: 'center',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 1,
                          zIndex: -1,
                          marginRight: 15,
                        }}
                        onPress={() => {
                          if (exportEmail.length > 0) {
                            CommonStore.update((s) => {
                              s.isLoading = true;
                            });

                            setIsExcel(true);

                            const excelData = convertDataToExcelFormat();

                            generateEmailReport(
                              EMAIL_REPORT_TYPE.EXCEL,
                              excelData,
                              'KooDoo Transaction Sales Report',
                              'KooDoo Transaction Sales Report.xlsx',
                              `/merchant/${merchantId}/reports/${uuidv4()}.xlsx`,
                              exportEmail,
                              'KooDoo Transaction Sales Report',
                              'KooDoo Transaction Sales Report',
                              () => {
                                CommonStore.update((s) => {
                                  s.isLoading = false;
                                });

                                setIsExcel(false);

                                Alert.alert(
                                  'Success',
                                  'Report will be sent to the email address shortly',
                                );

                                setExportModalVisibility(false);
                              },
                            );
                          } else {
                            Alert.alert('Info', 'Invalid email address');
                          }

                          logEventAnalytics({
                            eventName: ANALYTICS.MODULE_REPORT_CHA_DL_BTN_C_REP_EXCEL,
                            eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_CHA_DL_BTN_C_REP_EXCEL
                          })
                        }}>
                        {isLoading && isExcel ? (
                          <ActivityIndicator
                            size={'small'}
                            color={Colors.whiteColor}
                          />
                        ) : (
                          <Text
                            style={{
                              color: Colors.whiteColor,
                              //marginLeft: 5,
                              fontSize: switchMerchant ? 10 : 16,
                              fontFamily: 'NunitoSans-Bold',
                            }}>
                            EXCEL
                          </Text>
                        )}
                      </TouchableOpacity>

                      <TouchableOpacity
                        disabled={isLoading}
                        style={{
                          justifyContent: 'center',
                          flexDirection: 'row',
                          borderWidth: 1,
                          borderColor: Colors.primaryColor,
                          backgroundColor: '#4E9F7D',
                          borderRadius: 5,
                          width: switchMerchant ? 100 : 100,
                          paddingHorizontal: 10,
                          height: switchMerchant ? 35 : 40,
                          alignItems: 'center',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 1,
                          zIndex: -1,
                        }}
                        onPress={() => {
                          if (exportEmail.length > 0) {
                            CommonStore.update((s) => {
                              s.isLoading = true;
                            });

                            setIsCsv(true);

                            //const csvData = convertArrayToCSV(transactionTypeSales);
                            const csvData = convertDataToCSVFormat();

                            generateEmailReport(
                              EMAIL_REPORT_TYPE.CSV,
                              csvData,
                              'KooDoo Transaction Sales Report',
                              'KooDoo Transaction Sales Report.csv',
                              `/merchant/${merchantId}/reports/${uuidv4()}.csv`,
                              exportEmail,
                              'KooDoo Transaction Sales Report',
                              'KooDoo Transaction Sales Report',
                              () => {
                                CommonStore.update((s) => {
                                  s.isLoading = false;
                                });

                                setIsCsv(false);

                                Alert.alert(
                                  'Success',
                                  'Report will be sent to the email address shortly',
                                );

                                setExportModalVisibility(false);
                              },
                            );
                          } else {
                            Alert.alert('Info', 'Invalid email address');
                          }

                          logEventAnalytics({
                            eventName: ANALYTICS.MODULE_REPORT_CHA_DL_BTN_C_REP_CSV,
                            eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_CHA_DL_BTN_C_REP_CSV
                          })
                        }}>
                        {isLoading && isCsv ? (
                          <ActivityIndicator
                            size={'small'}
                            color={Colors.whiteColor}
                          />
                        ) : (
                          <Text
                            style={{
                              color: Colors.whiteColor,
                              //marginLeft: 5,
                              fontSize: switchMerchant ? 10 : 16,
                              fontFamily: 'NunitoSans-Bold',
                            }}>
                            CSV
                          </Text>
                        )}
                      </TouchableOpacity>

                      {/* <TouchableOpacity
                                            style={[styles.modalSaveButton, {
                                                zIndex: -1
                                            }]}
                                            onPress={() => { downloadPDF() }}>
                                            <Text style={[styles.modalDescText, { color: Colors.primaryColor }]}>PDF</Text>
                                        </TouchableOpacity> */}
                    </View>
                  </View>
                </View>
              </View>
            </ModalView>

            <DateTimePickerModal
              isVisible={showDateTimePicker}
              mode={'date'}
              onConfirm={(text) => {
                // setRev_date(moment(text).startOf('day'));
                CommonStore.update(s => {
                  s.historyStartDate = moment(text).startOf('day');
                });
                setShowDateTimePicker(false);
              }}
              onCancel={() => {
                setShowDateTimePicker(false);
              }}
              maximumDate={moment(historyEndDate).toDate()}
              date={moment(historyStartDate).toDate()}
            />

            <DateTimePickerModal
              isVisible={showDateTimePicker1}
              mode={'date'}
              onConfirm={(text) => {
                // setRev_date1(moment(text).endOf('day'));
                CommonStore.update(s => {
                  s.historyEndDate = moment(text).endOf('day');
                });
                setShowDateTimePicker1(false);
              }}
              onCancel={() => {
                setShowDateTimePicker1(false);
              }}
              minimumDate={moment(historyStartDate).toDate()}
              date={moment(historyEndDate).toDate()}
            />

            {/* <ModalView
                style={{ flex: 1 }}
                visible={visible}
                transparent={true}
                animationType="slide">

                <KeyboardAvoidingView
                    behavior="padding"
                    style={{
                        backgroundColor: 'rgba(0,0,0,0.5)',
                        flex: 1,
                        justifyContent: 'center',
                        alignItems: 'center',
                        minHeight: windowHeight,
                    }}>
                    <View style={styles.confirmBox}>

                        <Text style={{ fontSize: 24, justifyContent: "center", alignSelf: "center", marginTop: 40, fontFamily: 'NunitoSans-Bold' }}>Enter your email</Text>
                        <View style={{ justifyContent: "center", alignSelf: "center", alignContent: 'center', marginTop: 20, flexDirection: 'row', width: '80%' }}>
                            <View style={{ justifyContent: 'center', marginHorizontal: 5 }}>
                                <Text style={{ color: Colors.descriptionColor, fontSize: 20, }}>
                                    email:
                                    </Text>
                            </View>
                            <TextInput
                                underlineColorAndroid={Colors.fieldtBgColor}
                                style={styles.textInput8}
                                placeholder="Enter your email"
                                // style={{
                                //     // paddingLeft: 1,
                                // }}                                        
                                //defaultValue={extentionCharges}
                                onChangeText={(text) => {
                                    // setState({ exportEmail: text });
                                    setExportEmail(text);
                                }}
                                value={exportEmail}
                            />

                <View style={{flexDirection:'row', marginTop: '13.5%'}}>
                                <TouchableOpacity
                                    onPress={emailTransaction}
                                    style={{
                                        backgroundColor: Colors.fieldtBgColor,
                                        width: '50%',
                                        justifyContent: 'center',
                                        alignItems: 'center',
                                        alignContent: 'center',
                                        height: 60,
                                        borderBottomLeftRadius: 30,
                                        borderRightWidth: StyleSheet.hairlineWidth,
                                        borderTopWidth: StyleSheet.hairlineWidth
                                    }}>
                                    <Text style={{ fontSize: 22, color: Colors.primaryColor, fontFamily: 'NunitoSans-SemiBold' }}>
                                        Email
                                    </Text>
                                </TouchableOpacity>
                                <TouchableOpacity
                                    onPress={() => {
                                        // setState({ visible: false });
                                        setVisible(false);
                                    }}
                                    style={{
                                        backgroundColor: Colors.fieldtBgColor,
                                        width: '50%',
                                        justifyContent: 'center',
                                        alignItems: 'center',
                                        alignContent: 'center',
                                        height: 60,
                                        borderBottomRightRadius: 30,
                                        borderTopWidth: StyleSheet.hairlineWidth
                                    }}>
                                    <Text style={{ fontSize: 22, color: Colors.descriptionColor, fontFamily: 'NunitoSans-SemiBold' }}>
                                        Cancel
                                            </Text>
                                </TouchableOpacity>
                            </View>
                        </View>

                    </View>
                </KeyboardAvoidingView>
            </ModalView> */}

            {/* <View style={[styles.content, {
                //top: Platform.OS === 'ios' && keyboardHeight > 0 ? -keyboardHeight * 1 : 0,
            }]}> */}
            <KeyboardAvoidingView style={[styles.content, {
              padding: 20,
              width: Dimensions.get('window').width * (1 - Styles.sideBarWidth),
              backgroundColor: Colors.highlightColor,
            }]}>
              <View style={{ flex: 1 }}>
                <View
                  style={{
                    flexDirection: 'row',
                    //backgroundColor: '#ffffff',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    alignSelf: 'center',
                    //padding: 18,
                    marginTop: 5,
                    width: windowWidth * 0.87,
                  }}>
                  <Text
                    style={{
                      fontSize: switchMerchant ? 20 : 26,
                      fontFamily: 'NunitoSans-Bold',
                    }}>
                    {`Sales By:\n${name}`}
                  </Text>
                  <View
                    style={{
                      flexDirection: 'row',
                    }}>
                    <View
                      style={[
                        {
                          flexDirection: 'row',
                          alignItems: 'center',
                          borderRadius: 10,
                          marginRight: 10,
                          zIndex: 5,
                        },
                      ]}>
                      <DropDownPicker
                        containerStyle={{ marginLeft: 10, height: 40 }}
                        arrowColor={'black'}
                        arrowSize={switchMerchant ? 10 : 20}
                        arrowStyle={{ fontWeight: 'bold' }}
                        style={{
                          width: switchMerchant ? 140 : 210,
                          paddingVertical: 0,
                          backgroundColor: Colors.fieldtBgColor,
                          borderRadius: 10,
                        }}
                        placeholderStyle={{ color: Colors.fieldtTxtColor }}
                        items={outletDropdownList}
                        itemStyle={{ justifyContent: 'flex-start', zIndex: 2 }}
                        // placeholder="Choose Outlet"
                        placeholder={`${currOutlet && currOutlet.name ? currOutlet.name : 'Choose Outlet'}`}
                        onChangeItem={(item) => {
                          // setSelectedOutletList(items.map(item => item.value))
                          if (item !== undefined) {
                            CommonStore.update((s) => {
                              s.reportOutletIdList = item
                            })
                          }
                        }}
                        defaultValue={selectedOutletList}
                        dropDownMaxHeight={100}
                        dropDownStyle={{
                          width: switchMerchant ? 140 : 210,
                          height: switchMerchant ? 90 : 90,
                          backgroundColor: Colors.fieldtBgColor,
                          borderRadius: 10,
                          borderWidth: 1,
                          textAlign: 'left',
                          zIndex: 2,
                        }}
                        globalTextStyle={{
                          fontFamily: 'NunitoSans-SemiBold',
                          fontSize: switchMerchant ? 10 : 14,
                          color: Colors.fontDark,
                          marginLeft: 5,
                        }}
                        multiple
                        multipleText="%d outlet(s) selected"
                      />
                    </View>
                    {/* <View
                      style={[
                        {
                          flexDirection: 'row',
                          alignItems: 'center',
                          borderRadius: 10,
                          marginRight: 10,
                          zIndex: 5,
                        },
                      ]}>
                      <DropDownPicker
                        containerStyle={{ marginLeft: 10, height: 40 }}
                        arrowColor={'black'}
                        arrowSize={switchMerchant ? 10 : 20}
                        arrowStyle={{ fontWeight: 'bold' }}
                        style={{
                          width: switchMerchant ? 140 : 210,
                          paddingVertical: 0,
                          backgroundColor: Colors.fieldtBgColor,
                          borderRadius: 10,
                        }}
                        placeholderStyle={{ color: Colors.fieldtTxtColor }}
                        items={[
                          // { label: 'All', value: 'ALL' },
                          { label: 'Merchant App Order', value: APP_TYPE.MERCHANT },
                          { label: 'Waiter App Order', value: APP_TYPE.WAITER },
                          { label: 'User App Order', value: APP_TYPE.USER },
                          { label: 'QR Order', value: APP_TYPE.WEB_ORDER },
                        ]}
                        itemStyle={{ justifyContent: 'flex-start', zIndex: 2 }}
                        placeholder={'Filter App Type'}
                        onChangeItem={(item) => {
                          setFilterAppType(item);

                          logEventAnalytics({
                            eventName: ANALYTICS.MODULE_REPORT_CHA_DD_APP_ORDER,
                            eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_CHA_DD_APP_ORDER
                          })
                        }}
                        defaultValue={filterAppType}
                        dropDownMaxHeight={100}
                        dropDownStyle={{
                          width: switchMerchant ? 140 : 210,
                          height: switchMerchant ? 90 : 90,
                          backgroundColor: Colors.fieldtBgColor,
                          borderRadius: 10,
                          borderWidth: 1,
                          textAlign: 'left',
                          zIndex: 2,
                        }}
                        globalTextStyle={{
                          fontFamily: 'NunitoSans-SemiBold',
                          fontSize: switchMerchant ? 10 : 14,
                          color: Colors.fontDark,
                          marginLeft: 5,
                        }}
                        multiple
                        multipleText="%d App Type(s)"
                      />
                    </View> */}
                    <TouchableOpacity
                      style={{
                        justifyContent: 'center',
                        flexDirection: 'row',
                        borderWidth: 1,
                        borderColor: Colors.primaryColor,
                        backgroundColor: '#4E9F7D',
                        borderRadius: 5,
                        //width: 140,
                        paddingHorizontal: 10,
                        height: switchMerchant ? 35 : 40,
                        alignItems: 'center',
                        shadowOffset: {
                          width: 0,
                          height: 2,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 1,
                        zIndex: -1,
                        marginRight: 10,
                      }}
                      onPress={() => {
                        setExportModalVisibility(true);
                        logEventAnalytics({
                          eventName: ANALYTICS.MODULE_REPORT_CHA_C_DOWNLOAD_BTN,
                          eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_CHA_C_DOWNLOAD_BTN
                        })
                      }}>
                      <View
                        style={{ flexDirection: 'row', alignItems: 'center' }}>
                        <Icon
                          name="download"
                          size={switchMerchant ? 10 : 20}
                          color={Colors.whiteColor}
                        />
                        <Text
                          style={{
                            color: Colors.whiteColor,
                            marginLeft: 5,
                            fontSize: switchMerchant ? 10 : 16,
                            fontFamily: 'NunitoSans-Bold',
                          }}>
                          DOWNLOAD
                        </Text>
                      </View>
                    </TouchableOpacity>

                    <View
                      style={[
                        {
                          // flex: 1,
                          // alignContent: 'flex-end',
                          // marginBottom: 10,
                          // flexDirection: 'row',
                          // backgroundColor: 'red',
                          // alignItems: 'flex-end',
                          height: switchMerchant ? 35 : 40,
                          //marginTop: 10,
                        },
                        !isTablet()
                          ? {
                            marginLeft: 0,
                          }
                          : {},
                      ]}>
                      <View
                        style={{
                          width: switchMerchant ? 200 : 250,
                          height: switchMerchant ? 35 : 40,
                          backgroundColor: 'white',
                          borderRadius: 5,
                          // marginLeft: '53%',
                          flexDirection: 'row',
                          alignContent: 'center',
                          alignItems: 'center',

                          //marginRight: windowWidth * Styles.sideBarWidth,

                          shadowColor: '#000',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 3,
                          borderWidth: 1,
                          borderColor: '#E5E5E5',
                        }}>
                        <Icon
                          name="search"
                          size={switchMerchant ? 13 : 18}
                          color={Colors.primaryColor}
                          style={{ marginLeft: 15 }}
                        />
                        <TextInput
                          editable={!loading}
                          underlineColorAndroid={Colors.whiteColor}
                          style={{
                            width: switchMerchant ? 180 : 220,
                            fontSize: switchMerchant ? 10 : 15,
                            fontFamily: 'NunitoSans-Regular',
                            paddingLeft: 5,
                            height: 45,
                          }}
                          placeholderTextColor={Platform.select({
                            ios: '#a9a9a9',
                          })}
                          clearButtonMode="while-editing"
                          placeholder=" Search"
                          onChangeText={(text) => {
                            setSearch(text);
                            // setList1(false);
                            // setSearchList(true);

                            logEventAnalytics({
                              eventName: ANALYTICS.MODULE_REPORT_CHA_DL_BTN_TB_EMAIL,
                              eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_CHA_DL_BTN_TB_EMAIL
                            })
                          }}
                          value={search}
                        />
                      </View>
                    </View>
                  </View>
                </View>
                {/* <Text style={{ fontSize: 16, marginTop: 10, color: Colors.descriptionColor }}>Date last updated on {moment().format('LLLL')}</Text> */}
                <View
                  style={{
                    flexDirection: 'row',
                    //backgroundColor: '#ffffff',
                    justifyContent: 'space-between',
                    //padding: 18,
                    marginTop: 20,
                    width: '100%',
                    paddingLeft: switchMerchant
                      ? 0
                      : windowWidth <= 1823 && windowWidth >= 1820
                        ? '1.5%'
                        : '1%',
                    paddingRight: switchMerchant
                      ? 0
                      : windowWidth <= 1823 && windowWidth >= 1820
                        ? '1.5%'
                        : '1%',
                  }}>
                  {/* <View
                            style={[{
                                // flex: 1,
                                // alignContent: 'flex-end',
                                // marginBottom: 10,
                                // flexDirection: 'row',
                                // marginRight: '-40%',
                                // marginLeft: 310,
                                // backgroundColor: 'red',
                                // alignItems: 'flex-end',
                                // right: '-50%',
                                width: '45%',
                                height: 40,

                            }, !isTablet() ? {
                                marginLeft: 0,
                            } : {}]}>
                            <View style={{
                                width: 250,
                                height: 40,
                                backgroundColor: 'white',
                                borderRadius: 10,
                                // marginLeft: '53%',
                                flexDirection: 'row',
                                alignContent: 'center',
                                alignItems: 'center',

                                //marginRight: windowWidth * Styles.sideBarWidth,

                                position: 'absolute',
                                //right: '17%',

                                shadowColor: '#000',
                                shadowOffset: {
                                    width: 0,
                                    height: 2,
                                },
                                shadowOpacity: 0.22,
                                shadowRadius: 3.22,
                                elevation: 3,
                            }}>
                                <Icon name="search" size={18} color={Colors.fieldtTxtColor} style={{ marginLeft: 15 }} />
                                <TextInput
                                    editable={!loading}
                                    underlineColorAndroid={Colors.whiteColor}
                                    style={{
                                        width: 250,
                                        fontSize: 15,
                                        fontFamily: 'NunitoSans-Regular',
                                    }}
                                    clearButtonMode="while-editing"
                                    placeholder=" Search"
                                    onChangeText={(text) => {
                                        setSearch(text);
                                        // setList1(false);
                                        // setSearchList(true);
                                    }}
                                    value={search}
                                />
                            </View>
                        </View> */}

                  {/* <TouchableOpacity style={{ marginRight: 10, width: 230, flexDirection: 'row', alignItems: 'center', paddingLeft: 15, borderRadius: 10, height: 40, backgroundColor: Colors.whiteColor }}
                                    onPress={() => { changeClick() }}>
                                    <TouchableOpacity style={{ alignSelf: "center" }} onPress={() => { setState({ pickerMode: 'date', showDateTimePicker: true }) }}>
                                        <EvilIcons name="calendar" size={25} color={Colors.primaryColor} />
                                    </TouchableOpacity>
                                    <Text style={{ fontFamily: "NunitoSans-Regular" }}>{moment(rev_date).format("DD MMM yyyy")} - {moment(rev_date1).format("DD MMM yyyy")}</Text>
                                </TouchableOpacity> */}

                  <TouchableOpacity
                    style={[
                      {
                        justifyContent: 'center',
                        flexDirection: 'row',
                        borderWidth: 1,
                        borderColor: Colors.primaryColor,
                        backgroundColor: '#4E9F7D',
                        borderRadius: 5,
                        //width: 160,
                        paddingHorizontal: 10,
                        height: switchMerchant ? 35 : 40,
                        alignItems: 'center',
                        shadowOffset: {
                          width: 0,
                          height: 2,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 1,
                        zIndex: -1,

                        opacity: !showDetails ? 0 : 100,
                      },
                    ]}
                    onPress={() => {
                      setShowDetails(false);
                      setCurrentPage(pageReturn);
                      // console.log('Returning to page');
                      // console.log(pageReturn);
                      setPageCount(
                        Math.ceil(transactionTypeSales.length / perPage),
                      );
                      setCurrReportSummarySort('');
                      setCurrReportDetailsSort('');
                    }}
                    disabled={!showDetails}>
                    <AntDesign
                      name="arrowleft"
                      size={switchMerchant ? 10 : 20}
                      color={Colors.whiteColor}
                      style={
                        {
                          // top: -1,
                          //marginRight: -5,
                        }
                      }
                    />
                    <Text
                      style={{
                        color: Colors.whiteColor,
                        marginLeft: 5,
                        fontSize: switchMerchant ? 10 : 16,
                        fontFamily: 'NunitoSans-Bold',
                        // marginBottom: Platform.OS === 'ios' ? 0 : 2
                      }}>
                      Summary
                    </Text>
                  </TouchableOpacity>

                  <View style={{ flexDirection: 'row' }}>
                    <View
                      style={[
                        {
                          // marginRight: 10,
                          // paddingLeft: 15,
                          paddingHorizontal: 15,
                          flexDirection: 'row',
                          alignItems: 'center',
                          borderRadius: 10,
                          paddingVertical: 10,
                          justifyContent: 'center',
                          backgroundColor: Colors.whiteColor,
                          shadowOpacity: 0,
                          shadowColor: '#000',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 1,
                        },
                      ]}>
                      <View
                        style={{ alignSelf: 'center', marginRight: 5 }}
                        onPress={() => {
                          setState({
                            pickerMode: 'date',
                            showDateTimePicker: true,
                          });
                        }}>
                        {/* <EvilIcons name="calendar" size={25} color={Colors.primaryColor} /> */}
                        <GCalendar
                          width={switchMerchant ? 15 : 20}
                          height={switchMerchant ? 15 : 20}
                        />
                      </View>

                      <TouchableOpacity
                        onPress={() => {
                          setShowDateTimePicker(true);
                          setShowDateTimePicker1(false);

                          logEventAnalytics({
                            eventName: ANALYTICS.MODULE_REPORT_CHA_C_CAL_START,
                            eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_CHA_C_CAL_START
                          })
                        }}
                        style={{
                          marginHorizontal: 4,
                        }}>
                        <Text
                          style={
                            switchMerchant
                              ? { fontSize: 10, fontFamily: 'NunitoSans-Regular' }
                              : { fontFamily: 'NunitoSans-Regular' }
                          }>
                          {moment(historyStartDate).format('DD MMM yyyy')}
                        </Text>
                      </TouchableOpacity>

                      <Text
                        style={
                          switchMerchant
                            ? { fontSize: 10, fontFamily: 'NunitoSans-Regular' }
                            : { fontFamily: 'NunitoSans-Regular' }
                        }>
                        -
                      </Text>

                      <TouchableOpacity
                        onPress={() => {
                          setShowDateTimePicker(false);
                          setShowDateTimePicker1(true);

                          logEventAnalytics({
                            eventName: ANALYTICS.MODULE_REPORT_CHA_C_CAL_END,
                            eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_CHA_C_CAL_END
                          })
                        }}
                        style={{
                          marginHorizontal: 4,
                        }}>
                        <Text
                          style={
                            switchMerchant
                              ? { fontSize: 10, fontFamily: 'NunitoSans-Regular' }
                              : { fontFamily: 'NunitoSans-Regular' }
                          }>
                          {moment(historyEndDate).format('DD MMM yyyy')}
                        </Text>
                      </TouchableOpacity>
                    </View>

                    {/* <TouchableOpacity
                                style={{
                                    paddingHorizontal: 15, backgroundColor: Colors.whiteColor, height: 40, width: 120, alignItems: 'center', borderRadius: 7, flexDirection: 'row', justifyContent: 'center', shadowColor: '#000',
                                    shadowOffset: {
                                        width: 0,
                                        height: 2,
                                    },
                                    shadowOpacity: 0.22,
                                    shadowRadius: 3.22,
                                    elevation: 1,
                                }}
                                onPress={() => {
                                    // setState({
                                    //     visible: true
                                    // })
                                    setVisible(true);
                                }}
                            >
                                <Upload width={15} height={15} />
                                <Text style={{ fontFamily: "NunitoSans-Regular", fontSize: 13, marginLeft: 12 }}>Email</Text>
                            </TouchableOpacity> */}
                  </View>

                  {/* <View style={{ flex: 4 }}>
                            <TouchableOpacity>
                                <View style={{ width: '92%', height: 50, backgroundColor: Colors.whiteColor, borderRadius: 10, elevation: 2, alignItems: 'center', flexDirection: 'row' }}>
                                    <AntDesign name='search1' size={25} color={Colors.primaryColor} style={{ marginLeft: '5%' }} />
                                    <TextInput
                                        editable={!loading}
                                        underlineColorAndroid={Colors.whiteColor}
                                        style={{ width: '82%' }}
                                        clearButtonMode="while-editing"
                                        placeholder=" Search"
                                        onChangeText={(text) => {
                                            setState({
                                                search: text.trim(),
                                                list1: false,
                                                searchList: true,
                                            });
                                        }}
                                        value={search}
                                    //onSubmitEditing={searchBarItem()}
                                    />
                                </View>
                            </TouchableOpacity>
                        </View>
                        <View style={{ flex: 6, flexDirection: 'row', justifyContent: 'flex-end' }}>
                            <View style={{ width: '40%' }}>
                                <TouchableOpacity style={{ width: '100%' }} onPress={() => { setState({ day: !day }) }}>
                                    <View style={{ width: '100%', height: 50, backgroundColor: Colors.whiteColor, borderRadius: 10, elevation: 2, alignItems: 'center', flexDirection: 'row' }}>
                                        <EvilIcons name='calendar' size={30} color={Colors.primaryColor} style={{ marginLeft: '5%' }} />
                                        <View style={{ justifyContent: 'center', flex: 2 }}>
                                            <Text style={{ color: Colors.descriptionColor, marginLeft: '2%', fontSize: 12 }}>{moment(startDate).format('DD MMM YYYY')} - {moment(endDate).format('DD MMM YYYY')} </Text>
                                        </View>
                                    </View>
                                </TouchableOpacity>
                                <DateTimePickerModal
                                    isVisible={showDateTimePicker}
                                    mode={pickerMode}
                                    onConfirm={(text) => {
                                        if (pick == 1) {
                                            var date_ob = new Date(text);
                                            let date = ("0" + date_ob.getDate()).slice(-2);
                                            let month = ("0" + (date_ob.getMonth() + 1)).slice(-2);
                                            let year = date_ob.getFullYear();
                                            setState({ startDate: year + "-" + month + "-" + date })
                                        } else {
                                            var date_ob = new Date(text);
                                            let date = ("0" + date_ob.getDate()).slice(-2);
                                            let month = ("0" + (date_ob.getMonth() + 1)).slice(-2);
                                            let year = date_ob.getFullYear();
                                            setState({ endDate: year + "-" + month + "-" + date })
                                        }

                                        setState({ showDateTimePicker: false })
                                    }}
                                    onCancel={() => {
                                        setState({ showDateTimePicker: false })
                                    }}
                                />
                                {day ?
                                    <View style={{ position: 'absolute', width: "100%", backgroundColor: Colors.whiteColor, marginTop: '20%', zIndex: 6000 }}>
                                        <TouchableOpacity style={{ padding: 10, backgroundColor: Colors.primaryColor }} onPress={() => { moment() }}>
                                            <Text style={{ color: Colors.whiteColor }}>Today</Text>
                                        </TouchableOpacity>
                                        <TouchableOpacity style={{ padding: 10, backgroundColor: Colors.whiteColor }} onPress={() => { setState({ startDate: moment(moment(new Date()).subtract(1, 'days')).format('YYYY-MM-DD'), endDate: moment(new Date()).format('YYYY-MM-DD') }) }}>
                                            <Text style={{ color: "#828282" }}>Yesterday</Text>
                                        </TouchableOpacity>
                                        <TouchableOpacity style={{ padding: 10, backgroundColor: Colors.whiteColor }} onPress={() => { setState({ startDate: moment(moment(new Date()).subtract(7, 'days')).format('YYYY-MM-DD'), endDate: moment(new Date()).format('YYYY-MM-DD') }) }}>
                                            <Text style={{ color: "#828282" }}>Last 7 days</Text>
                                        </TouchableOpacity>
                                        <TouchableOpacity style={{ padding: 10, backgroundColor: Colors.whiteColor }} onPress={() => { setState({ startDate: moment(moment(new Date()).subtract(30, 'days')).format('YYYY-MM-DD'), endDate: moment(new Date()).format('YYYY-MM-DD') }) }}>
                                            <Text style={{ color: "#828282" }}>Last 30 days</Text>
                                        </TouchableOpacity>
                                        <TouchableOpacity style={{ padding: 10, backgroundColor: Colors.whiteColor }} onPress={() => { setState({ startDate: moment(moment(new Date()).startOf("month")).format('YYYY-MM-DD'), endDate: moment(moment(new Date()).endOf("month")).format('YYYY-MM-DD') }) }}>
                                            <Text style={{ color: "#828282" }}>This month</Text>
                                        </TouchableOpacity>
                                        <TouchableOpacity style={{ padding: 10, backgroundColor: Colors.whiteColor }} onPress={() => { setState({ startDate: moment(moment(moment(new Date()).startOf("month")).subtract(1, 'month')).format('YYYY-MM-DD'), endDate: moment(moment(moment(new Date()).endOf("month")).subtract(1, 'month')).format('YYYY-MM-DD') }) }}>
                                            <Text style={{ color: "#828282" }}>Last month</Text>
                                        </TouchableOpacity>
                                        <TouchableOpacity style={{ padding: 10, backgroundColor: Colors.whiteColor }}>
                                            <Text style={{ color: "#828282" }}>Custom range</Text>
                                        </TouchableOpacity>
                                        <View style={{ flexDirection: 'row' }}>
                                            <View style={{ flex: 1, marginLeft: 25 }}>
                                                <Text style={{ color: "#828282" }}>From</Text>
                                            </View>
                                            <View style={{ flex: 1 }}>
                                                <Text style={{ color: "#828282" }}>To</Text>
                                            </View>
                                        </View>
                                        <View style={{ flexDirection: 'row' }}>
                                            <TouchableOpacity style={{ width: "38%", marginLeft: 25, borderWidth: 1, alignItems: "center", borderColor: "#919191", backgroundColor: Colors.fieldtBgColor }}
                                                onPress={() => { setState({ pick: 1, pick1: 0, pickerMode: 'date', showDateTimePicker: true }) }}>
                                                <Text style={{ fontSize: 12 }}>{moment(startDate).format("DD MMM yyyy")}</Text>
                                            </TouchableOpacity>
                                            <View style={{ width: "8%" }}>
                                            </View>
                                            <TouchableOpacity style={{ width: "38%", borderWidth: 1, alignItems: "center", borderColor: "#919191", backgroundColor: Colors.fieldtBgColor }}
                                                onPress={() => { setState({ pick: 0, pick1: 1, pickerMode: 'date', showDateTimePicker: true }) }}>
                                                <Text style={{ fontSize: 12 }}>{moment(endDate).format("DD MMM yyyy")}</Text>
                                            </TouchableOpacity>
                                        </View>
                                        <View style={{ flexDirection: 'row', marginTop: 20 }}>
                                            <TouchableOpacity style={{ width: "38%", marginLeft: 15, borderWidth: 1, alignItems: "center", borderColor: "#919191", backgroundColor: Colors.whiteColor, height: 30, borderRadius: 5, justifyContent: "center", alignItems: 'center' }}
                                                onPress={() => { setState({ day: false }) }}>
                                                <Text style={{ fontSize: 15, color: "#919191" }}>Cancel</Text>
                                            </TouchableOpacity>
                                            <View style={{ width: "8%" }}>
                                            </View>
                                            <TouchableOpacity style={{ width: "38%", borderWidth: 1, alignItems: "center", borderColor: Colors.primaryColor, backgroundColor: Colors.primaryColor, height: 30, borderRadius: 5, justifyContent: "center", alignItems: 'center' }}
                                                onPress={() => { setState({ day: false }), getDetail() }}>
                                                <Text style={{ fontSize: 15, color: Colors.whiteColor }}>Apply</Text>
                                            </TouchableOpacity>
                                        </View>
                                        <View style={{ height: 20 }}>
                                        </View>
                                    </View>
                                    : null}
                            </View>
                            <View style={{ width: '4%' }}></View>
                            <TouchableOpacity style={{ width: '20%' }} onPress={() => { setState({ visible: true }); }}>
                                <View style={{ width: '100%', height: 50, backgroundColor: Colors.whiteColor, borderRadius: 10, elevation: 2, alignItems: 'center', flexDirection: 'row' }}>
                                    <AntDesign name='download' size={20} color={Colors.primaryColor} style={{ marginLeft: '5%' }} />
                                    <View style={{ justifyContent: 'center', flex: 2 }}>
                                        <Text style={{ color: Colors.descriptionColor, marginLeft: '5%', fontSize: 15 }}>Download</Text>
                                    </View>
                                </View>
                            </TouchableOpacity>
                            <View style={{ width: '4%' }}></View>
                            <TouchableOpacity style={{ width: '20%' }} onPress={() => { setState({ visible1: true }); }}>
                                <View style={{ width: '100%', height: 50, backgroundColor: Colors.whiteColor, borderRadius: 10, elevation: 2, alignItems: 'center', flexDirection: 'row' }}>
                                    <AntDesign name='upload' size={20} color={Colors.primaryColor} style={{ marginLeft: '5%', flex: 1 }} />
                                    <View style={{ justifyContent: 'center', flex: 2 }}>
                                        <Text style={{ color: Colors.descriptionColor, marginLeft: '2%', fontSize: 15 }}>Email</Text>
                                    </View>
                                </View>
                            </TouchableOpacity>
                        </View> */}
                </View>
                <View style={{ width: '100%', marginTop: 10, zIndex: -1 }}>
                  <View
                    style={{
                      backgroundColor: Colors.whiteColor,
                      width: windowWidth * 0.87,
                      height:
                        Platform.OS == 'android'
                          ? windowHeight * 0.6
                          : windowHeight * 0.66,
                      marginTop: 10,
                      marginHorizontal: 30,
                      marginBottom: 10,
                      alignSelf: 'center',
                      borderRadius: 5,
                      shadowOpacity: 0,
                      shadowColor: '#000',
                      shadowOffset: {
                        width: 0,
                        height: 2,
                      },
                      shadowOpacity: 0.22,
                      shadowRadius: 3.22,
                      elevation: 3,
                    }}>
                    {/* <View style={{ height: '89%', position: 'absolute', justifyContent: 'space-between', zIndex: showDetails && transactionTypeSalesDetails.length > 0 ? 10 : -2, marginVertical: 0, marginTop: 70, alignSelf: 'center' }}>
                                <TouchableOpacity
                                    onPress={() => {
                                        ScrollToTop();
                                    }}
                                    style={{ alignSelf: 'center', marginTop: '8%', zIndex: 10 }}>
                                    <AntDesign name={'upcircle'} size={23} color={Colors.primaryColor} style={{ opacity: showDetails && transactionTypeSalesDetails.length > 0 ? 0.4 : 0 }} />
                                </TouchableOpacity>
                                <TouchableOpacity
                                    onPress={() => {
                                        ScrollToBottom();
                                    }}
                                    style={{ alignSelf: 'center', marginTop: '42%', zIndex: 10 }}>
                                    <AntDesign name={'downcircle'} size={23} color={Colors.primaryColor} style={{ opacity: showDetails && transactionTypeSalesDetails.length > 0 ? 0.4 : 0 }} />
                                </TouchableOpacity>
                            </View> */}
                    {!showDetails ? (
                      <View style={{ marginTop: 10, flexDirection: 'row' }}>
                        <View
                          style={{
                            flexDirection: 'row',
                            width: '6%',
                            borderRightWidth: 1,
                            borderRightColor: 'lightgrey',
                            alignItems: 'center',
                            justifyContent: 'flex-start',
                            paddingLeft: 10,
                          }}>
                          <View style={{ flexDirection: 'row' }}>
                            <View style={{ flexDirection: 'column' }}>
                              <Text
                                numberOfLines={2}
                                style={{
                                  fontSize: switchMerchant ? 10 : 13,
                                  fontFamily: 'NunitoSans-Bold',
                                  textAlign: 'left',
                                }}>
                                {'No.\n'}
                              </Text>
                              <Text
                                style={{
                                  fontSize: switchMerchant ? 8 : 10,
                                  color: Colors.descriptionColor,
                                }} />
                            </View>
                            <View style={{ marginLeft: '3%' }}>
                              <Entypo
                                name="triangle-up"
                                size={switchMerchant ? 7 : 14}
                                color="transparent" />

                              <Entypo
                                name="triangle-down"
                                size={switchMerchant ? 7 : 14}
                                color="transparent" />
                              <Text
                                style={{
                                  fontSize: 10,
                                  color: Colors.descriptionColor,
                                }} />
                            </View>
                          </View>
                          {/* <View style={{ marginLeft: '3%' }}>
                                                <TouchableOpacity onPress={() => setCurrReportSummarySort(REPORT_SORT_FIELD_TYPE.PRODUCT_CATEGORY_ASC)}>
                                                    <Entypo name='triangle-up' size={14} color={currReportSummarySort === REPORT_SORT_FIELD_TYPE.PRODUCT_CATEGORY_ASC ? Colors.secondaryColor : Colors.descriptionColor}></Entypo>
                                                </TouchableOpacity>

                                                <TouchableOpacity onPress={() => setCurrReportSummarySort(REPORT_SORT_FIELD_TYPE.PRODUCT_CATEGORY_DESC)}>
                                                    <Entypo name='triangle-down' size={14} color={currReportSummarySort === REPORT_SORT_FIELD_TYPE.PRODUCT_CATEGORY_DESC ? Colors.secondaryColor : Colors.descriptionColor}></Entypo>
                                                </TouchableOpacity>
                                            </View> */}
                        </View>
                        <View
                          style={{
                            flexDirection: 'row',
                            width: '18%',
                            borderRightWidth: 1,
                            borderRightColor: 'lightgrey',
                            alignItems: 'center',
                            justifyContent: 'flex-start',
                            paddingLeft: 10,
                          }}>
                          <TouchableOpacity
                            onPress={() => {
                              if (
                                currReportSummarySort ===
                                REPORT_SORT_FIELD_TYPE.USER_ORDER_TYPE_ASC
                              ) {
                                setCurrReportSummarySort(
                                  REPORT_SORT_FIELD_TYPE.USER_ORDER_TYPE_DESC,
                                );
                              } else {
                                setCurrReportSummarySort(
                                  REPORT_SORT_FIELD_TYPE.USER_ORDER_TYPE_ASC,
                                );
                              }

                              logEventAnalytics({
                                eventName: ANALYTICS.MODULE_REPORT_CHA_SUM_TABLE_C_TABLE_TRAN_CA,
                                eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_CHA_SUM_TABLE_C_TABLE_TRAN_CA
                              })
                            }}>
                            <View style={{ flexDirection: 'row' }}>
                              <View style={{ flexDirection: 'column' }}>
                                <Text
                                  numberOfLines={2}
                                  style={{
                                    fontSize: switchMerchant ? 10 : 13,
                                    fontFamily: 'NunitoSans-Bold',
                                    textAlign: 'left',
                                  }}>
                                  {'Transaction\nCategory'}
                                </Text>
                                <Text
                                  style={{
                                    fontSize: switchMerchant ? 8 : 10,
                                    color: Colors.descriptionColor,
                                  }} />
                              </View>
                              <View style={{ marginLeft: '3%' }}>
                                <Entypo
                                  name="triangle-up"
                                  size={switchMerchant ? 7 : 14}
                                  color={
                                    currReportSummarySort ===
                                      REPORT_SORT_FIELD_TYPE.USER_ORDER_TYPE_ASC
                                      ? Colors.secondaryColor
                                      : Colors.descriptionColor
                                  } />

                                <Entypo
                                  name="triangle-down"
                                  size={switchMerchant ? 7 : 14}
                                  color={
                                    currReportSummarySort ===
                                      REPORT_SORT_FIELD_TYPE.USER_ORDER_TYPE_DESC
                                      ? Colors.secondaryColor
                                      : Colors.descriptionColor
                                  } />
                                <Text
                                  style={{
                                    fontSize: 10,
                                    color: Colors.descriptionColor,
                                  }} />
                              </View>
                            </View>
                          </TouchableOpacity>
                        </View>

                        <View
                          style={{
                            flexDirection: 'row',
                            width: '10%',
                            borderRightWidth: 1,
                            borderRightColor: 'lightgrey',
                            alignItems: 'center',
                            justifyContent: 'flex-start',
                            paddingLeft: 10,
                          }}>
                          <TouchableOpacity
                            onPress={() => {
                              if (
                                currReportSummarySort ===
                                REPORT_SORT_FIELD_TYPE.TOTAL_TRANSACTION_ASC
                              ) {
                                setCurrReportSummarySort(
                                  REPORT_SORT_FIELD_TYPE.TOTAL_TRANSACTION_DESC,
                                );
                              } else {
                                setCurrReportSummarySort(
                                  REPORT_SORT_FIELD_TYPE.TOTAL_TRANSACTION_ASC,
                                );
                              }

                              logEventAnalytics({
                                eventName: ANALYTICS.MODULE_REPORT_CHA_SUM_TABLE_C_TABLE_OR,
                                eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_CHA_SUM_TABLE_C_TABLE_OR
                              })
                            }}>
                            <View style={{ flexDirection: 'row' }}>
                              <View style={{ flexDirection: 'column' }}>
                                <Text
                                  numberOfLines={2}
                                  style={{
                                    fontSize: switchMerchant ? 10 : 13,
                                    fontFamily: 'NunitoSans-Bold',
                                    textAlign: 'center',
                                  }}>
                                  {'Order\n'}
                                </Text>
                                <Text
                                  style={{
                                    fontSize: switchMerchant ? 8 : 10,
                                    color: Colors.descriptionColor,
                                  }}>
                                  Qty
                                </Text>
                              </View>
                              <View style={{ marginLeft: '3%' }}>
                                <Entypo
                                  name="triangle-up"
                                  size={switchMerchant ? 7 : 14}
                                  color={
                                    currReportSummarySort ===
                                      REPORT_SORT_FIELD_TYPE.TOTAL_TRANSACTION_ASC
                                      ? Colors.secondaryColor
                                      : Colors.descriptionColor
                                  } />

                                <Entypo
                                  name="triangle-down"
                                  size={switchMerchant ? 7 : 14}
                                  color={
                                    currReportSummarySort ===
                                      REPORT_SORT_FIELD_TYPE.TOTAL_TRANSACTION_DESC
                                      ? Colors.secondaryColor
                                      : Colors.descriptionColor
                                  } />
                                <Text
                                  style={{
                                    fontSize: 10,
                                    color: Colors.descriptionColor,
                                  }} />
                              </View>
                            </View>
                          </TouchableOpacity>
                        </View>

                        <View
                          style={{
                            flexDirection: 'row',
                            width: '10%',
                            borderRightWidth: 1,
                            borderRightColor: 'lightgrey',
                            alignItems: 'center',
                            justifyContent: 'flex-start',
                            paddingLeft: 10,
                          }}>
                          <TouchableOpacity
                            onPress={() => {
                              if (
                                currReportSummarySort ===
                                REPORT_SORT_FIELD_TYPE.TOTAL_SALES_ASC
                              ) {
                                setCurrReportSummarySort(
                                  REPORT_SORT_FIELD_TYPE.TOTAL_SALES_DESC,
                                );
                              } else {
                                setCurrReportSummarySort(
                                  REPORT_SORT_FIELD_TYPE.TOTAL_SALES_ASC,
                                );
                              }

                              logEventAnalytics({
                                eventName: ANALYTICS.MODULE_REPORT_CHA_SUM_TABLE_C_TABLE_SALES,
                                eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_CHA_SUM_TABLE_C_TABLE_SALES
                              })
                            }}>
                            <View style={{ flexDirection: 'row' }}>
                              <View style={{ flexDirection: 'column' }}>
                                <Text
                                  numberOfLines={2}
                                  style={{
                                    fontSize: switchMerchant ? 10 : 13,
                                    fontFamily: 'NunitoSans-Bold',
                                  }}>
                                  {'Sales\n'}
                                </Text>
                                <Text
                                  style={{
                                    fontSize: switchMerchant ? 8 : 10,
                                    color: Colors.descriptionColor,
                                  }}>
                                  RM
                                </Text>
                              </View>

                              <View
                                style={{
                                  marginLeft: '3%',
                                  justifyContent: 'space-between',
                                }}>
                                <View>
                                  <Entypo
                                    name="triangle-up"
                                    size={switchMerchant ? 7 : 14}
                                    color={
                                      currReportSummarySort ===
                                        REPORT_SORT_FIELD_TYPE.TOTAL_SALES_ASC
                                        ? Colors.secondaryColor
                                        : Colors.descriptionColor
                                    } />

                                  <Entypo
                                    name="triangle-down"
                                    size={switchMerchant ? 7 : 14}
                                    color={
                                      currReportSummarySort ===
                                        REPORT_SORT_FIELD_TYPE.TOTAL_SALES_DESC
                                        ? Colors.secondaryColor
                                        : Colors.descriptionColor
                                    } />
                                </View>
                                {/* <Text
                                style={{
                                  fontSize: 10,
                                  color: Colors.descriptionColor,
                                }}></Text> */}
                                <Tooltip
                                  isVisible={saleTip}
                                  content={
                                    <Text>
                                      Product Amount - Discount + Tax + Service
                                      Charge
                                    </Text>
                                  }
                                  placement="top"
                                  onClose={() => setSaleTip(false)}>
                                  <TouchableOpacity
                                    onPress={() => {

                                      setSaleTip(true);

                                      logEventAnalytics({
                                        eventName: ANALYTICS.MODULE_REPORT_CHA_SUM_TABLE_SALES_C_ICON_QM,
                                        eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_CHA_SUM_TABLE_SALES_C_ICON_QM
                                      })

                                    }}
                                    style={styles.touchable}>
                                    <Feather
                                      name="help-circle"
                                      size={switchMerchant ? 10 : 15}
                                      style={{ color: Colors.primaryColor }}
                                    />
                                  </TouchableOpacity>
                                </Tooltip>
                              </View>
                            </View>
                          </TouchableOpacity>
                        </View>

                        <View
                          style={{
                            flexDirection: 'row',
                            width: '8%',
                            borderRightWidth: 1,
                            borderRightColor: 'lightgrey',
                            alignItems: 'center',
                            justifyContent: 'flex-start',
                            paddingLeft: 10,
                          }}>
                          <TouchableOpacity
                            onPress={() => {
                              if (
                                currReportSummarySort ===
                                REPORT_SORT_FIELD_TYPE.TOTAL_DISCOUNT_ASC
                              ) {
                                setCurrReportSummarySort(
                                  REPORT_SORT_FIELD_TYPE.TOTAL_DISCOUNT_DESC,
                                );
                              } else {
                                setCurrReportSummarySort(
                                  REPORT_SORT_FIELD_TYPE.TOTAL_DISCOUNT_ASC,
                                );
                              }
                              logEventAnalytics({
                                eventName: ANALYTICS.MODULE_REPORT_CHA_SUM_TABLE_C_TABLE_DISC_RM,
                                eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_CHA_SUM_TABLE_C_TABLE_DISC_RM
                              })
                            }}>
                            <View style={{ flexDirection: 'row' }}>
                              <View style={{ flexDirection: 'column' }}>
                                <Text
                                  numberOfLines={2}
                                  style={{
                                    fontSize: switchMerchant ? 10 : 13,
                                    fontFamily: 'NunitoSans-Bold',
                                    textAlign: 'center',
                                  }}>
                                  {'Disc\n'}
                                </Text>
                                <Text
                                  style={{
                                    fontSize: switchMerchant ? 8 : 10,
                                    color: Colors.descriptionColor,
                                  }}>
                                  RM
                                </Text>
                              </View>
                              <View style={{ marginLeft: '3%' }}>
                                <Entypo
                                  name="triangle-up"
                                  size={switchMerchant ? 7 : 14}
                                  color={
                                    currReportSummarySort ===
                                      REPORT_SORT_FIELD_TYPE.TOTAL_DISCOUNT_ASC
                                      ? Colors.secondaryColor
                                      : Colors.descriptionColor
                                  } />

                                <Entypo
                                  name="triangle-down"
                                  size={switchMerchant ? 7 : 14}
                                  color={
                                    currReportSummarySort ===
                                      REPORT_SORT_FIELD_TYPE.TOTAL_DISCOUNT_DESC
                                      ? Colors.secondaryColor
                                      : Colors.descriptionColor
                                  } />
                                <Text
                                  style={{
                                    fontSize: 10,
                                    color: Colors.descriptionColor,
                                  }} />
                              </View>
                            </View>
                          </TouchableOpacity>
                        </View>
                        <View
                          style={{
                            flexDirection: 'row',
                            width: '8%',
                            borderRightWidth: 1,
                            borderRightColor: 'lightgrey',
                            alignItems: 'center',
                            justifyContent: 'flex-start',
                            paddingLeft: 10,
                          }}>
                          <TouchableOpacity
                            onPress={() => {
                              if (
                                currReportSummarySort ===
                                REPORT_SORT_FIELD_TYPE.DISCOUNT_ASC
                              ) {
                                setCurrReportSummarySort(
                                  REPORT_SORT_FIELD_TYPE.DISCOUNT_DESC,
                                );
                              } else {
                                setCurrReportSummarySort(
                                  REPORT_SORT_FIELD_TYPE.DISCOUNT_ASC,
                                );
                              }

                              logEventAnalytics({
                                eventName: ANALYTICS.MODULE_REPORT_CHA_SUM_TABLE_C_TABLE_DISC_PERC,
                                eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_CHA_SUM_TABLE_C_TABLE_DISC_PERC
                              })
                            }}>
                            <View style={{ flexDirection: 'row' }}>
                              <View style={{ flexDirection: 'column' }}>
                                <Text
                                  numberOfLines={2}
                                  style={{
                                    fontSize: switchMerchant ? 10 : 13,
                                    fontFamily: 'NunitoSans-Bold',
                                  }}>
                                  {'Disc\n'}
                                </Text>
                                <Text
                                  style={{
                                    fontSize: switchMerchant ? 8 : 10,
                                    color: Colors.descriptionColor,
                                  }}>
                                  %
                                </Text>
                              </View>
                              <View style={{ marginLeft: '3%' }}>
                                <Entypo
                                  name="triangle-up"
                                  size={switchMerchant ? 7 : 14}
                                  color={
                                    currReportSummarySort ===
                                      REPORT_SORT_FIELD_TYPE.DISCOUNT_ASC
                                      ? Colors.secondaryColor
                                      : Colors.descriptionColor
                                  } />

                                <Entypo
                                  name="triangle-down"
                                  size={switchMerchant ? 7 : 14}
                                  color={
                                    currReportSummarySort ===
                                      REPORT_SORT_FIELD_TYPE.DISCOUNT_DESC
                                      ? Colors.secondaryColor
                                      : Colors.descriptionColor
                                  } />
                                <Text
                                  style={{
                                    fontSize: 10,
                                    color: Colors.descriptionColor,
                                  }} />
                              </View>
                            </View>
                          </TouchableOpacity>
                        </View>

                        <View
                          style={{
                            flexDirection: 'row',
                            width: '8%',
                            borderRightWidth: 1,
                            borderRightColor: 'lightgrey',
                            alignItems: 'center',
                            justifyContent: 'flex-start',
                            paddingLeft: 10,
                          }}>
                          <TouchableOpacity
                            onPress={() => {
                              if (
                                currReportSummarySort ===
                                REPORT_SORT_FIELD_TYPE.TAX_ASC
                              ) {
                                setCurrReportSummarySort(
                                  REPORT_SORT_FIELD_TYPE.TAX_DESC,
                                );
                              } else {
                                setCurrReportSummarySort(
                                  REPORT_SORT_FIELD_TYPE.TAX_ASC,
                                );
                              }

                              logEventAnalytics({
                                eventName: ANALYTICS.MODULE_REPORT_CHA_SUM_TABLE_C_TABLE_TAX,
                                eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_CHA_SUM_TABLE_C_TABLE_TAX
                              })
                            }}>
                            <View style={{ flexDirection: 'row' }}>
                              <View style={{ flexDirection: 'column' }}>
                                <Text
                                  numberOfLines={2}
                                  style={{
                                    fontSize: switchMerchant ? 10 : 13,
                                    fontFamily: 'NunitoSans-Bold',
                                  }}>
                                  {'Tax\n'}
                                </Text>
                                <Text
                                  style={{
                                    fontSize: switchMerchant ? 8 : 10,
                                    color: Colors.descriptionColor,
                                  }}>
                                  RM
                                </Text>
                              </View>
                              <View style={{ marginLeft: '3%' }}>
                                <Entypo
                                  name="triangle-up"
                                  size={switchMerchant ? 7 : 14}
                                  color={
                                    currReportSummarySort ===
                                      REPORT_SORT_FIELD_TYPE.TAX_ASC
                                      ? Colors.secondaryColor
                                      : Colors.descriptionColor
                                  } />

                                <Entypo
                                  name="triangle-down"
                                  size={switchMerchant ? 7 : 14}
                                  color={
                                    currReportSummarySort ===
                                      REPORT_SORT_FIELD_TYPE.TAX_DESC
                                      ? Colors.secondaryColor
                                      : Colors.descriptionColor
                                  } />
                                <Text
                                  style={{
                                    fontSize: 10,
                                    color: Colors.descriptionColor,
                                  }} />
                              </View>
                            </View>
                          </TouchableOpacity>
                        </View>
                        <View
                          style={{
                            flexDirection: 'row',
                            width: '10%',
                            borderRightWidth: 1,
                            borderRightColor: 'lightgrey',
                            alignItems: 'center',
                            justifyContent: 'flex-start',
                            paddingLeft: 10,
                          }}>
                          <TouchableOpacity
                            onPress={() => {
                              if (
                                currReportSummarySort ===
                                REPORT_SORT_FIELD_TYPE.SERVICE_CHARGE_ASC
                              ) {
                                setCurrReportSummarySort(
                                  REPORT_SORT_FIELD_TYPE.SERVICE_CHARGE_DESC,
                                );
                              } else {
                                setCurrReportSummarySort(
                                  REPORT_SORT_FIELD_TYPE.SERVICE_CHARGE_ASC,
                                );
                              }
                              logEventAnalytics({
                                eventName: ANALYTICS.MODULE_REPORT_CHA_SUM_TABLE_C_TABLE_SERV_CHAR,
                                eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_CHA_SUM_TABLE_C_TABLE_SERV_CHAR
                              })
                            }}>
                            <View style={{ flexDirection: 'row' }}>
                              <View style={{ flexDirection: 'column' }}>
                                <Text
                                  numberOfLines={2}
                                  style={{
                                    fontSize: switchMerchant ? 10 : 13,
                                    fontFamily: 'NunitoSans-Bold',
                                  }}>
                                  {'Service\nCharge'}
                                </Text>
                                <Text
                                  style={{
                                    fontSize: switchMerchant ? 8 : 10,
                                    color: Colors.descriptionColor,
                                  }}>
                                  RM
                                </Text>
                              </View>
                              <View style={{ marginLeft: '3%' }}>
                                <Entypo
                                  name="triangle-up"
                                  size={switchMerchant ? 7 : 14}
                                  color={
                                    currReportSummarySort ===
                                      REPORT_SORT_FIELD_TYPE.SERVICE_CHARGE_ASC
                                      ? Colors.secondaryColor
                                      : Colors.descriptionColor
                                  } />

                                <Entypo
                                  name="triangle-down"
                                  size={switchMerchant ? 7 : 14}
                                  color={
                                    currReportSummarySort ===
                                      REPORT_SORT_FIELD_TYPE.SERVICE_CHARGE_DESC
                                      ? Colors.secondaryColor
                                      : Colors.descriptionColor
                                  } />
                                <Text
                                  style={{
                                    fontSize: 10,
                                    color: Colors.descriptionColor,
                                  }} />
                              </View>
                            </View>
                          </TouchableOpacity>
                        </View>
                        {/* <View style={{ flexDirection: 'row', flex: 1, borderRightWidth: 1, borderRightColor: 'lightgrey', alignItems: 'center', justifyContent: 'center' }}>
                                            <View style={{ flexDirection: 'column' }}>
                                                <TouchableOpacity onPress={() => {
                                                    if (currReportSummarySort === REPORT_SORT_FIELD_TYPE.GP_ASC) {
                                                        setCurrReportSummarySort(REPORT_SORT_FIELD_TYPE.GP_DESC)
                                                    }
                                                    else {
                                                        setCurrReportSummarySort(REPORT_SORT_FIELD_TYPE.GP_ASC)
                                                    }
                                                }}>
                                                    <Text numberOfLines={2} style={{ fontSize: 13, fontFamily: 'NunitoSans-Bold' }}>GP</Text>
                                                    <Text style={{ fontSize: 10, color: Colors.descriptionColor }}>%</Text>
                                                </TouchableOpacity>
                                            </View>
                                            <View style={{ marginLeft: '3%' }}>
                                                <Entypo name='triangle-up' size={14} color={currReportSummarySort === REPORT_SORT_FIELD_TYPE.GP_ASC ? Colors.secondaryColor : Colors.descriptionColor}></Entypo>

                                                <Entypo name='triangle-down' size={14} color={currReportSummarySort === REPORT_SORT_FIELD_TYPE.GP_DESC ? Colors.secondaryColor : Colors.descriptionColor}></Entypo>
                                            </View>
                                        </View> */}
                        <View
                          style={{
                            flexDirection: 'row',
                            width: '10%',
                            borderRightWidth: 1,
                            borderRightColor: 'lightgrey',
                            alignItems: 'center',
                            justifyContent: 'flex-start',
                            paddingLeft: 10,
                          }}>
                          <TouchableOpacity
                            onPress={() => {
                              if (
                                currReportDetailsSort ===
                                REPORT_SORT_FIELD_TYPE.USER_ORDER_SALES_RETURN_ASC
                              ) {
                                setCurrReportDetailsSort(
                                  REPORT_SORT_FIELD_TYPE.USER_ORDER_SALES_RETURN_DESC,
                                );
                              } else {
                                setCurrReportDetailsSort(
                                  REPORT_SORT_FIELD_TYPE.USER_ORDER_SALES_RETURN_ASC,
                                );
                              }

                              logEventAnalytics({
                                eventName: ANALYTICS.MODULE_REPORT_CHA_SUM_TABLE_C_TABLE_SERV_RET,
                                eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_CHA_SUM_TABLE_C_TABLE_SERV_RET
                              })
                            }}>
                            <View style={{ flexDirection: 'row' }}>
                              <View style={{ flexDirection: 'column' }}>
                                <Text
                                  numberOfLines={2}
                                  style={{
                                    fontSize: switchMerchant ? 10 : 13,
                                    fontFamily: 'NunitoSans-Bold',
                                  }}>
                                  {'Sales\nReturn'}
                                </Text>
                                <Text
                                  style={{
                                    fontSize: switchMerchant ? 8 : 10,
                                    color: Colors.descriptionColor,
                                  }}>
                                  RM
                                </Text>
                              </View>
                              <View style={{ marginLeft: '3%' }}>
                                <Entypo
                                  name="triangle-up"
                                  size={switchMerchant ? 7 : 14}
                                  color={
                                    currReportDetailsSort ===
                                      REPORT_SORT_FIELD_TYPE.USER_ORDER_SALES_RETURN_ASC
                                      ? Colors.secondaryColor
                                      : Colors.descriptionColor
                                  } />

                                <Entypo
                                  name="triangle-down"
                                  size={switchMerchant ? 7 : 14}
                                  color={
                                    currReportDetailsSort ===
                                      REPORT_SORT_FIELD_TYPE.USER_ORDER_SALES_RETURN_DESC
                                      ? Colors.secondaryColor
                                      : Colors.descriptionColor
                                  } />
                                <Text
                                  style={{
                                    fontSize: 10,
                                    color: Colors.descriptionColor,
                                  }} />
                              </View>
                            </View>
                          </TouchableOpacity>
                        </View>
                        <View
                          style={{
                            flexDirection: 'row',
                            width: '12%',
                            //borderRightWidth: 1,
                            //borderRightColor: 'lightgrey',
                            alignItems: 'center',
                            justifyContent: 'flex-start',
                            paddingLeft: 10,
                          }}>
                          <TouchableOpacity
                            onPress={() => {
                              if (
                                currReportSummarySort ===
                                REPORT_SORT_FIELD_TYPE.NET_SALES_ASC
                              ) {
                                setCurrReportSummarySort(
                                  REPORT_SORT_FIELD_TYPE.NET_SALES_DESC,
                                );
                              } else {
                                setCurrReportSummarySort(
                                  REPORT_SORT_FIELD_TYPE.NET_SALES_ASC,
                                );
                              }

                              logEventAnalytics({
                                eventName: ANALYTICS.MODULE_REPORT_CHA_SUM_TABLE_C_TABLE_NET_SALES,
                                eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_CHA_SUM_TABLE_C_TABLE_NET_SALES
                              })
                            }}>
                            <View style={{ flexDirection: 'row' }}>
                              <View style={{ flexDirection: 'column' }}>
                                <Text
                                  numberOfLines={2}
                                  style={{
                                    fontSize: switchMerchant ? 10 : 13,
                                    fontFamily: 'NunitoSans-Bold',
                                  }}>
                                  {'Net Sales\n'}
                                </Text>
                                <Text
                                  style={{
                                    fontSize: switchMerchant ? 8 : 10,
                                    color: Colors.descriptionColor,
                                  }}>
                                  RM
                                </Text>
                              </View>
                              <View
                                style={{
                                  marginLeft: '3%',
                                  justifyContent: 'space-between',
                                }}>
                                <View>
                                  <Entypo
                                    name="triangle-up"
                                    size={switchMerchant ? 7 : 14}
                                    color={
                                      currReportSummarySort ===
                                        REPORT_SORT_FIELD_TYPE.NET_SALES_ASC
                                        ? Colors.secondaryColor
                                        : Colors.descriptionColor
                                    } />

                                  <Entypo
                                    name="triangle-down"
                                    size={switchMerchant ? 7 : 14}
                                    color={
                                      currReportSummarySort ===
                                        REPORT_SORT_FIELD_TYPE.NET_SALES_DESC
                                        ? Colors.secondaryColor
                                        : Colors.descriptionColor
                                    } />
                                </View>
                                {/* <Text
                                style={{
                                  fontSize: 10,
                                  color: Colors.descriptionColor,
                                }}></Text> */}
                                <Tooltip
                                  isVisible={netSaleTip}
                                  content={
                                    <Text>
                                      Total Sales - Tax - Service Charge
                                    </Text>
                                  }
                                  placement="top"
                                  onClose={() => setNetSaleTip(false)}>
                                  <TouchableOpacity
                                    onPress={() => {

                                      setNetSaleTip(true);

                                      logEventAnalytics({
                                        eventName: ANALYTICS.MODULE_REPORT_CHA_SUM_TABLE_NS_C_ICON_QM,
                                        eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_CHA_SUM_TABLE_NS_C_ICON_QM
                                      })


                                    }}
                                    style={styles.touchable}>
                                    <Feather
                                      name="help-circle"
                                      size={switchMerchant ? 10 : 15}
                                      style={{ color: Colors.primaryColor }}
                                    />
                                  </TouchableOpacity>
                                </Tooltip>
                              </View>
                            </View>
                          </TouchableOpacity>
                        </View>
                        {/* <View
                        style={{
                          flexDirection: 'row',
                          flex: 1.8,
                          borderRightWidth: 0,
                          borderRightColor: 'lightgrey',
                          alignItems: 'center',
                          justifyContent: 'flex-start',
                          paddingLeft: 10,
                        }}>
                        <TouchableOpacity
                          onPress={() => {
                            if (
                              currReportSummarySort ===
                              REPORT_SORT_FIELD_TYPE.AVERAGE_NET_SALES_ASC
                            ) {
                              setCurrReportSummarySort(
                                REPORT_SORT_FIELD_TYPE.AVERAGE_NET_SALES_DESC,
                              );
                            } else {
                              setCurrReportSummarySort(
                                REPORT_SORT_FIELD_TYPE.AVERAGE_NET_SALES_ASC,
                              );
                            }
                          }}>
                          <View style={{ flexDirection: 'row' }}>
                            <View style={{ flexDirection: 'column' }}>
                              <Text
                                numberOfLines={2}
                                style={{
                                  fontSize: switchMerchant ? 10 : 13,
                                  fontFamily: 'NunitoSans-Bold',
                                  textAlign: 'left',
                                }}>
                                {'Avg\nNet Sales'}
                              </Text>
                              <Text
                                style={{
                                  fontSize: switchMerchant ? 8 : 10,
                                  color: Colors.descriptionColor,
                                }}>
                                RM
                              </Text>
                            </View>
                            <View style={{ marginLeft: '3%' }}>
                              <Entypo
                                name="triangle-up"
                                size={switchMerchant ? 7 : 14}
                                color={
                                  currReportSummarySort ===
                                    REPORT_SORT_FIELD_TYPE.AVERAGE_NET_SALES_ASC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor
                                }></Entypo>

                              <Entypo
                                name="triangle-down"
                                size={switchMerchant ? 7 : 14}
                                color={
                                  currReportSummarySort ===
                                    REPORT_SORT_FIELD_TYPE.AVERAGE_NET_SALES_DESC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor
                                }></Entypo>
                              <Text
                                style={{
                                  fontSize: 10,
                                  color: Colors.descriptionColor,
                                }}></Text>
                            </View>
                          </View>
                        </TouchableOpacity>
                      </View> */}
                      </View>
                    ) : (
                      <View style={{ marginTop: 10, flexDirection: 'row' }}>
                        <View
                          style={{
                            flexDirection: 'row',
                            width: '6%',
                            borderRightWidth: 1,
                            borderRightColor: 'lightgrey',
                            alignItems: 'center',
                            justifyContent: 'flex-start',
                            paddingLeft: 10,
                          }}>
                          <View style={{ flexDirection: 'row' }}>
                            <View style={{ flexDirection: 'column' }}>
                              <Text
                                numberOfLines={2}
                                style={{
                                  fontSize: switchMerchant ? 10 : 13,
                                  fontFamily: 'NunitoSans-Bold',
                                  textAlign: 'left',
                                }}>
                                {'No.\n'}
                              </Text>
                              <Text
                                style={{
                                  fontSize: switchMerchant ? 8 : 10,
                                  color: Colors.descriptionColor,
                                }} />
                            </View>
                            <View style={{ marginLeft: '3%' }}>
                              <Entypo
                                name="triangle-up"
                                size={switchMerchant ? 7 : 14}
                                color="transparent" />

                              <Entypo
                                name="triangle-down"
                                size={switchMerchant ? 7 : 14}
                                color="transparent" />
                              <Text
                                style={{
                                  fontSize: 10,
                                  color: Colors.descriptionColor,
                                }} />
                            </View>
                          </View>
                          {/* <View style={{ marginLeft: '3%' }}>
                                                <TouchableOpacity onPress={() => setCurrReportSummarySort(REPORT_SORT_FIELD_TYPE.PRODUCT_CATEGORY_ASC)}>
                                                    <Entypo name='triangle-up' size={14} color={currReportSummarySort === REPORT_SORT_FIELD_TYPE.PRODUCT_CATEGORY_ASC ? Colors.secondaryColor : Colors.descriptionColor}></Entypo>
                                                </TouchableOpacity>

                                                <TouchableOpacity onPress={() => setCurrReportSummarySort(REPORT_SORT_FIELD_TYPE.PRODUCT_CATEGORY_DESC)}>
                                                    <Entypo name='triangle-down' size={14} color={currReportSummarySort === REPORT_SORT_FIELD_TYPE.PRODUCT_CATEGORY_DESC ? Colors.secondaryColor : Colors.descriptionColor}></Entypo>
                                                </TouchableOpacity>
                                            </View> */}
                        </View>
                        <View
                          style={{
                            flexDirection: 'row',
                            width: '12%',
                            borderRightWidth: 1,
                            borderRightColor: 'lightgrey',
                            alignItems: 'center',
                            justifyContent: 'flex-start',
                            paddingLeft: 10,
                          }}>
                          <TouchableOpacity
                            onPress={() => {
                              if (
                                currReportDetailsSort ===
                                REPORT_SORT_FIELD_TYPE.USER_ORDER_TYPE_ASC
                              ) {
                                setCurrReportDetailsSort(
                                  REPORT_SORT_FIELD_TYPE.USER_ORDER_TYPE_DESC,
                                );
                              } else {
                                setCurrReportDetailsSort(
                                  REPORT_SORT_FIELD_TYPE.USER_ORDER_TYPE_ASC,
                                );
                              }

                              logEventAnalytics({
                                eventName: ANALYTICS.MODULE_REPORT_CHA_SUM_TABLE_IND_SUM_C_TABLE_TRAN_CA,
                                eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_CHA_SUM_TABLE_IND_SUM_C_TABLE_TRAN_CA
                              })
                            }}>
                            <View style={{ flexDirection: 'row' }}>
                              <View style={{ flexDirection: 'column' }}>
                                <Text
                                  numberOfLines={2}
                                  style={{
                                    fontSize: switchMerchant ? 10 : 13,
                                    fontFamily: 'NunitoSans-Bold',
                                    textAlign: 'left',
                                  }}>
                                  {'Transaction\nCategory'}
                                </Text>
                                <Text
                                  style={{
                                    fontSize: switchMerchant ? 8 : 10,
                                    color: Colors.descriptionColor,
                                  }} />
                              </View>
                              <View style={{ marginLeft: '3%' }}>
                                <Entypo
                                  name="triangle-up"
                                  size={switchMerchant ? 7 : 14}
                                  color={
                                    currReportDetailsSort ===
                                      REPORT_SORT_FIELD_TYPE.USER_ORDER_TYPE_ASC
                                      ? Colors.secondaryColor
                                      : Colors.descriptionColor
                                  } />

                                <Entypo
                                  name="triangle-down"
                                  size={switchMerchant ? 7 : 14}
                                  color={
                                    currReportDetailsSort ===
                                      REPORT_SORT_FIELD_TYPE.USER_ORDER_TYPE_DESC
                                      ? Colors.secondaryColor
                                      : Colors.descriptionColor
                                  } />
                                <Text
                                  style={{
                                    fontSize: 10,
                                    color: Colors.descriptionColor,
                                  }} />
                              </View>
                            </View>
                          </TouchableOpacity>
                        </View>

                        <View
                          style={{
                            flexDirection: 'row',
                            width: '18%',
                            borderRightWidth: 1,
                            borderRightColor: 'lightgrey',
                            alignItems: 'center',
                            justifyContent: 'flex-start',
                            paddingLeft: 10,
                          }}>
                          <TouchableOpacity
                            onPress={() => {
                              if (
                                currReportDetailsSort ===
                                REPORT_SORT_FIELD_TYPE.USER_ORDER_DATE_TIME_ASC
                              ) {
                                setCurrReportDetailsSort(
                                  REPORT_SORT_FIELD_TYPE.USER_ORDER_DATE_TIME_DESC,
                                );
                              } else {
                                setCurrReportDetailsSort(
                                  REPORT_SORT_FIELD_TYPE.USER_ORDER_DATE_TIME_ASC,
                                );
                              }

                              logEventAnalytics({
                                eventName: ANALYTICS.MODULE_REPORT_CHA_SUM_TABLE_IND_SUM_C_TABLE_TRAN_DT,
                                eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_CHA_SUM_TABLE_IND_SUM_C_TABLE_TRAN_DT
                              })
                            }}>
                            <View style={{ flexDirection: 'row' }}>
                              <View style={{ flexDirection: 'column' }}>
                                <Text
                                  numberOfLines={2}
                                  style={{
                                    fontSize: switchMerchant ? 10 : 13,
                                    fontFamily: 'NunitoSans-Bold',
                                    textAlign: 'left',
                                  }}>
                                  {'Transaction\nDate & Time'}
                                </Text>
                                <Text
                                  style={{
                                    fontSize: switchMerchant ? 8 : 10,
                                    color: Colors.descriptionColor,
                                  }} />
                              </View>
                              <View style={{ marginLeft: '3%' }}>
                                <Entypo
                                  name="triangle-up"
                                  size={switchMerchant ? 7 : 14}
                                  color={
                                    currReportDetailsSort ===
                                      REPORT_SORT_FIELD_TYPE.USER_ORDER_DATE_TIME_ASC
                                      ? Colors.secondaryColor
                                      : Colors.descriptionColor
                                  } />

                                <Entypo
                                  name="triangle-down"
                                  size={switchMerchant ? 7 : 14}
                                  color={
                                    currReportDetailsSort ===
                                      REPORT_SORT_FIELD_TYPE.USER_ORDER_DATE_TIME_DESC
                                      ? Colors.secondaryColor
                                      : Colors.descriptionColor
                                  } />
                                <Text
                                  style={{
                                    fontSize: 10,
                                    color: Colors.descriptionColor,
                                  }} />
                              </View>
                            </View>
                          </TouchableOpacity>
                        </View>
                        <View
                          style={{
                            flexDirection: 'row',
                            width: '10%',
                            borderRightWidth: 1,
                            borderRightColor: 'lightgrey',
                            alignItems: 'center',
                            justifyContent: 'flex-start',
                            paddingLeft: 10,
                          }}>
                          <TouchableOpacity
                            onPress={() => {
                              if (
                                currReportDetailsSort ===
                                REPORT_SORT_FIELD_TYPE.USER_ORDER_SALES_ASC
                              ) {
                                setCurrReportDetailsSort(
                                  REPORT_SORT_FIELD_TYPE.USER_ORDER_SALES_DESC,
                                );
                              } else {
                                setCurrReportDetailsSort(
                                  REPORT_SORT_FIELD_TYPE.USER_ORDER_SALES_ASC,
                                );
                              }

                              logEventAnalytics({
                                eventName: ANALYTICS.MODULE_REPORT_CHA_SUM_TABLE_IND_SUM_C_TABLE_SALES,
                                eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_CHA_SUM_TABLE_IND_SUM_C_TABLE_SALES
                              })
                            }}>
                            <View style={{ flexDirection: 'row' }}>
                              <View style={{ flexDirection: 'column' }}>
                                <Text
                                  numberOfLines={2}
                                  style={{
                                    fontSize: switchMerchant ? 10 : 13,
                                    fontFamily: 'NunitoSans-Bold',
                                  }}>
                                  {'Sales\n'}
                                </Text>
                                <Text
                                  style={{
                                    fontSize: switchMerchant ? 8 : 10,
                                    color: Colors.descriptionColor,
                                  }}>
                                  RM
                                </Text>
                              </View>
                              <View
                                style={{
                                  marginLeft: '3%',
                                  justifyContent: 'space-between',
                                }}>
                                <View>
                                  <Entypo
                                    name="triangle-up"
                                    size={switchMerchant ? 7 : 14}
                                    color={
                                      currReportDetailsSort ===
                                        REPORT_SORT_FIELD_TYPE.USER_ORDER_SALES_ASC
                                        ? Colors.secondaryColor
                                        : Colors.descriptionColor
                                    } />

                                  <Entypo
                                    name="triangle-down"
                                    size={switchMerchant ? 7 : 14}
                                    color={
                                      currReportDetailsSort ===
                                        REPORT_SORT_FIELD_TYPE.USER_ORDER_SALES_DESC
                                        ? Colors.secondaryColor
                                        : Colors.descriptionColor
                                    } />
                                </View>
                                {/* <Text
                                style={{
                                  fontSize: 10,
                                  color: Colors.descriptionColor,
                                }}></Text> */}
                                <Tooltip
                                  isVisible={saleTip}
                                  content={
                                    <Text>
                                      Product Amount - Discount + Tax + Service
                                      Charge
                                    </Text>
                                  }
                                  placement="top"
                                  onClose={() => setSaleTip(false)}>
                                  <TouchableOpacity
                                    onPress={() => {

                                      setSaleTip(true);

                                      logEventAnalytics({
                                        eventName: ANALYTICS.MODULE_REPORT_CHA_SUM_TABLE_IND_SUM_T_S_C_ICON_QM,
                                        eventNameParsed: ANALYTICS_PARSED, MODULE_REPORT_CHA_SUM_TABLE_IND_SUM_T_S_C_ICON_QM
                                      })

                                    }}
                                    style={styles.touchable}>
                                    <Feather
                                      name="help-circle"
                                      size={switchMerchant ? 10 : 15}
                                      style={{ color: Colors.primaryColor }}
                                    />
                                  </TouchableOpacity>
                                </Tooltip>
                              </View>
                            </View>
                          </TouchableOpacity>
                        </View>
                        {/* <View style={{ flexDirection: 'row', flex: 2.7, borderRightWidth: 1, borderRightColor: 'lightgrey', alignItems: 'center', justifyContent: 'center' }}>
                                    <View style={{ flexDirection: 'column' }}>
                                        <Text numberOfLines={2} style={{ fontSize: 13, fontFamily: 'NunitoSans-Bold' }}>Transaction Time</Text>
                                        <Text style={{ fontSize: 10, color: Colors.descriptionColor }}></Text>
                                    </View>
                                </View> */}
                        <View
                          style={{
                            flexDirection: 'row',
                            width: '8%',
                            borderRightWidth: 1,
                            borderRightColor: 'lightgrey',
                            alignItems: 'center',
                            justifyContent: 'flex-start',
                            paddingLeft: 10,
                          }}>
                          <TouchableOpacity
                            onPress={() => {
                              if (
                                currReportDetailsSort ===
                                REPORT_SORT_FIELD_TYPE.USER_ORDER_TOTAL_DISCOUNT_ASC
                              ) {
                                setCurrReportDetailsSort(
                                  REPORT_SORT_FIELD_TYPE.USER_ORDER_TOTAL_DISCOUNT_DESC,
                                );
                              } else {
                                setCurrReportDetailsSort(
                                  REPORT_SORT_FIELD_TYPE.USER_ORDER_TOTAL_DISCOUNT_ASC,
                                );
                              }

                              logEventAnalytics({
                                eventName: ANALYTICS.MODULE_REPORT_CHA_SUM_TABLE_IND_SUM_C_TABLE_DISC_RM,
                                eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_CHA_SUM_TABLE_C_TABLE_DISC_RM
                              })
                            }}>
                            <View style={{ flexDirection: 'row' }}>
                              <View style={{ flexDirection: 'column' }}>
                                <Text
                                  numberOfLines={2}
                                  style={{
                                    fontSize: switchMerchant ? 10 : 13,
                                    fontFamily: 'NunitoSans-Bold',
                                  }}>
                                  {'Disc\n'}
                                </Text>
                                <Text
                                  style={{
                                    fontSize: switchMerchant ? 8 : 10,
                                    color: Colors.descriptionColor,
                                  }}>
                                  RM
                                </Text>
                              </View>
                              <View style={{ marginLeft: '3%' }}>
                                <Entypo
                                  name="triangle-up"
                                  size={switchMerchant ? 7 : 14}
                                  color={
                                    currReportDetailsSort ===
                                      REPORT_SORT_FIELD_TYPE.USER_ORDER_TOTAL_DISCOUNT_ASC
                                      ? Colors.secondaryColor
                                      : Colors.descriptionColor
                                  } />

                                <Entypo
                                  name="triangle-down"
                                  size={switchMerchant ? 7 : 14}
                                  color={
                                    currReportDetailsSort ===
                                      REPORT_SORT_FIELD_TYPE.USER_ORDER_TOTAL_DISCOUNT_DESC
                                      ? Colors.secondaryColor
                                      : Colors.descriptionColor
                                  } />
                                <Text
                                  style={{
                                    fontSize: 10,
                                    color: Colors.descriptionColor,
                                  }} />
                              </View>
                            </View>
                          </TouchableOpacity>
                        </View>
                        <View
                          style={{
                            flexDirection: 'row',
                            width: '8%',
                            borderRightWidth: 1,
                            borderRightColor: 'lightgrey',
                            alignItems: 'center',
                            justifyContent: 'flex-start',
                            paddingLeft: 10,
                          }}>
                          <TouchableOpacity
                            onPress={() => {
                              if (
                                currReportDetailsSort ===
                                REPORT_SORT_FIELD_TYPE.USER_ORDER_DISCOUNT_ASC
                              ) {
                                setCurrReportDetailsSort(
                                  REPORT_SORT_FIELD_TYPE.USER_ORDER_DISCOUNT_DESC,
                                );
                              } else {
                                setCurrReportDetailsSort(
                                  REPORT_SORT_FIELD_TYPE.USER_ORDER_DISCOUNT_ASC,
                                );
                              }

                              logEventAnalytics({
                                eventName: ANALYTICS.MODULE_REPORT_CHA_SUM_TABLE_IND_SUM_C_TABLE_DISC_PERC,
                                eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_CHA_SUM_TABLE_C_TABLE_DISC_PERC
                              })

                            }}>
                            <View style={{ flexDirection: 'row' }}>
                              <View style={{ flexDirection: 'column' }}>
                                <Text
                                  numberOfLines={2}
                                  style={{
                                    fontSize: switchMerchant ? 10 : 13,
                                    fontFamily: 'NunitoSans-Bold',
                                  }}>
                                  {'Disc\n'}
                                </Text>
                                <Text
                                  style={{
                                    fontSize: switchMerchant ? 8 : 10,
                                    color: Colors.descriptionColor,
                                  }}>
                                  %
                                </Text>
                              </View>
                              <View style={{ marginLeft: '3%' }}>
                                <Entypo
                                  name="triangle-up"
                                  size={switchMerchant ? 7 : 14}
                                  color={
                                    currReportDetailsSort ===
                                      REPORT_SORT_FIELD_TYPE.USER_ORDER_DISCOUNT_ASC
                                      ? Colors.secondaryColor
                                      : Colors.descriptionColor
                                  } />

                                <Entypo
                                  name="triangle-down"
                                  size={switchMerchant ? 7 : 14}
                                  color={
                                    currReportDetailsSort ===
                                      REPORT_SORT_FIELD_TYPE.USER_ORDER_DISCOUNT_DESC
                                      ? Colors.secondaryColor
                                      : Colors.descriptionColor
                                  } />
                                <Text
                                  style={{
                                    fontSize: 10,
                                    color: Colors.descriptionColor,
                                  }} />
                              </View>
                            </View>
                          </TouchableOpacity>
                        </View>
                        <View
                          style={{
                            flexDirection: 'row',
                            width: '8%',
                            alignItems: 'center',
                            justifyContent: 'flex-start',
                            paddingLeft: 10,
                            borderRightWidth: 1,
                            borderRightColor: 'lightgrey',
                          }}>
                          <TouchableOpacity
                            onPress={() => {
                              if (
                                currReportDetailsSort ===
                                REPORT_SORT_FIELD_TYPE.USER_ORDER_TAX_ASC
                              ) {
                                setCurrReportDetailsSort(
                                  REPORT_SORT_FIELD_TYPE.USER_ORDER_TAX_DESC,
                                );
                              } else {
                                setCurrReportDetailsSort(
                                  REPORT_SORT_FIELD_TYPE.USER_ORDER_TAX_ASC,
                                );
                              }

                              logEventAnalytics({
                                eventName: ANALYTICS.MODULE_REPORT_CHA_SUM_TABLE_IND_SUM_C_TABLE_TAX,
                                eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_CHA_SUM_TABLE_IND_SUM_C_TABLE_TAX
                              })
                            }}>
                            <View style={{ flexDirection: 'row' }}>
                              <View style={{ flexDirection: 'column' }}>
                                <Text
                                  numberOfLines={2}
                                  style={{
                                    fontSize: switchMerchant ? 10 : 13,
                                    fontFamily: 'NunitoSans-Bold',
                                  }}>
                                  {'Tax\n'}
                                </Text>
                                <Text
                                  style={{
                                    fontSize: switchMerchant ? 8 : 10,
                                    color: Colors.descriptionColor,
                                  }}>
                                  RM
                                </Text>
                              </View>
                              <View style={{ marginLeft: '3%' }}>
                                <Entypo
                                  name="triangle-up"
                                  size={switchMerchant ? 7 : 14}
                                  color={
                                    currReportDetailsSort ===
                                      REPORT_SORT_FIELD_TYPE.USER_ORDER_TAX_ASC
                                      ? Colors.secondaryColor
                                      : Colors.descriptionColor
                                  } />

                                <Entypo
                                  name="triangle-down"
                                  size={switchMerchant ? 7 : 14}
                                  color={
                                    currReportDetailsSort ===
                                      REPORT_SORT_FIELD_TYPE.USER_ORDER_TAX_DESC
                                      ? Colors.secondaryColor
                                      : Colors.descriptionColor
                                  } />
                                <Text
                                  style={{
                                    fontSize: 10,
                                    color: Colors.descriptionColor,
                                  }} />
                              </View>
                            </View>
                          </TouchableOpacity>
                        </View>
                        {/* <View style={{ flexDirection: 'row', flex: 1.6, borderRightWidth: 1, borderRightColor: 'lightgrey', alignItems: 'center', justifyContent: 'center' }}>
                                            <View style={{ flexDirection: 'column' }}>
                                                <Text numberOfLines={2} style={{ fontSize: 13, fontFamily: 'NunitoSans-Bold' }}>Disc</Text>
                                                <Text style={{ fontSize: 10, color: Colors.descriptionColor }}>%</Text>
                                            </View>
                                            <View style={{ marginLeft: '3%' }}>
                                                <TouchableOpacity onPress={() => setCurrReportDetailsSort(REPORT_SORT_FIELD_TYPE.USER_ORDER_DISCOUNT_ASC)}>
                                                    <Entypo name='triangle-up' size={14} color={currReportDetailsSort === REPORT_SORT_FIELD_TYPE.USER_ORDER_DISCOUNT_ASC ? Colors.secondaryColor : Colors.descriptionColor}></Entypo>
                                                </TouchableOpacity>

                                                <TouchableOpacity onPress={() => setCurrReportDetailsSort(REPORT_SORT_FIELD_TYPE.USER_ORDER_DISCOUNT_DESC)}>
                                                    <Entypo name='triangle-down' size={14} color={currReportDetailsSort === REPORT_SORT_FIELD_TYPE.USER_ORDER_DISCOUNT_DESC ? Colors.secondaryColor : Colors.descriptionColor}></Entypo>
                                                </TouchableOpacity>
                                            </View>
                                        </View> */}
                        <View
                          style={{
                            flexDirection: 'row',
                            width: '9%',
                            alignItems: 'center',
                            justifyContent: 'flex-start',
                            paddingLeft: 10,
                            borderRightWidth: 1,
                            borderRightColor: 'lightgrey',
                          }}>
                          <TouchableOpacity
                            onPress={() => {
                              if (
                                currReportDetailsSort ===
                                REPORT_SORT_FIELD_TYPE.USER_ORDER_SERVICE_CHARGE_ASC
                              ) {
                                setCurrReportDetailsSort(
                                  REPORT_SORT_FIELD_TYPE.USER_ORDER_SERVICE_CHARGE_DESC,
                                );
                              } else {
                                setCurrReportDetailsSort(
                                  REPORT_SORT_FIELD_TYPE.USER_ORDER_SERVICE_CHARGE_ASC,
                                );
                              }

                              logEventAnalytics({
                                eventName: ANALYTICS.MODULE_REPORT_CHA_SUM_TABLE_IND_SUM_C_TABLE_SERV_CHAR,
                                eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_CHA_SUM_TABLE_IND_SUM_C_TABLE_SERV_CHAR
                              })
                            }}>
                            <View style={{ flexDirection: 'row' }}>
                              <View style={{ flexDirection: 'column' }}>
                                <Text
                                  numberOfLines={2}
                                  style={{
                                    fontSize: switchMerchant ? 10 : 13,
                                    fontFamily: 'NunitoSans-Bold',
                                  }}>
                                  {'Service\nCharge'}
                                </Text>
                                <Text
                                  style={{
                                    fontSize: switchMerchant ? 8 : 10,
                                    color: Colors.descriptionColor,
                                  }}>
                                  RM
                                </Text>
                              </View>

                              <View style={{ marginLeft: '3%' }}>
                                <Entypo
                                  name="triangle-up"
                                  size={switchMerchant ? 7 : 14}
                                  color={
                                    currReportDetailsSort ===
                                      REPORT_SORT_FIELD_TYPE.USER_ORDER_SERVICE_CHARGE_ASC
                                      ? Colors.secondaryColor
                                      : Colors.descriptionColor
                                  } />

                                <Entypo
                                  name="triangle-down"
                                  size={switchMerchant ? 7 : 14}
                                  color={
                                    currReportDetailsSort ===
                                      REPORT_SORT_FIELD_TYPE.USER_ORDER_SERVICE_CHARGE_DESC
                                      ? Colors.secondaryColor
                                      : Colors.descriptionColor
                                  } />
                                <Text
                                  style={{
                                    fontSize: 10,
                                    color: Colors.descriptionColor,
                                  }} />
                              </View>
                            </View>
                          </TouchableOpacity>
                        </View>
                        <View
                          style={{
                            flexDirection: 'row',
                            width: '9%',
                            // borderRightWidth: 1,
                            // borderRightColor: 'lightgrey',
                            alignItems: 'center',
                            justifyContent: 'flex-start',
                            paddingLeft: 10,
                          }}>
                          <TouchableOpacity
                            onPress={() => {
                              if (
                                currReportDetailsSort ===
                                REPORT_SORT_FIELD_TYPE.USER_ORDER_SALES_RETURN_ASC
                              ) {
                                setCurrReportDetailsSort(
                                  REPORT_SORT_FIELD_TYPE.USER_ORDER_SALES_RETURN_DESC,
                                );
                              } else {
                                setCurrReportDetailsSort(
                                  REPORT_SORT_FIELD_TYPE.USER_ORDER_SALES_RETURN_ASC,
                                );
                              }

                              logEventAnalytics({
                                eventName: ANALYTICS.MODULE_REPORT_CHA_SUM_TABLE_IND_SUM_C_TABLE_SERV_RET,
                                eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_CHA_SUM_TABLE_IND_SUM_C_TABLE_SERV_RET
                              })
                            }}>
                            <View style={{ flexDirection: 'row' }}>
                              <View style={{ flexDirection: 'column' }}>
                                <Text
                                  numberOfLines={2}
                                  style={{
                                    fontSize: switchMerchant ? 10 : 13,
                                    fontFamily: 'NunitoSans-Bold',
                                  }}>
                                  {'Sales\nReturn'}
                                </Text>
                                <Text
                                  style={{
                                    fontSize: switchMerchant ? 8 : 10,
                                    color: Colors.descriptionColor,
                                  }}>
                                  RM
                                </Text>
                              </View>
                              <View style={{ marginLeft: '3%' }}>
                                <Entypo
                                  name="triangle-up"
                                  size={switchMerchant ? 7 : 14}
                                  color={
                                    currReportDetailsSort ===
                                      REPORT_SORT_FIELD_TYPE.USER_ORDER_SALES_RETURN_ASC
                                      ? Colors.secondaryColor
                                      : Colors.descriptionColor
                                  } />

                                <Entypo
                                  name="triangle-down"
                                  size={switchMerchant ? 7 : 14}
                                  color={
                                    currReportDetailsSort ===
                                      REPORT_SORT_FIELD_TYPE.USER_ORDER_SALES_RETURN_DESC
                                      ? Colors.secondaryColor
                                      : Colors.descriptionColor
                                  } />
                                <Text
                                  style={{
                                    fontSize: 10,
                                    color: Colors.descriptionColor,
                                  }} />
                              </View>
                            </View>
                          </TouchableOpacity>
                        </View>
                        {/* <View style={{ flexDirection: 'row', flex: 2, borderRightWidth: 1, borderRightColor: 'lightgrey', alignItems: 'center', justifyContent: 'center' }}>
                                            <View style={{ flexDirection: 'column' }}>
                                                <Text numberOfLines={2} style={{ fontSize: 13, fontFamily: 'NunitoSans-Bold' }}>Service Charge</Text>
                                                <Text style={{ fontSize: 10, color: Colors.descriptionColor }}>RM</Text>
                                            </View>

                                            <View style={{ marginLeft: '3%' }}>
                                                <TouchableOpacity onPress={() => setCurrReportDetailsSort(REPORT_SORT_FIELD_TYPE.USER_ORDER_SERVICE_CHARGE_ASC)}>
                                                    <Entypo name='triangle-up' size={14} color={currReportDetailsSort === REPORT_SORT_FIELD_TYPE.USER_ORDER_SERVICE_CHARGE_ASC ? Colors.secondaryColor : Colors.descriptionColor}></Entypo>
                                                </TouchableOpacity>

                                                <TouchableOpacity onPress={() => setCurrReportDetailsSort(REPORT_SORT_FIELD_TYPE.USER_ORDER_SERVICE_CHARGE_DESC)}>
                                                    <Entypo name='triangle-down' size={14} color={currReportDetailsSort === REPORT_SORT_FIELD_TYPE.USER_ORDER_SERVICE_CHARGE_DESC ? Colors.secondaryColor : Colors.descriptionColor}></Entypo>
                                                </TouchableOpacity>
                                            </View>
                                        </View> */}
                        {/* <View style={{ flexDirection: 'row', flex: 1, borderRightWidth: 1, borderRightColor: 'lightgrey', alignItems: 'center', justifyContent: 'center' }}>
                                            <View style={{ flexDirection: 'column' }}>
                                                <TouchableOpacity onPress={() => {
                                                    if (currReportDetailsSort === REPORT_SORT_FIELD_TYPE.USER_ORDER_GP_ASC) {
                                                        setCurrReportDetailsSort(REPORT_SORT_FIELD_TYPE.USER_ORDER_GP_DESC)
                                                    }
                                                    else {
                                                        setCurrReportDetailsSort(REPORT_SORT_FIELD_TYPE.USER_ORDER_GP_ASC)
                                                    }
                                                }}>
                                                    <Text numberOfLines={2} style={{ fontSize: 13, fontFamily: 'NunitoSans-Bold' }}>GP</Text>
                                                    <Text style={{ fontSize: 10, color: Colors.descriptionColor }}>%</Text>
                                                </TouchableOpacity>
                                            </View>
                                            <View style={{ marginLeft: '3%' }}>
                                                <Entypo name='triangle-up' size={14} color={currReportDetailsSort === REPORT_SORT_FIELD_TYPE.USER_ORDER_GP_ASC ? Colors.secondaryColor : Colors.descriptionColor}></Entypo>

                                                <Entypo name='triangle-down' size={14} color={currReportDetailsSort === REPORT_SORT_FIELD_TYPE.USER_ORDER_GP_DESC ? Colors.secondaryColor : Colors.descriptionColor}></Entypo>
                                            </View>
                                        </View> */}
                        <View
                          style={{
                            flexDirection: 'row',
                            width: '12%',
                            borderRightWidth: 0,
                            borderRightColor: 'lightgrey',
                            alignItems: 'center',
                            justifyContent: 'flex-start',
                            paddingLeft: 10,
                            borderLeftWidth: 1,
                            borderLeftColor: 'lightgrey',
                          }}>
                          <TouchableOpacity
                            onPress={() => {
                              if (
                                currReportDetailsSort ===
                                REPORT_SORT_FIELD_TYPE.USER_ORDER_NET_SALES_ASC
                              ) {
                                setCurrReportDetailsSort(
                                  REPORT_SORT_FIELD_TYPE.USER_ORDER_NET_SALES_DESC,
                                );
                              } else {
                                setCurrReportDetailsSort(
                                  REPORT_SORT_FIELD_TYPE.USER_ORDER_NET_SALES_ASC,
                                );
                              }
                              logEventAnalytics({
                                eventName: ANALYTICS.MODULE_REPORT_CHA_SUM_TABLE_IND_SUM_C_TABLE_NET_SALES,
                                eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_CHA_SUM_TABLE_IND_SUM_C_TABLE_NET_SALES
                              })
                            }}>
                            <View style={{ flexDirection: 'row' }}>
                              <View style={{ flexDirection: 'column' }}>
                                <Text
                                  numberOfLines={2}
                                  style={{
                                    fontSize: switchMerchant ? 10 : 13,
                                    fontFamily: 'NunitoSans-Bold',
                                  }}>
                                  {'Net Sales\n'}
                                </Text>
                                <View style={{ flexDirection: 'row' }}>
                                  <Text
                                    style={{
                                      fontSize: switchMerchant ? 8 : 10,
                                      color: Colors.descriptionColor,
                                    }}>
                                    RM
                                  </Text>
                                  <Text
                                    style={{
                                      fontSize: switchMerchant ? 8 : 10,
                                      color: Colors.primaryColor,
                                    }}>
                                    {' '}
                                    *incl tax
                                  </Text>
                                </View>
                              </View>
                              <View
                                style={{
                                  marginLeft: '3%',
                                  justifyContent: 'space-between',
                                }}>
                                <View>
                                  <Entypo
                                    name="triangle-up"
                                    size={switchMerchant ? 7 : 14}
                                    color={
                                      currReportDetailsSort ===
                                        REPORT_SORT_FIELD_TYPE.USER_ORDER_NET_SALES_ASC
                                        ? Colors.secondaryColor
                                        : Colors.descriptionColor
                                    } />

                                  <Entypo
                                    name="triangle-down"
                                    size={switchMerchant ? 7 : 14}
                                    color={
                                      currReportDetailsSort ===
                                        REPORT_SORT_FIELD_TYPE.USER_ORDER_NET_SALES_DESC
                                        ? Colors.secondaryColor
                                        : Colors.descriptionColor
                                    } />
                                </View>
                                {/* <Text
                                style={{
                                  fontSize: 10,
                                  color: Colors.descriptionColor,
                                }}></Text> */}
                                <Tooltip
                                  isVisible={netSaleTip}
                                  content={
                                    <Text>
                                      Total Sales - Tax - Service Charge
                                    </Text>
                                  }
                                  placement="top"
                                  onClose={() => setNetSaleTip(false)}>
                                  <TouchableOpacity
                                    onPress={() => {
                                      setNetSaleTip(true);

                                      logEventAnalytics({
                                        eventName: ANALYTICS.MODULE_REPORT_CHA_SUM_TABLE_IND_SUM_T_NS_C_ICON_QM,
                                        eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_CHA_SUM_TABLE_IND_SUM_T_NS_C_ICON_QM
                                      })

                                    }}
                                    style={styles.touchable}>
                                    <Feather
                                      name="help-circle"
                                      size={switchMerchant ? 10 : 15}
                                      style={{ color: Colors.primaryColor }}
                                    />
                                  </TouchableOpacity>
                                </Tooltip>
                              </View>
                            </View>
                          </TouchableOpacity>
                        </View>
                        {/* <View style={{ flexDirection: 'row', flex: 3, borderRightWidth: 1, borderRightColor: 'lightgrey', alignItems: 'center', justifyContent: 'center' }}>
                                            <View>
                                                <Text style={{ fontSize: 13, fontFamily: 'NunitoSans-Regular' }}>Average Net Sales</Text>
                                                <Text style={{ fontSize: 10, color: Colors.descriptionColor }}></Text>
                                            </View>
                                        </View> */}
                      </View>
                    )}
                    {isTableLoading && (
                      <View style={{ 
                        flex: 1,
                        backgroundColor: 'rgba(255,255,255,0.8)', 
                        position: 'absolute', 
                        width: '100%', 
                        height: '100%',
                        justifyContent: 'center', 
                        alignItems: 'center',
                        zIndex: 999,
                        pointerEvents: 'auto'
                      }}>
                        <ActivityIndicator size="large" color={Colors.primaryColor} />
                      </View>
                    )}

                    {!showDetails ? (
                      <>
                        {transactionTypeSales.length > 0 ? (
                          <FlatList
                            showsVerticalScrollIndicator={false}
                            ref={flatListRef}
                            data={sortReportDataList(
                              transactionTypeSales.filter((item) => {
                                if (search !== '') {
                                  if (
                                    item.orderType
                                      .toLowerCase()
                                      .includes(search.toLowerCase())
                                  ) {
                                    return true;
                                  } else if (
                                    item.totalTransactions
                                      .toString()
                                      .toLowerCase()
                                      .includes(search.toLowerCase())
                                  ) {
                                    return true;
                                  } else if (
                                    item.totalSales
                                      .toFixed(2)
                                      .toString()
                                      .toLowerCase()
                                      .includes(search.toLowerCase())
                                  ) {
                                    return true;
                                  } else if (
                                    item.totalDiscount
                                      .toFixed(2)
                                      .toString()
                                      .toLowerCase()
                                      .includes(search.toLowerCase())
                                  ) {
                                    return true;
                                  } else if (
                                    item.discount
                                      .toFixed(2)
                                      .toString()
                                      .toLowerCase()
                                      .includes(search.toLowerCase())
                                  ) {
                                    return true;
                                  } else if (
                                    item.tax
                                      .toFixed(2)
                                      .toString()
                                      .toLowerCase()
                                      .includes(search.toLowerCase())
                                  ) {
                                    return true;
                                  } else if (
                                    item.serviceCharge
                                      .toFixed(2)
                                      .toString()
                                      .toLowerCase()
                                      .includes(search.toLowerCase())
                                  ) {
                                    return true;
                                  } else if (
                                    item.netSales
                                      .toFixed(2)
                                      .toString()
                                      .toLowerCase()
                                      .includes(search.toLowerCase())
                                  ) {
                                    return true;
                                  } else if (
                                    item.averageNetSales
                                      .toFixed(2)
                                      .toString()
                                      .toLowerCase()
                                      .includes(search.toLowerCase())
                                  ) {
                                    return true;
                                  } else {
                                    return false;
                                  }
                                } else {
                                  return true;
                                }
                              }),
                              currReportSummarySort,
                            ).slice(
                              (currentPage - 1) * perPage,
                              currentPage * perPage,
                            )}
                            // extraData={transactionTypeSales}
                            renderItem={renderItem}
                            keyExtractor={(item, index) => String(index)}
                            style={{ marginTop: 10 }}
                          />
                        ) : (
                          <View
                            style={{
                              height: windowHeight * 0.4,
                            }}>
                            <View
                              style={{
                                alignItems: 'center',
                                justifyContent: 'center',
                                height: '100%',
                              }}>
                              <Text style={{ color: Colors.descriptionColor }}>
                                - No Data Available -
                              </Text>
                            </View>
                          </View>
                        )}
                      </>
                    ) : (
                      <>
                        {transactionTypeSalesDetails.length > 0 ? (
                          <FlatList
                            showsVerticalScrollIndicator={false}
                            ref={flatListRef}
                            data={sortReportDataList(
                              transactionTypeSalesDetails,
                              currReportDetailsSort,
                            )
                              .filter((item) => {
                                if (search !== '') {
                                  if (
                                    item.orderType
                                      .toLowerCase()
                                      .includes(search.toLowerCase())
                                  ) {
                                    return true;
                                  } else if (
                                    item.totalPrice
                                      .toFixed(2)
                                      .toString()
                                      .toLowerCase()
                                      .includes(search.toLowerCase())
                                  ) {
                                    return true;
                                  } else if (
                                    getOrderDiscountInfoInclOrderBased(item)
                                      .toFixed(2)
                                      .toString()
                                      .toLowerCase()
                                      .includes(search.toLowerCase())
                                  ) {
                                    return true;
                                  } else if (
                                    item.tax
                                      .toFixed(2)
                                      .toString()
                                      .toLowerCase()
                                      .includes(search.toLowerCase())
                                  ) {
                                    return true;
                                  } else if (
                                    moment(item.createdAt)
                                      .format('DD MMM YYY hh:mma')
                                      .toLowerCase()
                                      .includes(search.toLowerCase())
                                  ) {
                                    return true;
                                  } else {
                                    return false;
                                  }
                                } else {
                                  return true;
                                }
                              })
                              .slice(
                                (currentDetailsPage - 1) * perPage,
                                currentDetailsPage * perPage,
                              )}
                            // extraData={transactionTypeSales}
                            renderItem={renderItemDetails}
                            keyExtractor={(item, index) => String(index)}
                            style={{ marginTop: 10 }}
                          />
                        ) : (
                          <View
                            style={{
                              height: windowHeight * 0.4,
                            }}>
                            <View
                              style={{
                                alignItems: 'center',
                                justifyContent: 'center',
                                height: '100%',
                              }}>
                              <Text style={{ color: Colors.descriptionColor }}>
                                - No Data Available -
                              </Text>
                            </View>
                          </View>
                        )}
                      </>
                    )}

                    {/* {searchList ? (

                                <FlatList
                                    data={lists}
                                    extraData={lists}
                                    renderItem={renderSearchItem}
                                    keyExtractor={(item, index) => String(index)}
                                />

                            ) : null} */}
                  </View>

                  {!showDetails ? (
                    <View
                      style={{
                        flexDirection: 'row',
                        marginTop: 10,
                        width: windowWidth * 0.87,
                        alignItems: 'center',
                        alignSelf: 'center',
                        justifyContent: 'flex-end',
                        top:
                          Platform.OS == 'ios'
                            ? pushPagingToTop && keyboardHeight > 0
                              ? -keyboardHeight * 1
                              : 0
                            : 0,
                        // backgroundColor: pushPagingToTop && keyboardHeight > 0 ? Colors.highlightColor : null,
                        // borderWidth: pushPagingToTop && keyboardHeight > 0 ? 1 : 0,
                        // borderColor: pushPagingToTop && keyboardHeight > 0 ? '#E5E5E5' : null,
                        borderRadius:
                          pushPagingToTop && keyboardHeight > 0 ? 8 : 0,
                        paddingHorizontal:
                          pushPagingToTop && keyboardHeight > 0 ? 10 : 0,
                        // shadowOffset: {
                        //     width: 0,
                        //     height: 1,
                        // },
                        // shadowOpacity: 0.22,
                        // shadowRadius: 3.22,
                        // elevation: 1,
                      }}>
                      <Text
                        style={{
                          fontSize: switchMerchant ? 10 : 14,
                          fontFamily: 'NunitoSans-Bold',
                          marginRight: '1%',
                        }}>
                        Items Showed
                      </Text>
                      <View
                        style={{
                          width: Platform.OS === 'ios' ? 65 : '13%', //65,
                          height: switchMerchant ? 20 : 35,
                          backgroundColor: Colors.whiteColor,
                          borderRadius: 10,
                          justifyContent: 'center',
                          paddingHorizontal: Platform.OS === 'ios' ? 0 : 0,
                          //paddingLeft:switchMerchant ? '4.5%': Platform.OS === 'ios' ? '2%' : '4%',
                          // paddingTop: '-60%',
                          borderWidth: 1,
                          borderColor: '#E5E5E5',
                          marginRight: '1%',
                        }}>
                        <RNPickerSelect
                          placeholder={{}}
                          useNativeAndroidPickerStyle={false}
                          style={{
                            inputIOS: {
                              fontSize: switchMerchant ? 10 : 14,
                              fontFamily: 'NunitoSans-Regular',
                              textAlign: 'center',
                            },
                            inputAndroid: {
                              fontSize: switchMerchant ? 10 : 14,
                              fontFamily: 'NunitoSans-Regular',
                              justifyContent: 'center',
                              textAlign: 'center',
                              height: 40,
                              color: 'black',
                            },
                            inputAndroidContainer: { width: '100%' },
                            //backgroundColor: 'red',
                            height: 35,

                            chevronContainer: {
                              display: 'none',
                            },
                            chevronDown: {
                              display: 'none',
                            },
                            chevronUp: {
                              display: 'none',
                            },
                          }}
                          items={TABLE_PAGE_SIZE_DROPDOWN_LIST.concat({
                            label: 'All',
                            value: !showDetails
                              ? transactionTypeSales.length
                              : transactionTypeSalesDetails.length,
                          })}
                          value={perPage}
                          onValueChange={(value, text) => {
                            setPerPage(value);
                            var currentPageTemp =
                              text.length > 0 ? parseInt(text) : 1;

                            setCurrentPage(
                              currentPageTemp > pageCount
                                ? pageCount
                                : currentPageTemp < 1
                                  ? 1
                                  : currentPageTemp,
                            );
                          }}
                        />
                      </View>

                      <Text
                        style={{
                          fontSize: switchMerchant ? 10 : 14,
                          fontFamily: 'NunitoSans-Bold',
                          marginRight: '1%',
                        }}>
                        Page
                      </Text>
                      <View
                        style={{
                          width: switchMerchant ? 65 : 70,
                          height: switchMerchant ? 20 : 35,
                          backgroundColor: Colors.whiteColor,
                          borderRadius: 10,
                          justifyContent: 'center',
                          paddingHorizontal: 22,
                          borderWidth: 1,
                          borderColor: '#E5E5E5',
                        }}>
                        {console.log('currentPage')}
                        {console.log(currentPage)}

                        <TextInput
                          onChangeText={(text) => {
                            var currentPageTemp =
                              text.length > 0 ? parseInt(text) : 1;

                            setCurrentPage(
                              currentPageTemp > pageCount
                                ? pageCount
                                : currentPageTemp < 1
                                  ? 1
                                  : currentPageTemp,
                            );
                          }}
                          placeholderTextColor={Platform.select({
                            ios: '#a9a9a9',
                          })}
                          placeholder={
                            pageCount !== 0 ? currentPage.toString() : '0'
                          }
                          style={{
                            color: 'black',
                            fontSize: switchMerchant ? 10 : 14,
                            fontFamily: 'NunitoSans-Regular',
                            marginTop: Platform.OS === 'ios' ? 0 : -15,
                            marginBottom: Platform.OS === 'ios' ? 0 : -15,
                            textAlign: 'center',
                            width: '100%',
                          }}
                          value={pageCount !== 0 ? currentPage.toString() : '0'}
                          defaultValue={
                            pageCount !== 0 ? currentPage.toString() : '0'
                          }
                          keyboardType={'numeric'}
                          onFocus={() => {
                            setPushPagingToTop(true);
                          }}
                        />
                      </View>
                      <Text
                        style={{
                          fontSize: switchMerchant ? 10 : 14,
                          fontFamily: 'NunitoSans-Bold',
                          marginLeft: '1%',
                          marginRight: '1%',
                        }}>
                        of {pageCount}
                      </Text>
                      <TouchableOpacity
                        style={{
                          width: switchMerchant ? 30 : 45,
                          height: switchMerchant ? 20 : 28,
                          backgroundColor: Colors.primaryColor,
                          alignItems: 'center',
                          justifyContent: 'center',
                        }}
                        onPress={() => {
                          prevPage();

                          logEventAnalytics({
                            eventName: ANALYTICS.MODULE_REPORT_CHA_SUM_TABLE_IND_SUM_C_PREV_PAGE_BUTTON,
                            eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_CHA_SUM_TABLE_IND_SUM_C_NEXT_PAGE_BUTTON
                          })
                        }}>
                        <MaterialIcons
                          name="keyboard-arrow-left"
                          size={switchMerchant ? 20 : 25}
                          style={{ color: Colors.whiteColor }}
                        />
                      </TouchableOpacity>
                      <TouchableOpacity
                        style={{
                          width: switchMerchant ? 30 : 45,
                          height: switchMerchant ? 20 : 28,
                          backgroundColor: Colors.primaryColor,
                          alignItems: 'center',
                          justifyContent: 'center',
                        }}
                        onPress={() => {
                          nextPage();

                          logEventAnalytics({
                            eventName: ANALYTICS.MODULE_REPORT_CHA_SUM_TABLE_IND_SUM_C_NEXT_PAGE_BUTTON,
                            eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_CHA_SUM_TABLE_IND_SUM_C_NEXT_PAGE_BUTTON
                          })
                        }}>
                        <MaterialIcons
                          name="keyboard-arrow-right"
                          size={switchMerchant ? 20 : 25}
                          style={{ color: Colors.whiteColor }}
                        />
                      </TouchableOpacity>
                    </View>
                  ) : (
                    <View
                      style={{
                        flexDirection: 'row',
                        marginTop: 10,
                        width: windowWidth * 0.87,
                        alignItems: 'center',
                        alignSelf: 'center',
                        justifyContent: 'flex-end',
                        top:
                          Platform.OS == 'ios'
                            ? pushPagingToTop && keyboardHeight > 0
                              ? -keyboardHeight * 1
                              : 0
                            : 0,
                        // backgroundColor: pushPagingToTop && keyboardHeight > 0 ? Colors.highlightColor : null,
                        // borderWidth: pushPagingToTop && keyboardHeight > 0 ? 1 : 0,
                        // borderColor: pushPagingToTop && keyboardHeight > 0 ? '#E5E5E5' : null,
                        borderRadius:
                          pushPagingToTop && keyboardHeight > 0 ? 8 : 0,
                        paddingHorizontal:
                          pushPagingToTop && keyboardHeight > 0 ? 10 : 0,
                        // shadowOffset: {
                        //     width: 0,
                        //     height: 1,
                        // },
                        // shadowOpacity: 0.22,
                        // shadowRadius: 3.22,
                        // elevation: 1,
                      }}>
                      <Text
                        style={{
                          fontSize: switchMerchant ? 10 : 14,
                          fontFamily: 'NunitoSans-Bold',
                          marginRight: '1%',
                        }}>
                        Items Showed
                      </Text>
                      <View
                        style={{
                          width: Platform.OS === 'ios' ? 65 : '13%', //65,
                          height: switchMerchant ? 20 : 35,
                          backgroundColor: Colors.whiteColor,
                          borderRadius: 10,
                          justifyContent: 'center',
                          paddingHorizontal: Platform.OS === 'ios' ? 0 : 0,
                          //paddingLeft:switchMerchant ? '4.5%': Platform.OS === 'ios' ? '2%' : '4%',
                          // paddingTop: '-60%',
                          borderWidth: 1,
                          borderColor: '#E5E5E5',
                          marginRight: '1%',
                        }}>
                        <RNPickerSelect
                          placeholder={{}}
                          useNativeAndroidPickerStyle={false}
                          style={{
                            inputIOS: {
                              fontSize: switchMerchant ? 10 : 14,
                              fontFamily: 'NunitoSans-Regular',
                              textAlign: 'center',
                            },
                            inputAndroid: {
                              fontSize: switchMerchant ? 10 : 14,
                              fontFamily: 'NunitoSans-Regular',
                              justifyContent: 'center',
                              textAlign: 'center',
                              height: 40,
                              color: 'black',
                            },
                            inputAndroidContainer: { width: '100%' },
                            //backgroundColor: 'red',
                            height: 35,

                            chevronContainer: {
                              display: 'none',
                            },
                            chevronDown: {
                              display: 'none',
                            },
                            chevronUp: {
                              display: 'none',
                            },
                          }}
                          items={TABLE_PAGE_SIZE_DROPDOWN_LIST.concat({
                            label: 'All',
                            value: !showDetails
                              ? transactionTypeSales.length
                              : transactionTypeSalesDetails.length,
                          })}
                          value={perPage}
                          onValueChange={(value) => {
                            setPerPage(value);
                          }}
                        />
                      </View>

                      <Text
                        style={{
                          fontSize: switchMerchant ? 10 : 14,
                          fontFamily: 'NunitoSans-Bold',
                          marginRight: '1%',
                        }}>
                        Page
                      </Text>
                      <View
                        style={{
                          width: switchMerchant ? 65 : 70,
                          height: switchMerchant ? 20 : 35,
                          backgroundColor: Colors.whiteColor,
                          borderRadius: 10,
                          justifyContent: 'center',
                          paddingHorizontal: 22,
                          borderWidth: 1,
                          borderColor: '#E5E5E5',
                        }}>
                        {console.log('currentDetailsPage')}
                        {console.log(currentDetailsPage)}

                        <TextInput
                          onChangeText={(text) => {
                            var currentPageTemp =
                              text.length > 0 ? parseInt(text) : 1;

                            setCurrentDetailsPage(
                              currentPageTemp > pageCount
                                ? pageCount
                                : currentPageTemp < 1
                                  ? 1
                                  : currentPageTemp,
                            );
                          }}
                          placeholder={
                            pageCount !== 0
                              ? currentDetailsPage.toString()
                              : '0'
                          }
                          placeholderTextColor={Platform.select({
                            ios: '#a9a9a9',
                          })}
                          style={{
                            color: 'black',
                            fontSize: switchMerchant ? 10 : 14,
                            fontFamily: 'NunitoSans-Regular',
                            marginTop: Platform.OS === 'ios' ? 0 : -15,
                            marginBottom: Platform.OS === 'ios' ? 0 : -15,
                            textAlign: 'center',
                            width: '100%',
                          }}
                          value={
                            pageCount !== 0
                              ? currentDetailsPage.toString()
                              : '0'
                          }
                          defaultValue={
                            pageCount !== 0
                              ? currentDetailsPage.toString()
                              : '0'
                          }
                          keyboardType={'numeric'}
                          onFocus={() => {
                            setPushPagingToTop(true);
                          }}
                        />
                      </View>
                      <Text
                        style={{
                          fontSize: switchMerchant ? 10 : 14,
                          fontFamily: 'NunitoSans-Bold',
                          marginLeft: '1%',
                          marginRight: '1%',
                        }}>
                        of {pageCount}
                      </Text>
                      <TouchableOpacity
                        style={{
                          width: switchMerchant ? 30 : 45,
                          height: switchMerchant ? 20 : 28,
                          backgroundColor: Colors.primaryColor,
                          alignItems: 'center',
                          justifyContent: 'center',
                        }}
                        onPress={() => {
                          prevDetailsPage();

                          logEventAnalytics({
                            eventName: ANALYTICS.MODULE_REPORT_CHA_SUM_TABLE_IND_TABLE_C_P_P_B,
                            eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_CHA_SUM_TABLE_IND_TABLE_C_P_P_B
                          })
                        }}>
                        <MaterialIcons
                          name="keyboard-arrow-left"
                          size={switchMerchant ? 20 : 25}
                          style={{ color: Colors.whiteColor }}
                        />
                      </TouchableOpacity>
                      <TouchableOpacity
                        style={{
                          width: switchMerchant ? 30 : 45,
                          height: switchMerchant ? 20 : 28,
                          backgroundColor: Colors.primaryColor,
                          alignItems: 'center',
                          justifyContent: 'center',
                        }}
                        onPress={() => {
                          nextDetailsPage();

                          logEventAnalytics({
                            eventName: ANALYTICS.MODULE_REPORT_CHA_SUM_TABLE_IND_TABLE_C_P_N_B,
                            eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_CHA_SUM_TABLE_IND_TABLE_C_P_N_B
                          })
                        }}>
                        <MaterialIcons
                          name="keyboard-arrow-right"
                          size={switchMerchant ? 20 : 25}
                          style={{ color: Colors.whiteColor }}
                        />
                      </TouchableOpacity>
                    </View>
                  )}
                </View>
              </View>
              <ModalView
                supportedOrientations={['landscape', 'portrait']}
                style={{ flex: 1 }}
                visible={visible}
                transparent
                animationType="slide">
                <KeyboardAvoidingView
                  //behavior="padding"
                  style={{
                    backgroundColor: 'rgba(0,0,0,0.5)',
                    flex: 1,
                    justifyContent: 'center',
                    alignItems: 'center',
                    minHeight: windowHeight,
                    top:
                      Platform.OS === 'ios' && keyboardHeight > 0
                        ? -keyboardHeight * 0.5
                        : 0,
                  }}>
                  <View style={[styles.confirmBox1, { ...getTransformForModalInsideNavigation(), }]}>
                    <Text
                      style={{
                        fontSize: 24,
                        justifyContent: 'center',
                        alignSelf: 'center',
                        marginTop: 40,
                        fontFamily: 'NunitoSans-Bold',
                      }}>
                      Enter your email
                    </Text>
                    <View
                      style={{
                        justifyContent: 'center',
                        alignSelf: 'center',
                        alignContent: 'center',
                        marginTop: 20,
                        flexDirection: 'row',
                        width: '80%',
                      }}>
                      <View
                        style={{ justifyContent: 'center', marginHorizontal: 5 }}>
                        <Text
                          style={{
                            color: Colors.descriptionColor,
                            fontSize: 20,
                          }}>
                          email:
                        </Text>
                      </View>
                      <TextInput
                        underlineColorAndroid={Colors.fieldtBgColor}
                        style={[styles.textInput8, { paddingLeft: 5 }]}
                        placeholder="Enter your email"
                        // style={{
                        //     // paddingLeft: 1,
                        // }}
                        //defaultValue={extentionCharges}
                        onChangeText={(text) => {
                          // setState({ exportEmail: text });
                          setExportEmail(text);
                        }}
                        value={exportEmail}
                        placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                      />
                    </View>
                    <Text
                      style={{
                        fontSize: 20,
                        fontFamily: 'NunitoSans-Bold',
                        marginTop: 25,
                        justifyContent: 'center',
                        alignSelf: 'center',
                        alignContent: 'center',
                      }}>
                      Share As:
                    </Text>

                    {/* Share file using email */}
                    <View
                      style={{
                        justifyContent: 'space-between',
                        alignSelf: 'center',
                        marginTop: 10,
                        flexDirection: 'row',
                        width: '80%',
                      }}>
                      <TouchableOpacity
                        style={[
                          styles.modalSaveButton1,
                          {
                            zIndex: -1,
                          },
                        ]}
                        onPress={() => { }}>
                        <Text
                          style={[
                            styles.modalDescText,
                            { color: Colors.primaryColor },
                          ]}>
                          Excel
                        </Text>
                      </TouchableOpacity>

                      <TouchableOpacity
                        style={[
                          styles.modalSaveButton1,
                          {
                            zIndex: -1,
                          },
                        ]}
                        onPress={() => { }}>
                        <Text
                          style={[
                            styles.modalDescText,
                            { color: Colors.primaryColor },
                          ]}>
                          CSV
                        </Text>
                      </TouchableOpacity>
                      <TouchableOpacity
                        style={[
                          styles.modalSaveButton1,
                          {
                            zIndex: -1,
                          },
                        ]}
                        onPress={() => { }}>
                        <Text
                          style={[
                            styles.modalDescText,
                            { color: Colors.primaryColor },
                          ]}>
                          PDF
                        </Text>
                      </TouchableOpacity>
                    </View>
                    <View
                      style={{
                        alignSelf: 'center',
                        marginTop: 20,
                        justifyContent: 'center',
                        alignItems: 'center',
                        // width: 260,
                        width: windowWidth * 0.2,
                        height: 60,
                        alignContent: 'center',
                        flexDirection: 'row',
                        marginTop: 40,
                      }}>
                      <TouchableOpacity
                        onPress={emailTransaction}
                        style={{
                          backgroundColor: Colors.fieldtBgColor,
                          width: '100%',
                          justifyContent: 'center',
                          alignItems: 'center',
                          alignContent: 'center',
                          height: 60,
                          borderBottomLeftRadius: 10,
                          borderRightWidth: StyleSheet.hairlineWidth,
                          borderTopWidth: StyleSheet.hairlineWidth,
                        }}>
                        <Text
                          style={{
                            fontSize: 22,
                            color: Colors.primaryColor,
                            fontFamily: 'NunitoSans-SemiBold',
                          }}>
                          Email
                        </Text>
                      </TouchableOpacity>
                      <TouchableOpacity
                        onPress={() => {
                          // setState({ visible: false });
                          setVisible(false);
                        }}
                        style={{
                          backgroundColor: Colors.fieldtBgColor,
                          width: '100%',
                          justifyContent: 'center',
                          alignItems: 'center',
                          alignContent: 'center',
                          height: 60,
                          borderBottomRightRadius: 10,
                          borderTopWidth: StyleSheet.hairlineWidth,
                        }}>
                        <Text
                          style={{
                            fontSize: 22,
                            color: Colors.descriptionColor,
                            fontFamily: 'NunitoSans-SemiBold',
                          }}>
                          Cancel
                        </Text>
                      </TouchableOpacity>
                    </View>
                  </View>
                </KeyboardAvoidingView>
              </ModalView>
              <ModalView
                supportedOrientations={['landscape', 'portrait']}
                style={{ flex: 1 }}
                visible={visible1}
                transparent
                animationType="slide">
                <View
                  style={{
                    backgroundColor: 'rgba(0,0,0,0.5)',
                    flex: 1,
                    justifyContent: 'center',
                    alignItems: 'center',
                    minHeight: windowHeight,
                  }}>
                  <View style={[styles.confirmBox, { ...getTransformForModalInsideNavigation(), }]}>
                    <View
                      style={{
                        flex: 3,
                        borderBottomWidth: StyleSheet.hairlineWidth,
                        justifyContent: 'center',
                        alignItems: 'center',
                      }} />
                    <View style={{ flex: 1, flexDirection: 'row' }}>
                      <TouchableOpacity
                        style={{
                          flex: 1,
                          borderRightWidth: StyleSheet.hairlineWidth,
                          justifyContent: 'center',
                        }}
                        onPress={() => {
                          email();
                        }}>
                        <Text
                          style={{
                            color: Colors.primaryColor,
                            fontSize: 24,
                            fontWeight: '400',
                            textAlign: 'center',
                          }}>
                          Email
                        </Text>
                      </TouchableOpacity>
                      <TouchableOpacity
                        style={{ flex: 1, justifyContent: 'center' }}
                        onPress={() => {
                          setState({ visible1: !visible1 });
                        }}>
                        <Text
                          style={{
                            color: Colors.descriptionColor,
                            fontSize: 24,
                            fontWeight: '400',
                            textAlign: 'center',
                          }}>
                          Cancel
                        </Text>
                      </TouchableOpacity>
                    </View>
                  </View>
                </View>
              </ModalView>
            </KeyboardAvoidingView>
          </ScrollView>
        </ScrollView>
      </View>
    </UserIdleWrapper>)
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
    flexDirection: 'row',
  },
  sidebar: {
    width: Dimensions.get('window').width * Styles.sideBarWidth,
    // shadowColor: '#000',
    // shadowOffset: {
    //   width: 0,
    //   height: 8,
    // },
    // shadowOpacity: 0.44,
    // shadowRadius: 10.32,

    // elevation: 16,
  },
  content: {
    padding: 20,
    width: Dimensions.get('window').width * (1 - Styles.sideBarWidth),
    backgroundColor: Colors.highlightColor,
  },
  headerLogo: {
    width: 112,
    height: 25,
    marginLeft: 10,
  },
  confirmBox: {
    // width: '30%',
    // height: '30%',
    // borderRadius: 30,
    // backgroundColor: Colors.whiteColor,
    width: Dimensions.get('window').width * 0.4,
    height: Dimensions.get('window').height * 0.3,
    borderRadius: 12,
    backgroundColor: Colors.whiteColor,
    justifyContent: 'space-between',
  },

  modalContainer: {
    flex: 1,
    backgroundColor: Colors.modalBgColor,
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalView: {
    height: Dimensions.get('window').width * 0.2,
    width: Dimensions.get('window').width * 0.3,
    backgroundColor: Colors.whiteColor,
    borderRadius: 12,
    padding: Dimensions.get('window').width * 0.03,
    alignItems: 'center',
    justifyContent: 'center',
  },
  closeButton: {
    position: 'absolute',
    right: Dimensions.get('window').width * 0.02,
    top: Dimensions.get('window').width * 0.02,

    elevation: 1000,
    zIndex: 1000,
  },
  modalTitle: {
    alignItems: 'center',
    top: '20%',
    position: 'absolute',
  },
  modalBody: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalTitleText: {
    fontFamily: 'NunitoSans-Bold',
    textAlign: 'center',
    fontSize: 20,
  },
  modalDescText: {
    fontFamily: 'NunitoSans-SemiBold',
    fontSize: 18,
    color: Colors.fieldtTxtColor,
  },
  modalBodyText: {
    flex: 1,
    fontFamily: 'NunitoSans-SemiBold',
    fontSize: 25,
    width: '20%',
  },
  modalSaveButton: {
    width: Dimensions.get('window').width * 0.15,
    backgroundColor: Colors.fieldtBgColor,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 8,

    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 1,

    marginVertical: 10,
  },
  modalSaveButton1: {
    width: Dimensions.get('window').width * 0.1,
    backgroundColor: Colors.fieldtBgColor,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 8,

    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 1,

    marginVertical: 10,
  },
  confirmBox1: {
    width: Dimensions.get('window').width * 0.4,
    height: Dimensions.get('window').height * 0.4,
    borderRadius: 12,
    backgroundColor: Colors.whiteColor,
    justifyContent: 'space-between',
  },
  submitText: {
    height:
      Platform.OS == 'ios'
        ? Dimensions.get('window').height * 0.06
        : Dimensions.get('window').height * 0.07,
    paddingVertical: 5,
    paddingHorizontal: 20,
    flexDirection: 'row',
    color: '#4cd964',
    textAlign: 'center',
    borderRadius: 10,
    borderWidth: 1,
    borderColor: Colors.primaryColor,
    justifyContent: 'center',
    alignContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  headerLeftStyle: {
    width: Dimensions.get('screen').width * 0.17,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
export default ReportSalesTransaction;
