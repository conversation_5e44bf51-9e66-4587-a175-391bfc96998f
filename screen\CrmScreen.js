import { Text } from "react-native-fast-text";
import React, { Component, useReducer, useState, useEffect, useCallback } from 'react';
import {
  StyleSheet,
  Image,
  View,
  Alert,
  TouchableOpacity,
  Dimensions,
  Modal as ModalComponent,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
  useWindowDimensions,
} from 'react-native';
import { ModalView as ModalViewComponent } from 'react-native-multiple-modals';
import Colors from '../constant/Colors';
import SideBar from './SideBar';
import Icon from 'react-native-vector-icons/Feather';
import AntDesign from 'react-native-vector-icons/AntDesign';
import Entypo from 'react-native-vector-icons/Entypo';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import EvilIcons from 'react-native-vector-icons/EvilIcons';
import { TextInput, FlatList, ScrollView } from 'react-native-gesture-handler';
import API from '../constant/API';
import ApiClient from '../util/ApiClient';
import * as User from '../util/User';
import AsyncStorage from '@react-native-async-storage/async-storage';
import CheckBox from '@react-native-community/checkbox';
import moment from 'moment';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import Styles from '../constant/Styles';
import {
  isTablet,
  getTransformForModalInsideNavigation,
  getTransformForScreenInsideNavigation,
  logEventAnalytics,
} from '../util/common';
import { OutletStore } from '../store/outletStore';
import { MerchantStore } from '../store/merchantStore';
import { UserStore } from '../store/userStore';
import Upload from '../assets/svg/Upload';
import Download from '../assets/svg/Download';
import RNFetchBlob from 'rn-fetch-blob';
import DocumentPicker from 'react-native-document-picker';
import {
  listenToUserChangesMerchant,
  listenToMerchantIdChangesMerchant,
  listenToCurrOutletIdChangesWaiter,
  listenToAllOutletsChangesMerchant,
  listenToCommonChangesMerchant,
  listenToSelectedOutletItemChanges,
  convertArrayToCSV,
  listenToSelectedOutletTableIdChanges,
  requestNotificationsPermission,
  autofitColumns,
  getImageFromFirebaseStorage,
  getPathForFirebaseStorageFromBlob,
  uploadFileToFirebaseStorage,
  generateEmailReport,
  fetchNextBatchCRMV2,
} from '../util/common';
import Feather from 'react-native-vector-icons/Feather';
import DropDownPicker from 'react-native-dropdown-picker';
import Ionicon from 'react-native-vector-icons/Ionicons';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import { CommonStore } from '../store/commonStore';
import AsyncImage from '../components/asyncImage';
import { useKeyboard } from '../hooks';
import XLSX from 'xlsx';
import { zip, unzip, unzipAssets, subscribe } from 'react-native-zip-archive';
const RNFS = require('@dr.pogodin/react-native-fs');
import 'react-native-get-random-values';
import { v4 as uuidv4 } from 'uuid';
import Switch from 'react-native-switch-pro';
import {
  USER_SORT_FIELD_TYPE,
  REPORT_SORT_FIELD_TYPE_COMPARE,
  USER_SORT_FIELD_TYPE_VALUE,
  REPORT_SORT_COMPARE_OPERATOR,
  EMAIL_REPORT_TYPE,
  EXPAND_TAB_TYPE,
  APP_TYPE,
} from '../constant/common';
import {
  ORDER_TYPE_PARSED,
  USER_POINTS_TRANSACTION_TYPE,
} from '../constant/common';
import Coins from '../assets/svg/Coins';
import { useFocusEffect } from '@react-navigation/native';
import UserIdleWrapper from '../components/userIdleWrapper';
import { ANALYTICS, ANALYTICS_PARSED } from '../constant/analytics';
import GCalendar from '../assets/svg/GCalendar';
import APILocal from '../util/apiLocalReplacers';
import { firebase } from '@react-native-firebase/firestore';
import { Collections } from '../constant/firebase';
import { awsBucket } from "../constant/env";
import { TempStore } from "../store/tempStore";

////////////////////////////////////
const ModalView = Platform.OS === 'android' ? ModalComponent : ModalViewComponent;

///////////////////////////////////

///////////////////////////////////

const CrmScreen = (props) => {
  const { navigation } = props;

  ///////////////////////////////////////////////////////////

  const [isMounted, setIsMounted] = useState(true);

  useFocusEffect(
    useCallback(() => {
      setIsMounted(true);
      return () => {
        setIsMounted(false);
      };
    }, [])
  );

  ///////////////////////////////////////////////////////////

  const { width: windowWidth, height: windowHeight } = useWindowDimensions();

  //////////////////////////////////// UseState Here

  const [keyboardHeight] = useKeyboard();
  const [switchMerchant, setSwitchMerchant] = useState(isTablet() ? false : true);
  const firebaseUid = UserStore.useState((s) => s.firebaseUid);
  const userName = UserStore.useState((s) => s.name);
  const merchantId = UserStore.useState((s) => s.merchantId);
  const merchantName = MerchantStore.useState((s) => s.name);
  const merchantLogo = MerchantStore.useState((s) => s.logo);

  const [list1, setList1] = useState(true);
  const [customerList, setCustomerList] = useState([]);

  const [perPage, setPerPage] = useState(100);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageCount, setPageCount] = useState(0);
  const [page, setPage] = useState(0);

  const [search, setSearch] = useState('');

  const [expandViewDict, setExpandViewDict] = useState({});
  const [expandOrder, setExpandOrder] = useState(false);

  const [visible, setVisible] = useState(false);

  const [controller, setController] = useState({});

  const [oriList, setOriList] = useState([]);
  const [offset, setOffset] = useState(0);

  /////////////////////////////////////////////

  const [headerSorting, setHeaderSorting] = useState([]);

  //////////////////////////////////////////////////////////////

  const [customerFirstVisitDate, setCustomerFirstVisitDate] = useState('');
  const [customerLastVisitDate, setCustomerLastVisitDate] = useState('');

  const [targetOutletDropdownList, setTargetOutletDropdownList] = useState([]);
  const [selectedTargetOutletId, setSelectedTargetOutletId] = useState('');

  const [expandTimeline, setExpandTimeline] = useState(false);

  const [isActiveMember, setIsActiveMember] = useState(true);

  const [usersExpandedDict, setUsersExpandedDict] = useState({});

  const [importModal, setImportModal] = useState(false);
  const [exportModal, setExportModal] = useState(false);

  const currOutletId = MerchantStore.useState((s) => s.currOutletId);
  const allOutlets = MerchantStore.useState((s) => s.allOutlets);

  const crmUsers = OutletStore.useState((s) => s.crmUsers);

  const selectedCustomerDineInOrders = OutletStore.useState(
    (s) => s.selectedCustomerDineInOrders,
  );
  const isLoading = CommonStore.useState((s) => s.isLoading);

  const currOutletTaxes = CommonStore.useState((s) => s.currOutletTaxes);

  const outletSelectDropdownView = CommonStore.useState(
    (s) => s.outletSelectDropdownView,
  );
  const selectedCustomerOrders = OutletStore.useState(
    (s) => s.selectedCustomerOrders,
  );
  const outletTablesDict = OutletStore.useState((s) => s.outletTablesDict);

  const [phTotalPayment, setPhTotalPayment] = useState(0);

  const [exportEmail, setExportEmail] = useState('');
  const [isLoadingExcel, setIsLoadingExcel] = useState(false);
  const [isLoadingCsv, setIsLoadingCsv] = useState(false);

  //////////////////////////////////////////////////////////////

  const [overviewCustomersPast30Days, setOverviewCustomersPast30Days] =
    useState(0);
  const [totalCustomersSpending, setTotalCustomersSpending] = useState(0);

  const [
    averageSpendingPer30DaysEmailDict,
    setAverageSpendingPer30DaysEmailDict,
  ] = useState({});

  const allOutletUserOrderDone = TempStore.useState(
    (s) => s.allOutletUserOrderDoneProcessed,
  );
  const allOutletsUserOrdersDoneRaw = OutletStore.useState((s) => s.allOutletsUserOrdersDone,);
  // const [allOutletsUserOrdersDone, setAllOutletsUserOrdersDone] = useState([]);

  const ptTimestamp = OutletStore.useState(s => s.ptTimestamp);
  const pteTimestamp = OutletStore.useState(s => s.pteTimestamp);
  const [filterAppType, setFilterAppType] = useState([APP_TYPE.WEB_ORDER, APP_TYPE.MERCHANT, APP_TYPE.USER, APP_TYPE.WAITER]);

  const [pushPagingToTop, setPushPagingToTop] = useState(false);

  const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
  const expandTab = CommonStore.useState((s) => s.expandTab);
  const currPageStack = CommonStore.useState((s) => s.currPageStack);

  const crmNextBatch = OutletStore.useState((s) => s.crmNextBatch);
  const lastDoc = OutletStore.useState((s) => s.crmLastDoc);

  const lastDocArr = OutletStore.useState(s => s.lastDocArr);

  const [showPurchaseDateS, setShowPurchaseDateS] = useState(false);
  const [showPurchaseDateE, setShowPurchaseDateE] = useState(false);
  const [showLastVisitMin, setShowLastVisitMin] = useState(false);
  const [showLastVisitMax, setShowLastVisitMax] = useState(false);
  const [purchaseStart, setPurchaseStart] = useState(Date.now());
  const [purchaseEnd, setPurchaseEnd] = useState(Date.now());
  const [lastVisitMin, setLastVisitMin] = useState(Date.now());
  const [lastVisitMax, setLastVisitMax] = useState(Date.now());
  const [purchaseAmountMin, setPurchaseAmountMin] = useState(0);
  const [purchaseAmountMax, setPurchaseAmountMax] = useState(0);
  const [segmentName, setSegmentName] = useState('');
  const [minPointEarned, setMinPointEarned] = useState(0);
  const [isFiltered, setIsFiltered] = useState(false);
  const filteredCRMList = OutletStore.useState(s => s.filteredCRMList);
  const [isSelectedAll, setIsSelectedAll] = useState(true); // To control 'Select All' checkbox
  const crmSegments = OutletStore.useState((s) => s.crmSegments);

  const [showDateTimePicker, setShowDateTimePicker] = useState(false);
  const [showDateTimePicker1, setShowDateTimePicker1] = useState(false);

  const historyStartDate = CommonStore.useState(s => s.historyStartDate);
  const historyEndDate = CommonStore.useState(s => s.historyEndDate);

  const currOutlet = MerchantStore.useState(s => s.currOutlet);
  const [searchList, setSearchList] = useState([]);
  const loyaltyTier = OutletStore.useState(s => s.loyaltyTier);
  // const crmUsersNoLimit = OutletStore.useState(s => s.crmUsersNoLimit);

  //////////////////////////////////////////////////////////////

  //////////////////////////////////// UseEffect here

  useEffect(() => {
    setLastVisitMin(Date.now());
    setLastVisitMax(Date.now());
    setPurchaseAmountMin(0);
    setPurchaseAmountMax(0);
    setIsFiltered(false);
    setSegmentName('');
  }, [])

  useEffect(() => {
    if (currentPage !== 1) {
      setCurrentPage(1)
    }
  }, [search])

  useEffect(() => {
    var allOutletsUserOrdersDoneTemp = [];

    if (isMounted) {
      var currDateTime = moment().valueOf();

      if (global.payoutTransactions.length > 0) {
        for (var j = 0; j < global.payoutTransactions.length; j++) {
          allOutletsUserOrdersDoneTemp = allOutletsUserOrdersDoneTemp.concat(
            (filterAppType && filterAppType.length > 0)
              ?
              (global.payoutTransactions[j].userOrdersFigures ? global.payoutTransactions[j].userOrdersFigures : [])
              :
              (global.payoutTransactions[j].userOrdersFigures ? global.payoutTransactions[j].userOrdersFigures : []).filter((item) =>
                //filterChartItems(item, appliedChartFilterQueriesLineChart),
                (filterAppType.includes(item.appType))
              )
          );
        }

        for (var j = 0; j < global.payoutTransactionsExtend.length; j++) {
          allOutletsUserOrdersDoneTemp = allOutletsUserOrdersDoneTemp.concat(
            (filterAppType && filterAppType.length > 0)
              ?
              (global.payoutTransactionsExtend[j].userOrdersFigures ? global.payoutTransactionsExtend[j].userOrdersFigures : [])
              :
              (global.payoutTransactionsExtend[j].userOrdersFigures ? global.payoutTransactionsExtend[j].userOrdersFigures : []).filter((item) =>
                //filterChartItems(item, appliedChartFilterQueriesLineChart),
                (filterAppType.includes(item.appType))
              )
          );
        }

        const startTime = moment().set({ hour: 0, minute: 0, second: 0 }); // Set the start time to 12:00am
        const endTime = moment().set({ hour: 5, minute: 55, second: 0 }); // Set the end time to 05:55am

        for (var i = 0; i < allOutletsUserOrdersDoneRaw.length; i++) {
          if (
            moment(allOutletsUserOrdersDoneRaw[i].createdAt).isSame(currDateTime, 'day')
            ||
            (
              moment(currDateTime).isBetween(startTime, endTime)
              &&
              moment(currDateTime).add(-1, 'day').isSame(allOutletsUserOrdersDoneRaw[i].createdAt, 'day')
            )
          ) {
            if (filterAppType.includes(allOutletsUserOrdersDoneRaw[i].appType)) {
              allOutletsUserOrdersDoneTemp.push(allOutletsUserOrdersDoneRaw[i]);
            }
          }
        }
      }
      else {
        allOutletsUserOrdersDoneTemp = allOutletsUserOrdersDoneRaw.filter((item) =>
          (filterAppType.includes(item.appType))
        );
      }

      // setAllOutletsUserOrdersDone(allOutletsUserOrdersDoneTemp);
    }
    else {
      // setAllOutletsUserOrdersDone([]);
    }
  }, [
    allOutletsUserOrdersDoneRaw,

    ptTimestamp,
    pteTimestamp,

    isMounted,
  ]);

  const filterCRMUsers = () => {
    CommonStore.update(s => {
      s.isLoading = true;
    })

    let body = {
      outletId: currOutletId,
      purchaseAmountMin: parseInt(purchaseAmountMin) || null,
      purchaseAmountMax: parseInt(purchaseAmountMax) || null,
      lastVisitDateMin: moment(lastVisitMin).startOf('day').valueOf(),
      lastVisitDateMax: moment(lastVisitMax).endOf('day').valueOf(),
    };

    APILocal.filterCRMUser({ body })
      .then((result) => {
        if (result.status) {
          let filteredCRMList = result.crmUserList;

          if (minPointEarned > 0) {
            filteredCRMList = filteredCRMList.filter(user => user.totalSpend * 0.1 >= minPointEarned);
          }

          let mergedCRMUsers = [];

          mergedCRMUsers = (lastDocArr.length > 0 ? crmNextBatch : crmUsers)
            .filter(user => filteredCRMList.some(item => item.phoneNo === user.number))
            .map(user => {
              const match = filteredCRMList.find(item => item.phoneNo === user.number);
              return {
                ...user,
                lastVisitDate: match ? match.lastVisitDate : null,
                totalSpend: match ? match.totalSpend : 0,
                isChecked: true,
              };
            });

          OutletStore.update((s) => {
            s.filteredCRMList = mergedCRMUsers;
          });
        }
      })
      .catch((err) => {
        console.error("Error filtering CRM users:", err);
      })
      .finally(() => {
        CommonStore.update(s => {
          s.isLoading = false;
        })
      });
  };

  // Test Create Segment
  // useEffect(() => {
  const createFilteredCRMSegment = () => {
    const selectedUsers = filteredCRMList.filter(user => user.isChecked);
    var isExisted = false;

    for (var i = 0; i < crmSegments.length; i++) {
      if (crmSegments[i].name === segmentName) {
        isExisted = true;
        break;
      }
    }
    if (isExisted) {
      Alert.alert('Info', 'Segment exists')
    } else {
      let body = {
        merchantId,
        crmUserList: selectedUsers,//[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13],
        segmentName: segmentName,
      };

      APILocal.createSegmentsWithCRMUser({ body })
        .then((result) => {
          if (result && result.status === 'success') {
            Alert.alert('Success', 'Segment Tag has been created');
          }
        })
        .catch((error) => {
          console.error("Error creating segment:", error);
        });
    }
  };
  // }, [])

  useEffect(() => {
    var overviewCustomersPast30DaysTemp = 0;
    var overviewCustomersPast30DaysTempCount = 0;
    var totalCustomersSpendingTemp = 0;

    var averageSpendingPer30DaysEmailDictTemp = {};

    if (currOutletId && currOutletId.length > 0) {
      var currOutletUserOrdersDone = allOutletUserOrderDone.filter(
        (order) => order.outletId === currOutletId
        // && (
        //   moment(order.completedDate).diff(
        //     moment(),
        //     'day',
        //   ) <= 30
        // ),
      );

      console.log(`crm test - currOutletUserOrdersDone.length: ${currOutletUserOrdersDone.length} - ${moment().format('YYYY-MM-DD hh:mm A')}`);

      // var currOutletUserOrdersDone = allOutletsUserOrdersDone;

      for (let i = 0; i < crmUsers.length; i++) {
        averageSpendingPer30DaysEmailDictTemp[crmUsers[i].email] = 0;

        var isCountedUser = false;

        let currOutletUserOrdersDoneFiltered = currOutletUserOrdersDone.filter(order => {
          if (
            order.crmUserId &&
            order.crmUserId === crmUsers[i].uniqueId
          ) {
            return true;
          } else if (
            order.userId &&
            order.userId === crmUsers[i].userId
          ) {
            return true;
          } else if (
            order.userPhone &&
            order.userPhone === crmUsers[i].number
          ) {
            return true;
          }
          else {
            return false;
          }
        });

        console.log(crmUsers[i].name);
        console.log(crmUsers[i].email);
        console.log(crmUsers[i].uniqueId);
        console.log(crmUsers[i].userId);
        console.log(crmUsers[i].number);
        console.log(`crm test - currOutletUserOrdersDoneFiltered.length: ${currOutletUserOrdersDoneFiltered.length} - ${moment().format('YYYY-MM-DD hh:mm A')}`);

        for (var j = 0; j < currOutletUserOrdersDoneFiltered.length; j++) {
          // const finalPriceRoundedUp =
          //   Math.round(currOutletUserOrdersDoneFiltered[j].finalPrice * 20) / 20;

          const finalPriceRoundedUp = currOutletUserOrdersDoneFiltered[j].finalPrice;

          totalCustomersSpendingTemp += finalPriceRoundedUp;

          if (!isCountedUser) {
            isCountedUser = true;
            overviewCustomersPast30DaysTempCount += 1;
          }
          averageSpendingPer30DaysEmailDictTemp[crmUsers[i].email] +=
            finalPriceRoundedUp;
        }

        // for (var j = 0; j < currOutletUserOrdersDone.length; j++) {
        //   const finalPriceRoundedUp =
        //     Math.round(currOutletUserOrdersDone[j].finalPrice * 20) / 20;

        //   if (
        //     currOutletUserOrdersDone[j].crmUserId &&
        //     currOutletUserOrdersDone[j].crmUserId === crmUsers[i].uniqueId
        //   ) {
        //     totalCustomersSpendingTemp += finalPriceRoundedUp;

        //     if (!isCountedUser) {
        //       isCountedUser = true;
        //       overviewCustomersPast30DaysTempCount += 1;
        //     }
        //     averageSpendingPer30DaysEmailDictTemp[crmUsers[i].email] +=
        //       finalPriceRoundedUp;

        //     continue;
        //   } else if (
        //     currOutletUserOrdersDone[j].userId === crmUsers[i].userId
        //   ) {
        //     totalCustomersSpendingTemp += finalPriceRoundedUp;

        //     if (!isCountedUser) {
        //       isCountedUser = true;
        //       overviewCustomersPast30DaysTempCount += 1;
        //     }
        //     averageSpendingPer30DaysEmailDictTemp[crmUsers[i].email] +=
        //       finalPriceRoundedUp;

        //     continue;
        //   } else if (
        //     currOutletUserOrdersDone[j].userPhone === crmUsers[i].number
        //   ) {
        //     // check phone
        //     totalCustomersSpendingTemp += finalPriceRoundedUp;

        //     if (!isCountedUser) {
        //       isCountedUser = true;
        //       overviewCustomersPast30DaysTempCount += 1;
        //     }
        //     averageSpendingPer30DaysEmailDictTemp[crmUsers[i].email] +=
        //       finalPriceRoundedUp;

        //     continue;
        //   }
        // }
      }
    }

    overviewCustomersPast30DaysTemp = Object.entries(
      averageSpendingPer30DaysEmailDictTemp,
    )
      .map(([key, value]) => value)
      .filter((spent) => spent > 0).length;

    setAverageSpendingPer30DaysEmailDict(averageSpendingPer30DaysEmailDictTemp);

    setOverviewCustomersPast30Days(overviewCustomersPast30DaysTempCount);

    setTotalCustomersSpending(totalCustomersSpendingTemp);
  }, [
    crmUsers,
    allOutletUserOrderDone,
    currOutletId,
  ]);

  useEffect(() => {
    {
      /* // console.log('outletCategoriesDict');
      // console.log(outletCategoriesDict);
  
      // console.log('report outlet items'); */

      var customerListDict = {};

      //const customerListTemp = Object.entries(productSalesDict).map(([key, value]) => ({ ...value }));

      //setCustomerList(customerListTemp);
      setHeaderSorting(crmUsers);
    }
  }, [crmUsers]);

  useEffect(() => {
    setCurrentPage(1);
    setPageCount(Math.ceil(crmUsers.length / perPage));
  }, [crmUsers.length]);

  useEffect(() => {
    var phTotalPaymentTemp = 0;

    for (var i = 0; i < selectedCustomerOrders.length; i++) {
      if (selectedCustomerOrders[i].completedDate) {
        phTotalPaymentTemp +=
          Math.round(selectedCustomerOrders[i].finalPrice * 20) / 20;
      }
    }

    setPhTotalPayment(phTotalPaymentTemp);
  }, [selectedCustomerOrders]);

  ////////////////////////////////////

  const convertDataToExcelFormat = () => {
    var excelData = [];

    for (var i = 0; i < crmUsers.length; i++) {
      var excelRow = {
        Name: crmUsers[i].name ? crmUsers[i].name : 'N/A',
        DOB: crmUsers[i].dob
          ? moment(crmUsers[i].dob).format('DD/MM/YYYY')
          : 'N/A',
        Gender: crmUsers[i].gender ? crmUsers[i].gender : 'N/A',
        Email: crmUsers[i].email ? crmUsers[i].email : 'N/A',
        Phone: crmUsers[i].number ? crmUsers[i].number : 'N/A',
        'User ID': crmUsers[i].uniqueName ? crmUsers[i].uniqueName : 'N/A',
      };

      excelData.push(excelRow);
    }

    return excelData;
  };

  const convertDataToCSVFormat = () => {
    var csvData = [];

    csvData.push(`Name,DOB,Gender,Email,Phone,User ID`);

    for (var i = 0; i < crmUsers.length; i++) {

      var csvRow = `${crmUsers[i].name ? crmUsers[i].name : 'N/A'},${crmUsers[i].dob ? moment(crmUsers[i].dob).format('DD/MM/YYYY') : 'N/A'},${crmUsers[i].gender ? crmUsers[i].gender : 'N/A'},${crmUsers[i].email ? crmUsers[i].email : 'N/A'},${crmUsers[i].number ? crmUsers[i].number : 'N/A'},${crmUsers[i].uniqueName ? crmUsers[i].uniqueName : 'N/A'}`;

      csvData.push(csvRow);
    }
    return csvData.join('\r\n');
  };

  const downloadPdf = () => { };

  const downloadExcel = () => {
    const excelData = convertDataToExcelFormat();

    var excelFile = `${Platform.OS === 'ios'
      ? RNFS.DocumentDirectoryPath
      : RNFS.DownloadDirectoryPath
      }/koodoo-report-Product-Sales${moment().format(
        'YYYY-MM-DD-HH-mm-ss',
      )}.xlsx`;
    var excelWorkSheet = XLSX.utils.json_to_sheet(excelData);
    var excelWorkBook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(
      excelWorkBook,
      excelWorkSheet,
      'Product Sales Report',
    );

    const workBookData = XLSX.write(excelWorkBook, {
      type: 'binary',
      bookType: 'xlsx',
    });

    RNFS.writeFile(excelFile, workBookData, 'ascii')
      .then((success) => {
        // console.log(`wrote file ${excelFile}`);

        Alert.alert(
          'Success',
          `Sent to ${excelFile}`,
          [{ text: 'OK', onPress: () => { } }],
          { cancelable: false },
        );
      })
      .catch((err) => {
        // console.log(err.message);
      });
  };

  //////////////////////////////////// Page standardize pattern here

  const setState = () => { };

  const [sort, setSort] = useState('');

  //Start Here Sorting

  const sortCRMUsers = (dataList, userSortFieldType) => {
    var dataListTemp = [...dataList];

    const userSortFieldTypeValue =
      USER_SORT_FIELD_TYPE_VALUE[userSortFieldType];

    const userSortFieldTypeCompare =
      REPORT_SORT_FIELD_TYPE_COMPARE[userSortFieldType];

    if (userSortFieldType === USER_SORT_FIELD_TYPE.PAST_SPENT_ASC ||
      userSortFieldType === USER_SORT_FIELD_TYPE.PAST_SPENT_DESC
    ) {
      // PAST SPENT
      if (userSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.ASC) {
        dataListTemp.sort(
          (a, b) =>
            (averageSpendingPer30DaysEmailDict[a.email] ? averageSpendingPer30DaysEmailDict[a.email] : '') -
            (averageSpendingPer30DaysEmailDict[b.email] ? averageSpendingPer30DaysEmailDict[b.email] : ''),
        );

        // if (userSortFieldType === USER_SORT_FIELD_TYPE.PAST_SPENT_ASC) {
        //   dataListTemp.sort((a, b) =>
        //     (averageSpendingPer30DaysEmailDict[a.email]
        //       ? averageSpendingPer30DaysEmailDict[a.email]
        //       : ''
        //     ).localeCompare(
        //       averageSpendingPer30DaysEmailDict[b.email] ? averageSpendingPer30DaysEmailDict[b.email] : '',
        //     ),
        //   );
        // } else {
        //   dataListTemp.sort(
        //     (a, b) =>
        //       (averageSpendingPer30DaysEmailDict[a.email] ? averageSpendingPer30DaysEmailDict[a.email] : '') -
        //       (averageSpendingPer30DaysEmailDict[b.email] ? averageSpendingPer30DaysEmailDict[b.email] : ''),
        //   );
        // }
      } else if (userSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.DESC) {
        dataListTemp.sort(
          (a, b) =>
            (averageSpendingPer30DaysEmailDict[b.email] ? averageSpendingPer30DaysEmailDict[b.email] : '') -
            (averageSpendingPer30DaysEmailDict[a.email] ? averageSpendingPer30DaysEmailDict[a.email] : ''),
        );

        // if (userSortFieldType === USER_SORT_FIELD_TYPE.PAST_SPENT_DESC) {
        //   dataListTemp.sort((a, b) =>
        //     (averageSpendingPer30DaysEmailDict[b.email]
        //       ? averageSpendingPer30DaysEmailDict[b.email]
        //       : ''
        //     ).localeCompare(
        //       averageSpendingPer30DaysEmailDict[a.email] ? averageSpendingPer30DaysEmailDict[a.email] : '',
        //     ),
        //   );
        // } else {
        //   dataListTemp.sort(
        //     (a, b) =>
        //       (averageSpendingPer30DaysEmailDict[b.email] ? averageSpendingPer30DaysEmailDict[b.email] : '') -
        //       (averageSpendingPer30DaysEmailDict[a.email] ? averageSpendingPer30DaysEmailDict[a.email] : ''),
        //   );
        // }
      }
    }
    else {
      //NAME
      if (userSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.ASC) {
        if (userSortFieldType === USER_SORT_FIELD_TYPE.NAME_ASC) {
          dataListTemp.sort((a, b) =>
            (a[userSortFieldTypeValue]
              ? a[userSortFieldTypeValue]
              : ''
            ).localeCompare(
              b[userSortFieldTypeValue] ? b[userSortFieldTypeValue] : '', 'en'
            ),
          );
        } else {
          dataListTemp.sort(
            (a, b) =>
              (a[userSortFieldTypeValue] ? a[userSortFieldTypeValue] : '') -
              (b[userSortFieldTypeValue] ? b[userSortFieldTypeValue] : ''),
          );
        }
      } else if (userSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.DESC) {
        if (userSortFieldType === USER_SORT_FIELD_TYPE.NAME_DESC) {
          dataListTemp.sort((a, b) =>
            (b[userSortFieldTypeValue]
              ? b[userSortFieldTypeValue]
              : ''
            ).localeCompare(
              a[userSortFieldTypeValue] ? a[userSortFieldTypeValue] : '', 'en'
            ),
          );
        } else {
          dataListTemp.sort(
            (a, b) =>
              (b[userSortFieldTypeValue] ? b[userSortFieldTypeValue] : '') -
              (a[userSortFieldTypeValue] ? a[userSortFieldTypeValue] : ''),
          );
        }
      }

      //NUMBER
      if (userSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.ASC) {
        if (userSortFieldType === USER_SORT_FIELD_TYPE.NUMBER_ASC) {
          dataListTemp.sort((a, b) =>
            (a[userSortFieldTypeValue]
              ? a[userSortFieldTypeValue]
              : ''
            ).localeCompare(
              b[userSortFieldTypeValue] ? b[userSortFieldTypeValue] : '',
            ),
          );
        } else {
          dataListTemp.sort(
            (a, b) =>
              (a[userSortFieldTypeValue] ? a[userSortFieldTypeValue] : '') -
              (b[userSortFieldTypeValue] ? b[userSortFieldTypeValue] : ''),
          );
        }
      } else if (userSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.DESC) {
        if (userSortFieldType === USER_SORT_FIELD_TYPE.NUMBER_DESC) {
          dataListTemp.sort((a, b) =>
            (b[userSortFieldTypeValue]
              ? b[userSortFieldTypeValue]
              : ''
            ).localeCompare(
              a[userSortFieldTypeValue] ? a[userSortFieldTypeValue] : '',
            ),
          );
        } else {
          dataListTemp.sort(
            (a, b) =>
              (b[userSortFieldTypeValue] ? b[userSortFieldTypeValue] : '') -
              (a[userSortFieldTypeValue] ? a[userSortFieldTypeValue] : ''),
          );
        }
      }

      //GENDER
      if (userSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.ASC) {
        if (userSortFieldType === USER_SORT_FIELD_TYPE.GENDER_ASC) {
          dataListTemp.sort((a, b) =>
            (a[userSortFieldTypeValue]
              ? a[userSortFieldTypeValue]
              : ''
            ).localeCompare(
              b[userSortFieldTypeValue] ? b[userSortFieldTypeValue] : '',
            ),
          );
        } else {
          dataListTemp.sort(
            (a, b) =>
              (a[userSortFieldTypeValue] ? a[userSortFieldTypeValue] : '') -
              (b[userSortFieldTypeValue] ? b[userSortFieldTypeValue] : ''),
          );
        }
      } else if (userSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.DESC) {
        if (userSortFieldType === USER_SORT_FIELD_TYPE.GENDER_DESC) {
          dataListTemp.sort((a, b) =>
            (b[userSortFieldTypeValue]
              ? b[userSortFieldTypeValue]
              : ''
            ).localeCompare(
              a[userSortFieldTypeValue] ? a[userSortFieldTypeValue] : '',
            ),
          );
        } else {
          dataListTemp.sort(
            (a, b) =>
              (b[userSortFieldTypeValue] ? b[userSortFieldTypeValue] : '') -
              (a[userSortFieldTypeValue] ? a[userSortFieldTypeValue] : ''),
          );
        }
      }

      //DOB
      if (userSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.ASC) {
        if (userSortFieldType === USER_SORT_FIELD_TYPE.DOB_ASC) {
          dataListTemp.sort(
            (a, b) =>
              (moment(a[userSortFieldTypeValue]).valueOf()
                ? moment(a[userSortFieldTypeValue]).valueOf()
                : '') -
              (moment(b[userSortFieldTypeValue]).valueOf()
                ? moment(b[userSortFieldTypeValue]).valueOf()
                : ''),
          );
        } else {
          dataListTemp.sort(
            (a, b) =>
              (a[userSortFieldTypeValue] ? a[userSortFieldTypeValue] : '') -
              (b[userSortFieldTypeValue] ? b[userSortFieldTypeValue] : ''),
          );
        }
      } else if (userSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.DESC) {
        if (userSortFieldType === USER_SORT_FIELD_TYPE.DOB_DESC) {
          dataListTemp.sort(
            (a, b) =>
              (moment(b[userSortFieldTypeValue]).valueOf()
                ? moment(b[userSortFieldTypeValue]).valueOf()
                : '') -
              (moment(a[userSortFieldTypeValue]).valueOf()
                ? moment(a[userSortFieldTypeValue]).valueOf()
                : ''),
          );
        } else {
          dataListTemp.sort(
            (a, b) =>
              (b[userSortFieldTypeValue] ? b[userSortFieldTypeValue] : '') -
              (a[userSortFieldTypeValue] ? a[userSortFieldTypeValue] : ''),
          );
        }
      }

      //RACE
      if (userSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.ASC) {
        if (userSortFieldType === USER_SORT_FIELD_TYPE.RACE_ASC) {
          dataListTemp.sort((a, b) =>
            (a[userSortFieldTypeValue]
              ? a[userSortFieldTypeValue]
              : ''
            ).localeCompare(
              b[userSortFieldTypeValue] ? b[userSortFieldTypeValue] : '',
            ),
          );
        } else {
          dataListTemp.sort(
            (a, b) =>
              (a[userSortFieldTypeValue] ? a[userSortFieldTypeValue] : '') -
              (b[userSortFieldTypeValue] ? b[userSortFieldTypeValue] : ''),
          );
        }
      } else if (userSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.DESC) {
        if (userSortFieldType === USER_SORT_FIELD_TYPE.RACE_DESC) {
          // dataListTemp.sort((a, b) => (b[userSortFieldTypeValue] ? b[userSortFieldTypeValue] : '') - (a[userSortFieldTypeValue] ? a[userSortFieldTypeValue] : ''));
          dataListTemp.sort((a, b) =>
            (b[userSortFieldTypeValue]
              ? b[userSortFieldTypeValue]
              : ''
            ).localeCompare(
              a[userSortFieldTypeValue] ? a[userSortFieldTypeValue] : '',
            ),
          );
        } else {
          dataListTemp.sort(
            (a, b) =>
              (b[userSortFieldTypeValue] ? b[userSortFieldTypeValue] : '') -
              (a[userSortFieldTypeValue] ? a[userSortFieldTypeValue] : ''),
          );
        }
      }

      //TIER
      if (userSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.ASC) {
        if (userSortFieldType === USER_SORT_FIELD_TYPE.TIER_ASC) {
          dataListTemp.sort((a, b) =>
            (a[userSortFieldTypeValue]
              ? a[userSortFieldTypeValue]
              : ''
            ).localeCompare(
              b[userSortFieldTypeValue] ? b[userSortFieldTypeValue] : '',
            ),
          );
        } else {
          dataListTemp.sort(
            (a, b) =>
              (a[userSortFieldTypeValue] ? a[userSortFieldTypeValue] : '') -
              (b[userSortFieldTypeValue] ? b[userSortFieldTypeValue] : ''),
          );
        }
      } else if (userSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.DESC) {
        if (userSortFieldType === USER_SORT_FIELD_TYPE.TIER_DESC) {
          // dataListTemp.sort((a, b) => (b[userSortFieldTypeValue] ? b[userSortFieldTypeValue] : '') - (a[userSortFieldTypeValue] ? a[userSortFieldTypeValue] : ''));
          dataListTemp.sort((a, b) =>
            (b[userSortFieldTypeValue]
              ? b[userSortFieldTypeValue]
              : ''
            ).localeCompare(
              a[userSortFieldTypeValue] ? a[userSortFieldTypeValue] : '',
            ),
          );
        } else {
          dataListTemp.sort(
            (a, b) =>
              (b[userSortFieldTypeValue] ? b[userSortFieldTypeValue] : '') -
              (a[userSortFieldTypeValue] ? a[userSortFieldTypeValue] : ''),
          );
        }
      }

      //STATUS
      if (userSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.ASC) {
        if (userSortFieldType === USER_SORT_FIELD_TYPE.STATUS_ASC) {
          dataListTemp.sort((a, b) =>
            (a[userSortFieldTypeValue]
              ? a[userSortFieldTypeValue]
              : ''
            ).localeCompare(
              b[userSortFieldTypeValue] ? b[userSortFieldTypeValue] : '',
            ),
          );
        } else {
          dataListTemp.sort(
            (a, b) =>
              (a[userSortFieldTypeValue] ? a[userSortFieldTypeValue] : '') -
              (b[userSortFieldTypeValue] ? b[userSortFieldTypeValue] : ''),
          );
        }
      } else if (userSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.DESC) {
        if (userSortFieldType === USER_SORT_FIELD_TYPE.STATUS_DESC) {
          dataListTemp.sort((a, b) =>
            (b[userSortFieldTypeValue]
              ? b[userSortFieldTypeValue]
              : ''
            ).localeCompare(
              a[userSortFieldTypeValue] ? a[userSortFieldTypeValue] : '',
            ),
          );
        } else {
          dataListTemp.sort(
            (a, b) =>
              (b[userSortFieldTypeValue] ? b[userSortFieldTypeValue] : '') -
              (a[userSortFieldTypeValue] ? a[userSortFieldTypeValue] : ''),
          );
        }
      }
    }

    return dataListTemp;
  };

  // navigation.dangerouslyGetParent().setOptions({
  //   tabBarVisible: false,
  // });

  navigation.setOptions({
    headerLeft: () => (
      <TouchableOpacity
        onPress={() => {
          if (isAlphaUser || true) {
            navigation.navigate('MenuOrderingScreen');

            CommonStore.update((s) => {
              s.currPage = 'MenuOrderingScreen';
              s.currPageStack = [...currPageStack, 'MenuOrderingScreen'];
            });
          }
          else {
            navigation.navigate('Table');

            CommonStore.update((s) => {
              s.currPage = 'Table';
              s.currPageStack = [...currPageStack, 'Table'];
            });
          }
          if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
            CommonStore.update((s) => {
              s.expandTab = EXPAND_TAB_TYPE.OPERATION;
            });
          }

          logEventAnalytics({
            eventName: ANALYTICS.MODULE_CRM_CUSTOMERS_C_LOGO,
            eventNameParsed: ANALYTICS_PARSED.MODULE_CRM_CUSTOMERS_C_LOGO
          })
        }}
        style={styles.headerLeftStyle}>
        <Image
          style={[{
            width: 124,
            height: 26,
          }, switchMerchant ? {
            transform: [
              { scaleX: 0.7 },
              { scaleY: 0.7 }
            ],
          } : {}]}
          resizeMode="contain"
          source={require('../assets/image/logo.png')}
        />
      </TouchableOpacity>
    ),
    headerTitle: () => (
      <View
        style={[
          {
            // justifyContent: Platform.OS === 'android' ? 'center' : 'flex-start',
            // alignItems: Platform.OS === 'android' ? 'center' : 'flex-start',
            // marginRight: Platform.OS === 'ios' ? "27%" : 0,
            // bottom: switchMerchant ? '2%' : 0,
            ...global.getHeaderTitleStyle(),
          },
          // windowWidth >= 768 && switchMerchant
          //   ? { right: windowWidth * 0.1 }
          //   : {},
          // windowWidth <= 768
          //   ? { right: 20 }
          //   : {},
        ]}>
        <Text
          style={{
            fontSize: switchMerchant ? 20 : 24,
            // lineHeight: 25,
            textAlign: 'left',
            alignItems: 'flex-start',
            justifyContent: 'flex-start',
            fontFamily: 'NunitoSans-Bold',
            color: Colors.whiteColor,
            opacity: 1,
            paddingLeft: Platform.OS === 'ios' ? "1%" : 0,
          }}>
          CRM
        </Text>
      </View>
    ),
    headerRight: () => (
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}>
        {outletSelectDropdownView()}
        <View
          style={{
            backgroundColor: 'white',
            width: 0.5,
            height: windowHeight * 0.025,
            opacity: 0.8,
            marginHorizontal: 15,
            bottom: -1,
          }} />
        <TouchableOpacity
          onPress={() => {
            if (global.currUserRole === 'admin') {
              navigation.navigate('Setting');
            }

            logEventAnalytics({
              eventName: ANALYTICS.MODULE_CRM_CUSTOMERS_C_PROFILE,
              eventNameParsed: ANALYTICS_PARSED.MODULE_CRM_CUSTOMERS_C_PROFILE
            })
          }}
          style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Text
            style={[{
              fontFamily: 'NunitoSans-SemiBold',
              fontSize: switchMerchant ? 10 : 16,
              color: Colors.secondaryColor,
              marginRight: 15,
            }, switchMerchant ? { width: windowWidth / 8 } : {}]}
            numberOfLines={switchMerchant ? 1 : 1}
          >
            {userName}
          </Text>
          <View
            style={{
              marginRight: 30,
              width: windowHeight * 0.05,
              height: windowHeight * 0.05,
              borderRadius: windowHeight * 0.05 * 0.5,
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: 'white',
            }}>
            <Image
              style={{
                width: windowHeight * 0.035,
                height: windowHeight * 0.035,
                alignSelf: 'center',
              }}
              source={require('../assets/image/profile-pic.jpg')}
            />
          </View>
        </TouchableOpacity>
      </View>
    ),
  });

  const convertTemplateToExcelFormat = () => {
    var excelTemplate = [];

    var allOutletsStr = allOutlets
      .map(
        (item) =>
          `${item.name}:${Math.floor(Math.random() * (100 - 0 + 1)) + 0}`,
      )
      .join(';');

    // var taxName = currOutletTaxes[0].name;
    var taxName = 'SST';

    var excelColumn = {
      Name: 'Kylie Campbell',
      DOB: '1994-04-13',
      Email: '<EMAIL>',
      Gender: 'Female',
      Phone: '6018-2988415',
      'User ID': 'kylie.campbell',
      Address: '41 Jln Usj 4/9G Taman Seafield Jaya 47600 Petaling Jaya',
      'Outlets Points': allOutletsStr,
      'Image Label (png, jpg, jpeg)': 'Kylie_Campbell',
      'Tier Level': '',
      'Total Spent': '0',
      'Total Visits': '0',
    };
    excelTemplate.push(excelColumn);

    var excelColumn2 = {
      Name: 'Roy Cruz',
      DOB: '1989-08-21',
      Email: '<EMAIL>',
      Gender: 'Male',
      Phone: '6012-7138233',
      'User ID': 'roy.cruz',
      Address: '35 Menara Promet Jln Sultan Ismail 50250 Kuala Lumpur',
      'Outlets Points': allOutletsStr,
      'Image Label (png, jpg, jpeg)': 'Roy_Cruz',
      'Tier Level': '',
      'Total Spent': '0',
      'Total Visits': '0',
    };
    excelTemplate.push(excelColumn2);

    // console.log('excelTemplate');
    // console.log(excelTemplate);

    return excelTemplate;
  };

  const exportTemplate = async () => {
    try {
      const excelTemplate = convertTemplateToExcelFormat();

      const tempFolderName = `koodoo-crm-user-template-${moment().format(
        'YYYY-MM-DD-HH-mm-ss',
      )}`;
      const tempFolderPath = `${Platform.OS === 'ios'
        ? RNFS.DocumentDirectoryPath
        : RNFS.DownloadDirectoryPath
        }/${tempFolderName}`;

      const tempImageFolderName = 'images/Kylie_Campbell';
      const tempImageFolderPath = `${Platform.OS === 'ios'
        ? RNFS.DocumentDirectoryPath
        : RNFS.DownloadDirectoryPath
        }/${tempFolderName}/${tempImageFolderName}`;

      const tempImageFolderName2 = 'images/Roy_Cruz';
      const tempImageFolderPath2 = `${Platform.OS === 'ios'
        ? RNFS.DocumentDirectoryPath
        : RNFS.DownloadDirectoryPath
        }/${tempFolderName}/${tempImageFolderName2}`;

      let exists = await RNFS.exists(tempFolderPath);
      if (exists) {
        // exists call delete
        await RNFS.unlink(tempFolderPath);
        // console.log("Previous Temp File Deleted", tempFolderPath);
      } else {
        // console.log("Previous Temp File Not Available", tempFolderPath)
      }

      RNFS.mkdir(tempFolderPath);
      RNFS.mkdir(tempImageFolderPath);
      RNFS.mkdir(tempImageFolderPath2);

      var templateImageUrl = '';

      // download merchant logo as example image file

      if (merchantLogo) {
        await new Promise((resolve, reject) => {
          if (
            merchantLogo.startsWith('http') ||
            merchantLogo.startsWith('file')
          ) {
            templateImageUrl = merchantLogo;

            resolve();
          } else {
            getImageFromFirebaseStorage(merchantLogo, (parsedUrl) => {
              templateImageUrl = parsedUrl;

              resolve();
            });
          }
        });

        var tempImageFileName = `image.${templateImageUrl.split('.').pop()}`;
        tempImageFileName = tempImageFileName.split('?')[0];

        const tempImageFilePath = `${Platform.OS === 'ios'
          ? RNFS.DocumentDirectoryPath
          : RNFS.DownloadDirectoryPath
          }/${tempFolderName}/${tempImageFolderName}/${tempImageFileName}`;

        const downloadJob = RNFS.downloadFile({
          fromUrl: templateImageUrl,
          toFile: tempImageFilePath,
        });

        await downloadJob.promise;

        const tempImageFilePath2 = `${Platform.OS === 'ios'
          ? RNFS.DocumentDirectoryPath
          : RNFS.DownloadDirectoryPath
          }/${tempFolderName}/${tempImageFolderName2}/${tempImageFileName}`;

        const downloadJob2 = RNFS.downloadFile({
          fromUrl: templateImageUrl,
          toFile: tempImageFilePath2,
        });

        await downloadJob2.promise;

        // var excelFile = `${Platform.OS === 'ios' ? RNFS.DocumentDirectoryPath : RNFS.DownloadDirectoryPath}/koodoo-Product${moment().format('YYYY-MM-DD-HH-mm-ss')}.xlsx`;
        var excelFile = `${Platform.OS === 'ios'
          ? RNFS.DocumentDirectoryPath
          : RNFS.DownloadDirectoryPath
          }/${tempFolderName}/koodoo-crm-user-template.xlsx`;
        var excelWorkSheet = XLSX.utils.json_to_sheet(excelTemplate);
        var excelWorkBook = XLSX.utils.book_new();

        excelWorkSheet = autofitColumns(excelTemplate, excelWorkSheet);

        XLSX.utils.book_append_sheet(
          excelWorkBook,
          excelWorkSheet,
          'CRM User Template',
        );

        const workBookData = XLSX.write(excelWorkBook, {
          type: 'binary',
          bookType: 'xlsx',
        });

        RNFS.writeFile(excelFile, workBookData, 'ascii')
          .then(async (success) => {
            // console.log(`wrote file ${excelFile}`);

            // zip the folder

            const tempZipPath = `${Platform.OS === 'ios'
              ? RNFS.DocumentDirectoryPath
              : RNFS.DownloadDirectoryPath
              }/${tempFolderName}.zip`;

            let exists = await RNFS.exists(tempZipPath);
            if (exists) {
              // exists call delete
              await RNFS.unlink(tempZipPath);
              // console.log("Previous Zip File Deleted");
            } else {
              // console.log("Previous Zip File Not Available")
            }

            zip(tempFolderPath, tempZipPath)
              .then(async (path) => {
                // console.log(`zip completed at ${path}`);

                let exists = await RNFS.exists(tempFolderPath);
                if (exists) {
                  // exists call delete
                  await RNFS.unlink(tempFolderPath);
                  // console.log("Clean Temp folder File Deleted");
                } else {
                  // console.log("Clean Temp folder File Not Available")
                }

                Alert.alert(
                  'Success',
                  `Sent to ${tempZipPath}`,
                  [{ text: 'OK', onPress: () => { } }],
                  { cancelable: false },
                );
              })
              .catch((error) => {
                console.error(error);

                Alert.alert('Error', 'Failed to export template \nTry deleting the older file and try again');
              });
          })
          .catch((err) => {
            // console.log(err.message);

            Alert.alert('Error', 'Failed to export template \nTry deleting the older file and try again');
          });
      }
      else {
        Alert.alert('Info', 'Please set the merchant logo first before proceed.');
      }
    } catch (ex) {
      console.error(ex);

      Alert.alert('Error', 'Failed to export template \nTry deleting the older file and try again');
    }
  };

  const importTemplateData = async () => {
    CommonStore.update((s) => {
      s.isLoading = true;
    });

    try {
      var res = null;
      if (Platform.OS === 'ios') {
        res = await DocumentPicker.pick({
          type: [DocumentPicker.types.zip],
          copyTo: 'documentDirectory',
        });
      }
      else {
        res = await DocumentPicker.pickSingle({
          type: [DocumentPicker.types.zip],
          copyTo: 'documentDirectory',
        });
      }

      // console.log('res');
      // console.log(res);

      // RNFetchBlob.fs.readFile(res.uri, 'base64').then(async data => {
      //   // upload to firebase storage

      //   var referenceId = uuidv4();
      //   var referencePath = `/merchant/${merchantId}}/batchUploads/${referenceId}.zip`;

      //   await uploadFileToFirebaseStorage(data, referencePath);
      // });

      var referenceId = uuidv4();
      var referencePath = `/merchant/${merchantId}}/batchUploads/${referenceId}.zip`;

      var translatedPath = '';
      if (Platform.OS === 'ios') {
        translatedPath = await getPathForFirebaseStorageFromBlob(res[0]);
      }
      else {
        translatedPath = await getPathForFirebaseStorageFromBlob(res);
      }

      // console.log('translatedPath');
      // console.log(translatedPath);

      if (Platform.OS === 'ios') {
        if (translatedPath && translatedPath.fileCopyUri) {
          translatedPath = translatedPath.fileCopyUri;
        }
      }

      await uploadFileToFirebaseStorage(translatedPath, referencePath);

      const body = {
        zipId: referenceId,
        zipPath: referencePath,

        userId: firebaseUid,
        merchantId,
        merchantName,
        outletId: currOutletId,
      };

      ApiClient.POST(API.batchCreateCRMUsers, body)
        .then((result) => {
          if (result && result.status === 'success') {
            Alert.alert(
              'Success',
              'Submitted to upload queue, we will notify you once the process is done',
            );
          } else {
            Alert.alert('Error', 'Failed to import data');
          }

          CommonStore.update((s) => {
            s.isLoading = false;
          });
        })
        .catch((err) => {
          // console.log(err);

          Alert.alert('Error', 'Failed to import data');

          CommonStore.update((s) => {
            s.isLoading = false;
          });
        });
    } catch (err) {
      CommonStore.update((s) => {
        s.isLoading = false;
      });

      if (DocumentPicker.isCancel(err)) {
        // User cancelled the picker, exit any dialogs or menus and move on
      } else {
        console.error(err);

        Alert.alert('Error', 'Failed to import data');
      }
    }
  };

  const actionUsersExpandedDict = (item) => {
    // for now set to one open only

    if (usersExpandedDict[item.email]) {
      // setUsersExpandedDict({
      //   ...usersExpandedDict,
      //   [item.email]: false,
      // });
      setUsersExpandedDict({
        [item.email]: false,
      });
    } else {
      // setUsersExpandedDict({
      //   ...usersExpandedDict,
      //   [item.email]: true,
      // });
      setUsersExpandedDict({
        [item.email]: true,
      });
    }
  };

  const expandOrderFunc = (param) => {
    if (expandOrder == false) {
      setExpandOrder(true);
      setExpandViewDict({
        ...expandViewDict,
        //[param.uniqueId]: true,
      });
      expandViewDict;
    } else {
      setExpandOrder(false);
      setExpandViewDict({
        ...expandViewDict,
        //[param.uniqueId]: undefined,
      });
    }
  };
  // const next = (e) => {
  //   const offset = e * perPage;
  //   setState({ offset: offset })
  //   loadMoreData()
  // }

  // const less = async () => {
  //   if (page > 0) {
  //     await setState({ page: page - 1, currentPage: currentPage - 1 })
  //     // console.log(page)
  //     var y = page
  //     pre(y)
  //   }
  // }

  const nextPage = () => {
    setCurrentPage(currentPage + 1 > pageCount ? currentPage : currentPage + 1);
  };

  const prevPage = () => {
    setCurrentPage(currentPage - 1 < 1 ? currentPage : currentPage - 1);
  };

  //   const loadMoreData = () => {
  //     const data = oriList;
  //     const slice = data.slice(offset, offset + perPage)
  //     setState({ list: slice, pageCount: Math.ceil(data.length / perPage) })
  // }

  const add = async () => {
    if (page + 1 < pageCount) {
      await setState({ page: page + 1, currentPage: currentPage + 1 });
      // console.log(page);
      var e = page;
      next(e);
    }
  };

  const renderItem = ({ item, index }) => {
    let levelName = '-';

    try {
      if (item?.levelOrderIndex != null) {
        const matchedLevel = loyaltyTier?.levels?.find(
          level => Number(level.orderIndex) === Number(item.levelOrderIndex)
        );

        levelName = matchedLevel?.levelName ?? '-';
      }
    } catch (err) {
      console.warn('Error getting loyalty level:', err);
    }

    return (
      (<View
        style={{
          //backgroundColor: (index + 1) % 2 == 0 ? Colors.whiteColor : Colors.highlightColor,
          backgroundColor: '#FFFFFF',
          paddingVertical: 10,
          //paddingHorizontal: 3,
          //paddingLeft: 1,
          borderColor: '#BDBDBD',
          borderTopWidth: (index + 1) % 2 == 0 ? 0 : 0.5,
          borderBottomWidth: (index + 1) % 2 == 0 ? 0 : 0.5,
          // width: '100%',
        }}>
        <TouchableOpacity
          onPress={() => {
            CommonStore.update(
              (s) => {
                s.selectedCustomerEdit = item;

                s.timestamp = Date.now();
              },
              () => {
                navigation.navigate('NewCustomer');

                CommonStore.update((s) => {
                  s.selectedCustomerEdit = item;
                });
              },
            );

            // CommonStore.update(s => {
            //   s.selectedCustomerEdit = item;
            // });

            // navigation.navigate('NewCustomer');
          }}>
          <View
            style={{
              flexDirection: 'row',
              marginTop: 10,
              marginBottom: 10,
              alignItems: 'center',
              borderBottomWidth:
                expandViewDict == true ? StyleSheet.hairlineWidth : null,
            }}>
            {isFiltered && filteredCRMList.length > 0 ?
              <View style={{ width: '3%' }}>
                <CheckBox
                  value={filteredCRMList[index].isChecked} // checkbox state for each user
                  onValueChange={(newValue) => {
                    const updatedUsers = [...filteredCRMList];
                    updatedUsers[index] = { ...updatedUsers[index], isChecked: newValue };
                    OutletStore.update((s) => {
                      s.filteredCRMList = updatedUsers;
                    })

                    // Automatically update the "Select All" checkbox
                    const allChecked = updatedUsers.every(user => user.isChecked);
                    setIsSelectedAll(allChecked);
                  }}
                />

              </View>
              : <></>}
            <View
              style={{
                flexDirection: 'row',
                marginLeft: 0,
                width: '7%',
                alignItems: 'center',
              }}>
              {item.avatar ? (
                <AsyncImage
                  style={{
                    width: switchMerchant ? 35 : 50,
                    height: switchMerchant ? 35 : 50,
                    alignSelf: 'center',
                    borderRadius: 100,
                    marginLeft: 5,
                  }}
                  source={{
                    uri: item.avatar,
                  }}
                  item={item}
                  hideLoading
                />
              ) : (
                <Image
                  style={{
                    width: switchMerchant ? 35 : 50,
                    height: switchMerchant ? 35 : 50,
                    alignSelf: 'center',
                    borderRadius: 100,
                    marginLeft: 5,
                  }}
                  source={require('../assets/image/profile-pic.jpg')}
                  hideLoading
                />
              )}
              {/* <View style={{ flexDirection: 'column', marginLeft: 5 }}>
              <Text style={{ flex: 1, fontSize: 13, fontWeight: '500', textAlign: 'left', fontFamily: 'NunitoSans-Bold' }}>{item.name}</Text>
              <Text style={{ flex: 1, fontSize: 12, fontWeight: '500', textAlign: 'left', color: Colors.fieldtTxtColor }}>{item.uniqueName}</Text>
            </View> */}
            </View>
            <View style={{ flexDirection: 'row', marginLeft: 0, width: isFiltered ? '9%' : '12%' }}>
              <View style={{ flexDirection: 'column', marginLeft: 5 }}>
                <Text
                  style={{
                    flex: 1,
                    fontSize: switchMerchant ? 10 : 14,
                    fontWeight: '500',
                    textAlign: 'left',
                    fontFamily: 'NunitoSans-Bold',
                    marginBottom: 5,
                  }}>
                  {item.name}
                </Text>
                <Text
                  style={{
                    flex: 1,
                    fontSize: switchMerchant ? 9 : 12,
                    fontFamily: 'NunitoSans-Regular',
                    fontWeight: '500',
                    textAlign: 'left',
                    color: Colors.fieldtTxtColor,
                  }}
                  numberOfLines={1}>
                  {/* {item.uniqueName} */}
                  {item.userIdHuman ? item.userIdHuman : '-'}
                </Text>
              </View>
            </View>

            <View style={{ flexDirection: 'row', marginLeft: 0, width: '20%' }}>
              <View style={{ flexDirection: 'column' }}>
                <Text
                  style={{
                    flex: 1,
                    fontSize: switchMerchant ? 10 : 14,
                    fontFamily: 'NunitoSans-Regular',
                    fontWeight: '500',
                    textAlign: 'left',
                    marginBottom: 5,
                  }}>
                  {item.number}
                </Text>
                <Text
                  style={{
                    flex: 1,
                    fontSize: switchMerchant ? 10 : 14,
                    fontFamily: 'NunitoSans-Regular',
                    fontWeight: '500',
                    textAlign: 'left',
                  }}
                  numberOfLines={1}>
                  {item.email}
                </Text>
              </View>
            </View>

            <View style={{ flexDirection: 'row', marginLeft: 0, width: '11%' }}>
              <View style={{ flexDirection: 'column' }}>
                <Text
                  style={{
                    flex: 1,
                    fontSize: switchMerchant ? 10 : 14,
                    fontFamily: 'NunitoSans-Regular',
                    fontWeight: '500',
                    textAlign: 'center',
                    marginLeft: 15,
                  }}>
                  {item.gender == 'Male' ? (
                    <Ionicon
                      name="male"
                      color={'blue'}
                      size={switchMerchant ? 11 : 22}
                    />
                  ) : item.gender == 'Female' ? (
                    <Ionicon
                      name="female"
                      color={'red'}
                      size={switchMerchant ? 11 : 22}
                    />
                  ) : (
                    '  -'
                  )}
                </Text>
                {/* <Text style={{ flex: 1, fontSize: 13, fontWeight: '500', textAlign: 'center' }}></Text> */}
              </View>
            </View>

            <View style={{ flexDirection: 'row', marginLeft: 0, width: '12%' }}>
              <View style={{ flexDirection: 'column', marginLeft: 0 }}>
                <Text
                  style={{
                    flex: 2,
                    fontSize: switchMerchant ? 10 : 14,
                    fontFamily: 'NunitoSans-Regular',
                    fontWeight: '500',
                    textAlign: 'center',
                  }}>
                  {item.dob ? moment(item.dob).format('DD/MM/YYYY') : '-'}
                </Text>
              </View>
            </View>

            <View style={{ flexDirection: 'row', marginLeft: 0, width: '10%' }}>
              <View style={{ flexDirection: 'column', marginLeft: 0 }}>
                <Text
                  style={{
                    flex: 2,
                    fontSize: switchMerchant ? 10 : 14,
                    fontFamily: 'NunitoSans-Regular',
                    fontWeight: '500',
                    textAlign: 'center',
                  }}>
                  {item.race ? item.race : 'N/A'}
                </Text>
              </View>
            </View>
            <View style={{ flexDirection: 'row', marginLeft: 0, width: '9%' }}>
              <View style={{ flexDirection: 'column', marginLeft: 0 }}>
                <Text
                  style={{
                    flex: 2,
                    fontSize: switchMerchant ? 10 : 14,
                    fontFamily: 'NunitoSans-Regular',
                    fontWeight: '500',
                    textAlign: 'center',
                  }}>
                  {item.levelName ? item.levelName : levelName}
                </Text>
              </View>
            </View>

            <View style={{ flexDirection: 'row', marginLeft: 0, width: '13%' }}>
              <View style={{ flexDirection: 'column', marginLeft: 0 }}>
                {/* {
                  moment().diff(moment(item.updatedAt), 'day') <= 7 ?
                    <Switch
                      style={{ transform: [{ scaleX: 0.9 }, { scaleY: 0.85 }] }}
                      // onSyncPress={(value) => {
                      //   setIsActiveMember(item, value)
                      // }}
                      value={true}
                      width={42}
                      circleColorActive={Colors.primaryColor}
                      circleColorInactive={Colors.fieldtTxtColor}
                      backgroundActive={'#dddddd'}
                    />
                    :
                    <Switch
                      style={{ transform: [{ scaleX: 0.9 }, { scaleY: 0.85 }] }}
                      // onSyncPress={(value) => {
                      //   // if(moment().diff(moment(item.updatedAt), 'day') <= 7){
                      //   //   setIsActiveMember(false);
                      //   // }
                      //   // else{
                      //   //   setIsActiveMember(true);
                      //   // }
                      //   setIsActiveMember(item, value)
                      // }}
                      value={false}
                      width={42}
                      circleColorActive={Colors.primaryColor}
                      circleColorInactive={Colors.fieldtTxtColor}
                      backgroundActive={'#dddddd'}
                    />
                } */}
                <Text
                  style={{
                    flex: 1,
                    fontSize: switchMerchant ? 10 : 14,
                    fontFamily: 'NunitoSans-Regular',
                    fontWeight: '500',
                    textAlign: 'center',
                  }}>
                  {isFiltered && parseInt(purchaseAmountMax) === 0 ? '0.00' :
                    (averageSpendingPer30DaysEmailDict[item.email]
                      ? averageSpendingPer30DaysEmailDict[item.email]
                        .toFixed(2)
                        .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')
                      : '0.00')}
                  {/* {isActiveMember ? 'Active Member' : 'Inactive Member'} */}
                </Text>
                {/* <Text style={{ flex: 1 }}>
              {`(${moment().diff(customerFirstVisitDate, 'month')} month${moment().diff(customerFirstVisitDate, 'month') > 1 ? 's' : ''})`}
              </Text> */}
                {/* {
                customerFirstVisitDate
                  ?
                  <Text style={{ flex: 1 }}>
                    {`(${moment().diff(customerFirstVisitDate, 'month')} month${moment().diff(customerFirstVisitDate, 'month') > 1 ? 's' : ''})`}
                  </Text>
                  :
                  <></>
              } */}
              </View>
            </View>

            <View style={{ flexDirection: 'row', marginLeft: 0, width: '5%' }}>
              {/* <View style={{flexDirection: 'column', marginLeft: 5}}> */}
              <TouchableOpacity
                onPress={() => {
                  // setExpandTimeline(!expandTimeline)

                  CommonStore.update((s) => {
                    s.selectedCustomerEdit = item;
                  });

                  actionUsersExpandedDict(item);

                  logEventAnalytics({
                    eventName: ANALYTICS.MODULE_CRM_CUSTOMERS_LIST_C_CUSTOMER_DETAILS,
                    eventNameParsed: ANALYTICS_PARSED.MODULE_CRM_CUSTOMERS_LIST_C_CUSTOMER_DETAILS
                  })

                }}>
                <Icon
                  name={
                    usersExpandedDict[item.email]
                      ? 'chevron-up'
                      : 'chevron-down'
                  }
                  size={switchMerchant ? 20 : 30}
                  color={Colors.tabGrey}
                />
              </TouchableOpacity>
              {/* </View> */}
            </View>
          </View>
        </TouchableOpacity>
        {usersExpandedDict[item.email] && (
          <>
            {isLoading ? (
              <View
                style={{
                  width: '100%',
                  height: 100,
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <ActivityIndicator
                  style={{}}
                  color={Colors.primaryColor}
                  size={'large'}
                />
              </View>
            ) : (
              <>
                {selectedCustomerDineInOrders.length > 0 ? (
                  // <View style={{ borderTopWidth: 1, borderColor: '#E5E5E5', }}>
                  (<ScrollView
                  // showsVerticalScrollIndicator={false}
                  // style={{ height: 350, borderWidth: 1, padding: 10, margin: 10, marginHorizontal: 10, borderRadius: 5, borderColor: '#E5E5E5' }}
                  >
                    <FlatList
                      showsVerticalScrollIndicator={false}
                      data={selectedCustomerDineInOrders}
                      renderItem={renderTimelineItem}
                      keyExtractor={(item, index) => String(index)}
                      style={{ marginTop: 10, paddingTop: 10 }}
                    //initialNumToRender={4}
                    />
                  </ScrollView>)
                ) : (
                  // </View>
                  (<View
                    style={{
                      width: '100%',
                      height: 100,
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}>
                    <Text
                      style={{
                        top: -20,
                        fontFamily: 'NunitoSans-Regular',
                        fontSize: switchMerchant ? 10 : 16,
                      }}>
                      No timeline for now.
                    </Text>
                  </View>)
                )}
              </>
            )}
          </>
        )}
      </View>)
    );
  };

  const renderTimelineItem = ({ item, index }) => {
    var detailsStr = 'N/A';
    var detailsList = [];

    // console.log(item);
    // console.log(item.finalPrice);

    const currOrder = selectedCustomerOrders.find(
      (order) => order.uniqueId === item.orderId,
    );

    if (item.transactionType === USER_POINTS_TRANSACTION_TYPE.EARN) {
      if (currOrder && currOrder.cartItems) {
        for (var i = 0; i < currOrder.cartItems.length; i++) {
          detailsList.push(currOrder.cartItems[i].name);
        }
      }
    } else if (item.transactionType === USER_POINTS_TRANSACTION_TYPE.REDEEM) {
      if (currOrder && currOrder.pointsToRedeemPackageIdList) {
        for (var i = 0; i < currOrder.pointsToRedeemPackageIdList.length; i++) {
          const currPackage =
            pointsRedeemPackagesDict[currOrder.pointsToRedeemPackageIdList[i]];

          if (currPackage) {
            detailsList.push(currPackage.packageName);
          }
        }
      }
    }

    if (detailsList.length > 0) {
      detailsStr = detailsList.join(', ');
    }

    return (
      <ScrollView showsVerticalScrollIndicator={false}>
        <View
          style={{
            width: '100%',
            alignItems: 'center',
            justifyContent: 'center',
          }}>
          <View
            style={{
              flexDirection: 'row',
              width: '66%',
              marginTop: 0,
              justifyContent: 'center',
              alignItems: 'center',
            }}>
            <View style={{ flexDirection: 'column' }}>
              {/* <Ionicon name='ellipse' size={26} color='#4E9F7D' style={{ alignSelf: 'center', marginTop: -5 }} /> */}
              <View
                style={{
                  alignSelf: 'center',
                  borderWidth: 0,
                  borderColor: '#E5F1EC',
                  height: switchMerchant ? 140 : 160,
                  width: 1,
                  marginTop: -10,
                  zIndex: -1,
                }}
              />
            </View>
            <View
              style={{
                borderWidth: 1,
                borderRadius: 6,
                borderColor: '#E5E5E5',
                height: switchMerchant ? 120 : 160,
                marginLeft: 10,
                padding: 10,
                width: switchMerchant ? 400 : 500,
              }}>
              <View style={{ marginLeft: 10, marginVertical: 5 }}>
                {/* <Text style={{ fontSize: 16, fontFamily: 'Nunitosans-Bold' }}> */}
                <View
                  style={{
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                  }}>
                  <Text
                    style={{
                      fontWeight: '600',
                      fontSize: switchMerchant ? 10 : 16,
                      color: Colors.primaryColor,
                    }}>
                    {moment(item.orderDate).format('DD MMM YYYY hh:mm A')}
                  </Text>
                  <Text
                    style={{
                      fontWeight: '600',
                      fontSize: switchMerchant ? 10 : 16,
                      color: Colors.primaryColor,
                    }}>
                    Table Code:{' '}
                    {item.tableCode
                      ? item.tableCode
                      : outletTablesDict[item.tableId]
                        ? outletTablesDict[item.tableId].code
                        : 'N/A'}
                  </Text>
                </View>
              </View>

              <View style={{ flexDirection: 'column', marginLeft: 10 }}>
                <View
                  style={{ flexDirection: 'row', width: '100%', marginTop: 5 }}>
                  <Text
                    style={
                      switchMerchant
                        ? { fontSize: 10, fontWeight: '600', width: '20%' }
                        : { fontWeight: '600', width: '20%' }
                    }>
                    Order ID:{' '}
                  </Text>
                  <TouchableOpacity
                    style={{
                      width: '80%',
                    }}
                    onPress={() => {
                      navigation.navigate('History');

                    }}>
                    <Text
                      style={{
                        fontSize: switchMerchant ? 10 : 16,
                        fontFamily: 'NunitoSans-Regular',
                        fontWeight: '700',
                      }}>
                      #{item.orderId}
                    </Text>
                  </TouchableOpacity>
                </View>

                <View
                  style={{ flexDirection: 'row', width: '100%', marginTop: 5 }}>
                  <Text
                    style={{
                      fontSize: switchMerchant ? 10 : 16,
                      fontFamily: 'NunitoSans-Regular',
                      fontWeight: '500',
                      width: '20%',
                    }}>
                    {/* Remarks:: */}
                    Item Ordered:
                  </Text>
                  {item.cartItems && item.cartItems.length > 0 ? (
                    <View style={{ flexDirection: 'row', width: '100%' }}>
                      <Text
                        style={{
                          fontSize: switchMerchant ? 10 : 16,
                          fontFamily: 'NunitoSans-Regular',
                          fontWeight: '700',
                          width: '80%',
                        }}
                        numberOfLines={3}>
                        {item.cartItems
                          .map((cartItem) => cartItem.itemName)
                          .join(', ')}
                      </Text>
                    </View>
                  ) : (
                    <></>
                  )}
                </View>

                {item.cartItemsCancelled &&
                  item.cartItemsCancelled.length > 0 ? (
                  <View
                    style={{ flexDirection: 'row', width: '100%', marginTop: 5 }}>
                    <Text
                      style={{
                        fontSize: switchMerchant ? 10 : 16,
                        fontFamily: 'NunitoSans-Regular',
                        fontWeight: '500',
                        width: '20%',
                      }}>
                      {/* Remarks: */}
                      Item Cancelled:
                    </Text>
                    <View style={{ flexDirection: 'row', width: '100%' }}>
                      <Text
                        style={{
                          fontSize: switchMerchant ? 10 : 16,
                          fontFamily: 'NunitoSans-Regular',
                          fontWeight: '700',
                          width: '80%',
                        }}>
                        {item.cartItemsCancelled
                          .map((cartItem) => cartItem.itemName)
                          .join(', ')}
                      </Text>
                    </View>
                  </View>
                ) : (
                  <></>
                )}

                <View
                  style={{ flexDirection: 'row', width: '100%', marginTop: 5 }}>
                  <Text
                    style={{
                      fontSize: switchMerchant ? 10 : 16,
                      fontFamily: 'NunitoSans-Regular',
                      fontWeight: '500',
                      width: '20%',
                    }}>
                    Total Price:
                  </Text>
                  <Text
                    style={{
                      fontSize: switchMerchant ? 10 : 16,
                      fontFamily: 'NunitoSans-Regular',
                      fontWeight: '700',
                      width: '80%',
                    }}>
                    {/* {item.remarks ? item.remarks : 'N/A'} */}
                    RM{' '}
                    {item.finalPrice
                      .toFixed(2)
                      .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
                  </Text>
                </View>
              </View>
            </View>
          </View>
        </View>
      </ScrollView>
    );
  };

  //search function
  const searchUsers = async (searchTerm) => {
    try {
      if (awsBucket === 'koodooprod') {
        // prod env

        const body = {
          query: searchTerm,
          outletId: currOutletId,
        };

        ApiClient.POST(API.msGetCrmUser, body)
          .then((result) => {
            if (result && result.status === 'success' &&
              result.data && result.data.length > 0
            ) {
              // Alert.alert(
              //   'Success',
              //   'Submitted to upload queue, we will notify you once the process is done',
              // );

              setSearchList(result.data);
            } else {
              Alert.alert('Info', 'No customers found')
              setSearchList([]);
            }

            CommonStore.update((s) => {
              s.isLoading = false;
            });
          })
          .catch((err) => {
            // console.log(err);

            Alert.alert('Info', 'No customers found')
            setSearchList([]);

            CommonStore.update((s) => {
              s.isLoading = false;
            });
          });
      }
      else {
        const userSnapshot = await firebase.firestore()
          .collection(Collections.CRMUser)
          .where('ngram', 'array-contains', searchTerm)
          .where('outletId', '==', currOutletId)
          .where('deletedAt', '==', null)
          .get();

        if (userSnapshot.size > 0) {
          // Process the results
          const users = userSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
          console.log('search users result:', users);

          setSearchList(users);

          CommonStore.update((s) => {
            s.isLoading = false;
          })
        } else {
          Alert.alert('Info', 'No customers found')
          console.log('No customers found');

          setSearchList([]);

          CommonStore.update((s) => {
            s.isLoading = false;
          })
        }
      }
    } catch (error) {
      console.error('Error fetching users:', error);

      setSearchList([]);

      CommonStore.update((s) => {
        s.isLoading = false;
      })
    }
  };


  /////Manage Filter

  ///////////////////////////////////////////////////////

  return (
    (<UserIdleWrapper disabled={!isMounted}>
      <View
        style={[
          styles.container,
          !isTablet()
            ? {
              transform: [{ scaleX: 1 }, { scaleY: 1 }],
            }
            : {},
          {
            ...getTransformForScreenInsideNavigation(),
          }
        ]}>
        {/* <View
          style={[
            styles.sidebar,
            !isTablet()
              ? {
                width: windowWidth * 0.08,
              }
              : {},
            switchMerchant
              ? {
                // width: '10%'
              }
              : {},
            {
              width: windowWidth * 0.08,
            }
          ]}>
          <SideBar
            navigation={props.navigation}
            selectedTab={6}
            expandCRM
          />
        </View> */}
        <ScrollView
          showsVerticalScrollIndicator={false}
          style={{}}
          contentContainerStyle={{
            paddingBottom: windowHeight * 0.1,
          }}>
          <ScrollView horizontal scrollEnabled={switchMerchant}>
            <ModalView
              supportedOrientations={['landscape', 'portrait']}
              style={{ flex: 1 }}
              visible={visible}
              transparent
              animationType="slide">
              <KeyboardAvoidingView
                behavior={Platform.OS === 'ios' ? 'padding' : undefined}
                style={{
                  backgroundColor: 'rgba(0,0,0,0.5)',
                  flex: 1,
                  justifyContent: 'center',
                  alignItems: 'center',
                  minHeight: windowHeight,
                }}
                keyboardVerticalOffset={Platform.OS === 'ios' ? 64 : 0}
              >
                <View
                  style={[
                    styles.ManageFilterBox,
                    {
                      borderRadius: 12, padding: 30, paddingHorizontal: 50,
                      ...getTransformForModalInsideNavigation(),
                    },
                  ]}>
                  <View
                    style={{
                      justifyContent: 'flex-end',
                      alignItems: 'flex-end',
                      marginTop: -15,
                      marginRight: -35,
                    }}>
                    <TouchableOpacity
                      onPress={() => {
                        setVisible(false);
                      }}>
                      <AntDesign
                        name={'closecircle'}
                        size={25}
                        color={'#858C94'}
                      />
                    </TouchableOpacity>
                  </View>
                  <View
                    style={{
                      justifyContent: 'center',
                      alignItems: 'center',
                      //marginTop: 10
                    }}>
                    <Text style={{ fontSize: 26, fontFamily: 'NunitoSans-Bold' }}>
                      Manage Filter
                    </Text>
                  </View>
                  <View
                    style={{
                      borderColor: '#E5E5E5',
                      borderWidth: 1,
                      marginHorizontal: -20,
                      marginBottom: 15,
                    }}
                  />

                  <View
                    style={{
                      flexDirection: 'row',
                      //justifyContent: 'center',
                      alignItems: 'center',
                      //paddingLeft: 30,
                      //borderRadius: 10,
                      height: 35,
                      marginVertical: 10,
                    }}>
                    <DropDownPicker
                      // controller={instance => controller = instance}
                      controller={(instance) => setController(instance)}
                      arrowColor={'#BDBDBD'}
                      arrowSize={23}
                      arrowStyle={{ paddingVertical: 0 }}
                      style={{
                        width: 180,
                        height: 35,
                        backgroundColor: '#F2F3F7',
                        borderRadius: 6,
                        paddingVertical: 0,
                      }}
                      itemStyle={{ justifyContent: 'flex-start' }}
                      placeholderStyle={{ color: '#B6B6B6' }}
                      //change value below (Eason)
                      items={[
                        { label: 'A', value: 1 },
                        { label: 'B', value: 2 },
                      ]}
                      placeholder={'Product Category'}
                      labelStyle={{ fontSize: 12.5 }}
                      onChangeItem={(selectedSort) => {
                        // setState({ sort: selectedSort }),
                        //sortingOrders(selectedSort);
                      }}
                    />
                    <DropDownPicker
                      // controller={instance => controller = instance}
                      controller={(instance) => setController(instance)}
                      arrowColor={'#BDBDBD'}
                      arrowSize={23}
                      arrowStyle={{ paddingVertical: 0 }}
                      style={{
                        width: 90,
                        backgroundColor: '#F2F3F7',
                        borderRadius: 6,
                        marginLeft: 20,
                        paddingVertical: 0,
                      }}
                      dropDownStyle={{
                        width: 90,
                        borderRadius: 6,
                        marginLeft: 20,
                        paddingVertical: 0,
                      }}
                      itemStyle={{ justifyContent: 'flex-start' }}
                      placeholderStyle={{ color: '#B6B6B6' }}
                      //change value below (Eason)
                      items={[
                        { label: 'A', value: 1 },
                        { label: 'B', value: 2 },
                      ]}
                      placeholder={'Is'}
                      labelStyle={{ fontSize: 12.5 }}
                      onChangeItem={(selectedSort) => {
                        // setState({ sort: selectedSort }),
                        //sortingOrders(selectedSort);
                      }}
                    //onOpen={() => controller1.close()}
                    />
                  </View>
                  <View
                    style={{
                      //marginRight: '33%',
                      //flexDirection: 'row',
                      //justifyContent: 'center',
                      //alignItems: 'center',
                      //paddingLeft: 30,
                      //borderRadius: 10,
                      marginTop: 20,
                      height: 40,
                      zIndex: -1,
                    }}>
                    <TextInput
                      style={{
                        borderRadius: 5,
                        borderColor: '#E5E5E5',
                        borderWidth: 1,
                        height: 35,
                        width: 200,
                        backgroundColor: Colors.fieldtBgColor,
                        paddingLeft: 5,
                      }}
                      placeholder="Enter Condition"
                      placeholderStyle={{ paddingLeft: 5 }}
                      placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                      onChangeText={() => { }}
                    />
                  </View>
                  {/* <View style={{
                flexDirection: 'row',
                justifyContent: 'center',
                alignItems: 'center',
                borderWidth: 1,
                borderColor: '#E0E0E0',
                backgroundColor: '#F2F3F7',
                width: 100,
                height: 40,
                borderRadius: 6,
                marginTop: 15,
              }}>
                <Text style={{
                  fontSize: 12.5,
                  Color: '#B6B6B6',
                }}>
                  Package A
                </Text>
                <TouchableOpacity
                  onPress={() => {

                  }}
                  style={{
                    marginLeft: 5
                  }}
                >
                  <AntDesign name={"close"} size={16} color={'#B6B6B6'} />
                </TouchableOpacity>
              </View> */}

                  <View
                    style={{
                      borderWidth: 1,
                      borderColor: '#E5E5E5',
                      marginVertical: 20,
                      marginHorizontal: -20,
                      zIndex: -1,
                    }}
                  />

                  <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'flex-end',
                      alignItems: 'flex-end',
                      zIndex: -1,
                    }}>
                    <TouchableOpacity
                      style={{
                        borderWidth: 1,
                        width: 110,
                        height: 40,
                        borderColor: '#4E9F7D',
                        borderRadius: 8,
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                      onPress={() => {
                        setVisible(false);
                      }}>
                      <Text
                        style={{
                          color: Colors.primaryColor,
                          fontSize: 17,
                          color: '#4E9F7D',
                          fontFamily: 'NunitoSans-Bold',
                        }}>
                        Cancel
                      </Text>
                    </TouchableOpacity>

                    <TouchableOpacity
                      style={{
                        borderWidth: 1,
                        width: 110,
                        height: 40,
                        borderColor: '#4E9F7D',
                        borderRadius: 8,
                        alignItems: 'center',
                        justifyContent: 'center',
                        backgroundColor: '#4E9F7D',
                        marginLeft: 10,
                      }}
                      onPress={() => { }}>
                      <Text
                        style={{
                          color: Colors.primaryColor,
                          fontSize: 17,
                          color: '#FFFFFF',
                          fontFamily: 'NunitoSans-Bold',
                        }}>
                        Apply
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>
              </KeyboardAvoidingView>
            </ModalView>

            <ModalView
              style={
                {
                  // flex: 1
                }
              }
              visible={importModal}
              supportedOrientations={['portrait', 'landscape']}
              transparent
              animationType={'slide'}>
              <View style={styles.modalContainer}>
                <View style={[styles.modalView1, {
                  height: Dimensions.get('window').width * 0.3,
                  width: Dimensions.get('window').width * 0.4,
                  padding: Dimensions.get('window').width * 0.03,

                  ...getTransformForModalInsideNavigation(),
                }]}>
                  <TouchableOpacity
                    disabled={isLoading}
                    style={[styles.closeButton, {
                      right: windowWidth * 0.02,
                      top: windowWidth * 0.02,
                    },]}
                    onPress={() => {
                      // setState({ changeTable: false });
                      setImportModal(false);
                    }}>
                    <AntDesign
                      name="closecircle"
                      size={switchMerchant ? 15 : 25}
                      color={Colors.fieldtTxtColor}
                    />
                  </TouchableOpacity>
                  <View style={styles.modalTitle}>
                    <Text
                      style={
                        switchMerchant
                          ? styles.modalTitleText1
                          : styles.modalTitleText
                      }>
                      Download Report
                    </Text>
                  </View>
                  <View
                    style={{
                      alignItems: 'center',
                      //top: '15%',
                    }}>
                    <View style={{ top: switchMerchant ? '14%' : '10%' }}>
                      <Text
                        style={{
                          fontSize: switchMerchant ? 10 : 16,
                          fontFamily: 'NunitoSans-Bold',
                        }}>
                        Email Address:
                      </Text>
                      <TextInput
                        placeholder="Enter your email"
                        placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                        style={{
                          backgroundColor: Colors.fieldtBgColor,
                          width: switchMerchant ? 240 : 370,
                          height: switchMerchant ? 35 : 50,
                          borderRadius: 5,
                          padding: 5,
                          marginVertical: 5,
                          borderWidth: 1,
                          borderColor: '#E5E5E5',
                          paddingLeft: 10,
                          fontSize: switchMerchant ? 10 : 16,
                        }}
                        autoCapitalize='none'
                        onChangeText={(text) => {
                          setExportEmail(text);

                          logEventAnalytics({
                            eventName: ANALYTICS.MODULE_CRM_CUSTOMERS_DL_BTN_TB_EMAIL,
                            eventNameParsed: ANALYTICS_PARSED.MODULE_CRM_CUSTOMERS_DL_BTN_TB_EMAIL
                          })
                        }}
                        value={exportEmail}
                      />
                      <Text
                        style={{
                          fontSize: switchMerchant ? 10 : 16,
                          fontFamily: 'NunitoSans-Bold',
                          marginTop: 15,
                        }}>
                        Send As:
                      </Text>
                      <View
                        style={{
                          alignItems: 'center',
                          justifyContent: 'center',
                          //top: '10%',
                          flexDirection: 'row',
                          marginTop: 10,
                        }}>
                        <TouchableOpacity
                          disabled={isLoading}
                          style={{
                            justifyContent: 'center',
                            flexDirection: 'row',
                            borderWidth: 1,
                            borderColor: Colors.primaryColor,
                            backgroundColor: '#4E9F7D',
                            borderRadius: 5,
                            width: switchMerchant ? 100 : 100,
                            paddingHorizontal: 10,
                            height: switchMerchant ? 35 : 40,
                            alignItems: 'center',
                            shadowOffset: {
                              width: 0,
                              height: 2,
                            },
                            shadowOpacity: 0.22,
                            shadowRadius: 3.22,
                            elevation: 1,
                            zIndex: -1,
                          }}
                          onPress={() => {
                            if (exportEmail.length > 0) {
                              CommonStore.update((s) => {
                                s.isLoading = true;
                              });

                              setIsLoadingExcel(true);

                              const excelData = convertDataToExcelFormat();

                              generateEmailReport(
                                EMAIL_REPORT_TYPE.EXCEL,
                                excelData,
                                'KooDoo CRM Report',
                                'KooDoo CRM Report.xlsx',
                                `/merchant/${merchantId}/reports/${uuidv4()}.xlsx`,
                                exportEmail,
                                'KooDoo CRM Report',
                                'KooDoo CRM Report',
                                () => {
                                  CommonStore.update((s) => {
                                    s.isLoading = false;
                                  });

                                  setIsLoadingExcel(false);

                                  Alert.alert(
                                    'Success',
                                    'Report will be sent to the email address shortly',
                                  );

                                  setImportModal(false);
                                },
                              );
                            } else {
                              Alert.alert('Info', 'Invalid email address');
                            }

                            logEventAnalytics({
                              eventName: ANALYTICS.MODULE_CRM_CUSTOMERS_DL_BTN_C_REP_EXCEL,
                              eventNameParsed: ANALYTICS_PARSED.MODULE_CRM_CUSTOMERS_DL_BTN_C_REP_EXCEL
                            })
                          }}>
                          {isLoading && isLoadingExcel ? (
                            <ActivityIndicator
                              size={'small'}
                              color={Colors.whiteColor}
                            />
                          ) : (
                            <Text
                              style={{
                                color: Colors.whiteColor,
                                //marginLeft: 5,
                                fontSize: switchMerchant ? 10 : 16,
                                fontFamily: 'NunitoSans-Bold',
                              }}>
                              EXCEL
                            </Text>
                          )}
                        </TouchableOpacity>

                        <TouchableOpacity
                          disabled={isLoading}
                          style={{
                            justifyContent: 'center',
                            flexDirection: 'row',
                            borderWidth: 1,
                            borderColor: Colors.primaryColor,
                            backgroundColor: '#4E9F7D',
                            borderRadius: 5,
                            width: switchMerchant ? 100 : 100,
                            paddingHorizontal: 10,
                            height: switchMerchant ? 35 : 40,
                            alignItems: 'center',
                            shadowOffset: {
                              width: 0,
                              height: 2,
                            },
                            shadowOpacity: 0.22,
                            shadowRadius: 3.22,
                            elevation: 1,
                            zIndex: -1,
                            marginLeft: 15,
                          }}
                          onPress={() => {
                            if (exportEmail.length > 0) {
                              CommonStore.update((s) => {
                                s.isLoading = true;
                              });

                              setIsLoadingCsv(true);

                              // const csvData = convertArrayToCSV(todaySalesChart.dataSource.data);
                              const csvData = convertDataToCSVFormat();

                              generateEmailReport(
                                EMAIL_REPORT_TYPE.CSV,
                                csvData,
                                'KooDoo CRM Report',
                                'KooDoo CRM Report.csv',
                                `/merchant/${merchantId}/reports/${uuidv4()}.csv`,
                                exportEmail,
                                'KooDoo CRM Report',
                                'KooDoo CRM Report',
                                () => {
                                  CommonStore.update((s) => {
                                    s.isLoading = false;
                                  });

                                  setIsLoadingCsv(false);

                                  Alert.alert(
                                    'Success',
                                    'Report will be sent to the email address shortly',
                                  );

                                  setImportModal(false);
                                },
                              );
                            } else {
                              Alert.alert('Info', 'Invalid email address');
                            }

                            logEventAnalytics({
                              eventName: ANALYTICS.MODULE_CRM_CUSTOMERS_DL_BTN_C_REP_CSV,
                              eventNameParsed: ANALYTICS_PARSED.MODULE_CRM_CUSTOMERS_DL_BTN_C_REP_CSV
                            })
                          }}>
                          {isLoading && isLoadingCsv ? (
                            <ActivityIndicator
                              size={'small'}
                              color={Colors.whiteColor}
                            />
                          ) : (
                            <Text
                              style={{
                                color: Colors.whiteColor,
                                //marginLeft: 5,
                                fontSize: switchMerchant ? 10 : 16,
                                fontFamily: 'NunitoSans-Bold',
                              }}>
                              CSV
                            </Text>
                          )}
                        </TouchableOpacity>
                      </View>
                    </View>
                  </View>
                </View>
              </View>
            </ModalView>

            <DateTimePickerModal
              isVisible={showPurchaseDateS}
              mode={'date'}
              onConfirm={(text) => {
                setPurchaseStart(moment(text));
                setShowPurchaseDateS(false);
              }}
              onCancel={() => {
                setShowPurchaseDateS(false);
              }}
              maximumDate={moment(purchaseEnd).toDate()}
              date={moment(purchaseStart).toDate()}
            />

            <DateTimePickerModal
              isVisible={showPurchaseDateE}
              mode={'date'}
              onConfirm={(text) => {
                setPurchaseEnd(moment(text));
                setShowPurchaseDateE(false);
              }}
              onCancel={() => {
                setShowPurchaseDateE(false);
              }}
              minimumDate={moment(purchaseStart).toDate()}
              date={moment(purchaseEnd).toDate()}
            />

            <DateTimePickerModal
              isVisible={showLastVisitMin}
              mode={'date'}
              onConfirm={(text) => {
                setLastVisitMin(moment(text));
                setShowLastVisitMin(false);
              }}
              onCancel={() => {
                setShowLastVisitMin(false);
              }}
              maximumDate={moment(lastVisitMax).toDate()}
              date={moment(lastVisitMin).toDate()}
            />

            <DateTimePickerModal
              isVisible={showLastVisitMax}
              mode={'date'}
              onConfirm={(text) => {
                setLastVisitMax(moment(text));
                setShowLastVisitMax(false);
              }}
              onCancel={() => {
                setShowLastVisitMax(false);
              }}
              minimumDate={moment(lastVisitMin).toDate()}
              date={moment(lastVisitMax).toDate()}
            />

            <KeyboardAvoidingView
              style={{
                // top:
                //   Platform.OS === 'ios' && keyboardHeight > 0
                //     ? -keyboardHeight * 1
                //     : 0,
              }}>
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'flex-end',
                  width: windowWidth * 0.87,
                  margin: 10,
                  padding: 10,
                  paddingRight: 0,
                  alignItems: 'center',
                  alignSelf: 'center',
                  //paddingRight: windowWidth * 0.015,
                }}>
                <View style={{ marginRight: 15, flexDirection: 'row' }}>
                  <TouchableOpacity
                    style={{
                      justifyContent: 'center',
                      flexDirection: 'row',
                      borderWidth: 1,
                      borderColor: Colors.primaryColor,
                      backgroundColor: '#4E9F7D',
                      borderRadius: 5,
                      //width: 160,
                      paddingHorizontal: 10,
                      height: switchMerchant ? 35 : 40,
                      alignItems: 'center',
                      shadowOffset: {
                        width: 0,
                        height: 2,
                      },
                      shadowOpacity: 0.22,
                      shadowRadius: 3.22,
                      elevation: 1,
                      zIndex: -1,
                    }}
                    onPress={() => {
                      setImportModal(true);
                      logEventAnalytics({
                        eventName: ANALYTICS.MODULE_CRM_CUSTOMERS_C_DOWNLOAD_BTN,
                        eventNameParsed: ANALYTICS_PARSED.MODULE_CRM_CUSTOMERS_C_DOWNLOAD_BTN
                      })
                    }}>
                    <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                      <Icon
                        name="download"
                        size={switchMerchant ? 10 : 20}
                        color={Colors.whiteColor}
                      />
                      <Text
                        style={{
                          color: Colors.whiteColor,
                          marginLeft: 5,
                          fontSize: switchMerchant ? 10 : 16,
                          fontFamily: 'NunitoSans-Bold',
                        }}>
                        DOWNLOAD
                      </Text>
                    </View>
                  </TouchableOpacity>

                  <ModalView
                    style={
                      {
                        // flex: 1
                      }
                    }
                    visible={exportModal}
                    supportedOrientations={['portrait', 'landscape']}
                    transparent
                    animationType={'slide'}>
                    <View style={styles.modalContainer}>
                      <View style={[styles.modalViewUploadOptions, {
                        height: Dimensions.get('window').width * 0.2,
                        width: Dimensions.get('window').width * 0.3,
                        padding: Dimensions.get('window').width * 0.03,

                        ...getTransformForModalInsideNavigation(),
                      }]}>
                        <TouchableOpacity
                          style={[styles.closeButton, {
                            right: windowWidth * 0.02,
                            top: windowWidth * 0.02,
                          },]}
                          onPress={() => {
                            // setState({ changeTable: false });
                            setExportModal(false);
                          }}>
                          <AntDesign
                            name="closecircle"
                            size={switchMerchant ? 15 : 25}
                            color={Colors.fieldtTxtColor}
                          />
                        </TouchableOpacity>
                        <View style={styles.modalTitle}>
                          <Text
                            style={
                              switchMerchant
                                ? {
                                  fontFamily: 'NunitoSans-Bold',
                                  textAlign: 'center',
                                  fontSize: 16,
                                }
                                : [styles.modalTitleText]
                            }>
                            Upload Options
                          </Text>
                        </View>
                        <View
                          style={{
                            alignItems: 'center',
                            top: switchMerchant ? '20%' : '10%',
                          }}>
                          {/* <TouchableOpacity
                    style={[styles.modalSaveButton, {
                      zIndex: -1.
                    }]}
                    onPress={() => { exportFile() }}>
                    <Text style={[styles.modalDescText, { color: Colors.primaryColor }]}>Export to CSV</Text>
                  </TouchableOpacity> */}

                          {/* <TouchableOpacity
                    style={[styles.modalSaveButton, {
                      zIndex: -1.
                    }]}
                    onPress={() => { exportTemplate() }}>
                    <Text style={[styles.modalDescText, { color: Colors.primaryColor }]}>Download Batch Template</Text>
                  </TouchableOpacity> */}

                          <TouchableOpacity
                            style={{
                              justifyContent: 'center',
                              flexDirection: 'row',
                              borderWidth: 1,
                              borderColor: Colors.primaryColor,
                              backgroundColor: '#4E9F7D',
                              borderRadius: 5,
                              width: switchMerchant ? 180 : 240,
                              paddingHorizontal: 10,
                              height: switchMerchant ? 35 : 45,
                              alignItems: 'center',
                              shadowOffset: {
                                width: 0,
                                height: 2,
                              },
                              shadowOpacity: 0.22,
                              shadowRadius: 3.22,
                              elevation: 1,
                              zIndex: -1,
                              marginBottom: 10,
                            }}
                            onPress={() => {
                              importTemplateData();
                              logEventAnalytics({
                                eventName: ANALYTICS.MODULE_CRM_CUSTOMERS_BATCH_UP_C_UPLOAD_TEMP,
                                eventNameParsed: ANALYTICS_PARSED.MODULE_CRM_CUSTOMERS_BATCH_UP_C_UPLOAD_TEMP
                              })
                            }}
                            disabled={isLoading}>
                            <Text
                              style={{
                                color: Colors.whiteColor,
                                //marginLeft: 5,
                                fontSize: switchMerchant ? 10 : 16,
                                fontFamily: 'NunitoSans-Bold',
                              }}>
                              {isLoading ? 'LOADING...' : 'UPLOAD TEMPLATE'}
                            </Text>

                            {isLoading ? (
                              <ActivityIndicator
                                style={{
                                  marginLeft: 5,
                                }}
                                color={Colors.whiteColor}
                                size={'small'}
                              />
                            ) : (
                              <></>
                            )}
                          </TouchableOpacity>
                          <TouchableOpacity
                            style={{
                              justifyContent: 'center',
                              flexDirection: 'row',
                              borderWidth: 1,
                              borderColor: Colors.primaryColor,
                              backgroundColor: '#4E9F7D',
                              borderRadius: 5,
                              width: switchMerchant ? 180 : 240,
                              paddingHorizontal: 10,
                              height: switchMerchant ? 35 : 45,
                              alignItems: 'center',
                              shadowOffset: {
                                width: 0,
                                height: 2,
                              },
                              shadowOpacity: 0.22,
                              shadowRadius: 3.22,
                              elevation: 1,
                              zIndex: -1,
                            }}
                            onPress={() => {
                              exportTemplate();

                              logEventAnalytics({
                                eventName: ANALYTICS.MODULE_CRM_CUSTOMERS_BATCH_UP_C_EXPORT_TEMP,
                                eventNameParsed: ANALYTICS_PARSED.MODULE_CRM_CUSTOMERS_BATCH_UP_C_EXPORT_TEMP
                              })
                            }}>
                            <Text
                              style={{
                                color: Colors.whiteColor,
                                //marginLeft: 5,
                                fontSize: switchMerchant ? 10 : 16,
                                fontFamily: 'NunitoSans-Bold',
                              }}>
                              EXPORT TEMPLATE
                            </Text>
                          </TouchableOpacity>
                        </View>
                      </View>
                    </View>
                  </ModalView>

                  <DateTimePickerModal
                    isVisible={showDateTimePicker}
                    mode={'date'}
                    onConfirm={(text) => {
                      // setRev_date(moment(text).startOf('day'));
                      CommonStore.update(s => {
                        s.historyStartDate = moment(text).startOf('day');
                      });
                      setShowDateTimePicker(false);
                    }}
                    onCancel={() => {
                      setShowDateTimePicker(false);
                    }}
                    maximumDate={moment(historyEndDate).toDate()}
                    date={moment(historyStartDate).toDate()}
                  />

                  <DateTimePickerModal
                    isVisible={showDateTimePicker1}
                    mode={'date'}
                    onConfirm={(text) => {
                      // setRev_date1(moment(text).endOf('day'));
                      CommonStore.update(s => {
                        s.historyEndDate = moment(text).endOf('day');
                      });
                      setShowDateTimePicker1(false);
                    }}
                    onCancel={() => {
                      setShowDateTimePicker1(false);
                    }}
                    minimumDate={moment(historyStartDate).toDate()}
                    date={moment(historyEndDate).toDate()}
                  />

                  <TouchableOpacity
                    style={{
                      justifyContent: 'center',
                      flexDirection: 'row',
                      borderWidth: 1,
                      borderColor: Colors.primaryColor,
                      backgroundColor: '#4E9F7D',
                      borderRadius: 5,
                      //width: 160,
                      paddingHorizontal: 10,
                      height: switchMerchant ? 35 : 40,
                      alignItems: 'center',
                      shadowOffset: {
                        width: 0,
                        height: 2,
                      },
                      shadowOpacity: 0.22,
                      shadowRadius: 3.22,
                      elevation: 1,
                      zIndex: -1,
                      marginLeft: 15,
                    }}
                    onPress={() => {
                      setExportModal(true);

                      logEventAnalytics({
                        eventName: ANALYTICS.MODULE_CRM_CUSTOMERS_C_BATCH_UPLOAD,
                        eventNameParsed: ANALYTICS_PARSED.MODULE_CRM_CUSTOMERS_C_BATCH_UPLOAD
                      })
                    }}>
                    <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                      <Icon
                        name="upload"
                        size={switchMerchant ? 10 : 20}
                        color={Colors.whiteColor}
                      />
                      <Text
                        style={{
                          color: Colors.whiteColor,
                          marginLeft: 5,
                          fontSize: switchMerchant ? 10 : 16,
                          fontFamily: 'NunitoSans-Bold',
                        }}>
                        BATCH UPLOAD
                      </Text>
                    </View>
                  </TouchableOpacity>
                </View>

                <TouchableOpacity
                  style={{
                    justifyContent: 'center',
                    flexDirection: 'row',
                    borderWidth: 1,
                    borderColor: Colors.primaryColor,
                    backgroundColor: '#4E9F7D',
                    borderRadius: 5,
                    //width: 160,
                    paddingHorizontal: 10,
                    height: switchMerchant ? 35 : 40,
                    alignItems: 'center',
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 1,
                    zIndex: -1,
                    marginRight: 15,
                    // display: 'none',
                  }}
                  onPress={() => {
                    CommonStore.update((s) => {
                      s.selectedCustomerEdit = null;
                    });

                    navigation.navigate('NewCustomer');

                    logEventAnalytics({
                      eventName: ANALYTICS.MODULE_CRM_CUSTOMERS_C_NEW_CUSTOMER,
                      eventNameParsed: ANALYTICS_PARSED.MODULE_CRM_CUSTOMERS_C_NEW_CUSTOMER
                    })
                  }}>
                  <View style={{}}>
                    <AntDesign
                      name="pluscircle"
                      size={switchMerchant ? 10 : 20}
                      color="#FFFFFF"
                    />
                  </View>
                  <Text
                    style={{
                      marginLeft: 5,
                      color: Colors.primaryColor,
                      fontSize: switchMerchant ? 10 : 16,
                      color: '#FFFFFF',
                      fontFamily: 'NunitoSans-Bold',
                    }}>
                    {/* ADD CUSTOMER */}
                    CUSTOMER
                  </Text>
                </TouchableOpacity>
                <View
                  style={{
                    //marginTop: 10,
                    width: switchMerchant ? 200 : 200,
                    height: switchMerchant ? 35 : 40,
                    backgroundColor: 'white',
                    borderRadius: 5,
                    flexDirection: 'row',
                    alignContent: 'center',
                    alignItems: 'center',
                    shadowColor: '#000',
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 3,
                    borderWidth: 1,
                    borderColor: '#E5E5E5',
                  }}>
                  <Icon
                    name="search"
                    size={switchMerchant ? 13 : 18}
                    color={Colors.primaryColor}
                    style={{ marginLeft: 15 }}
                  />

                  <View style={{ flex: 4 }}>
                    <TextInput
                      underlineColorAndroid={Colors.whiteColor}
                      style={{
                        width: switchMerchant ? 180 : 180,
                        fontSize: switchMerchant ? 10 : 16,
                        fontFamily: 'NunitoSans-Regular',
                        paddingLeft: 5,
                        height: 45,
                      }}
                      clearButtonMode="while-editing"
                      placeholder="Name Phone"
                      placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                      onChangeText={(text) => {
                        setSearch(text);
                        // setSearch(text.trim());

                        logEventAnalytics({
                          eventName: ANALYTICS.MODULE_CRM_CUSTOMERS_TB_SEARCH,
                          eventNameParsed: ANALYTICS_PARSED.MODULE_CRM_CUSTOMERS_TB_SEARCH
                        })
                      }}
                    // value={search}
                    />
                  </View>
                </View>
                <TouchableOpacity
                  disabled={isLoading}
                  style={{
                    justifyContent: 'center',
                    flexDirection: 'row',
                    borderWidth: 1,
                    borderColor: Colors.primaryColor,
                    backgroundColor: '#4E9F7D',
                    borderRadius: 5,
                    //width: 160,
                    paddingHorizontal: 10,
                    height: switchMerchant ? 35 : 40,
                    alignItems: 'center',
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 1,
                    zIndex: -1,
                    // marginRight: 15,
                    marginLeft: 15,
                    // display: 'none',
                  }}
                  onPress={() => {
                    CommonStore.update((s) => {
                      s.isLoading = true;
                    });

                    if (searchList.length > 0) {
                      setSearchList([]);

                      CommonStore.update((s) => {
                        s.isLoading = false;
                      });
                    }
                    else {
                      searchUsers(search);
                    }
                  }}>
                  {isLoading ?
                    <View style={{ justifyContent: 'center', alignItems: 'center' }}>
                      <ActivityIndicator size='small' color={Colors.whiteColor} />
                    </View>
                    :
                    <Text
                      style={{
                        marginLeft: 5,
                        color: Colors.primaryColor,
                        fontSize: switchMerchant ? 10 : 16,
                        color: '#FFFFFF',
                        fontFamily: 'NunitoSans-Bold',
                      }}>
                      {searchList.length > 0 ? 'CLEAR' : 'SEARCH'}
                    </Text>
                  }
                </TouchableOpacity>
              </View>

              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'flex-end',
                  width: windowWidth * 0.87,
                  margin: 10,
                  marginTop: 0,
                  padding: 10,
                  paddingTop: 0,
                  paddingRight: 0,
                  alignItems: 'center',
                  alignSelf: 'center',
                  //paddingRight: windowWidth * 0.015,
                }}>
                <View
                  style={[
                    {
                      //marginRight: Platform.OS === 'ios' ? 0 : 10,
                      // paddingLeft: 15,
                      paddingHorizontal: 15,
                      flexDirection: 'row',
                      alignItems: 'center',
                      borderRadius: 10,
                      paddingVertical: 10,
                      justifyContent: 'center',
                      backgroundColor: Colors.whiteColor,
                      shadowOpacity: 0,
                      shadowColor: '#000',
                      shadowOffset: {
                        width: 0,
                        height: 2,
                      },
                      shadowOpacity: 0.22,
                      shadowRadius: 3.22,
                      elevation: 1,
                    },
                  ]}>
                  <View
                    style={{ alignSelf: 'center', marginRight: 5 }}
                    onPress={() => {
                      setState({
                        pickerMode: 'date',
                        showDateTimePicker: true,
                      });
                    }}>
                    {/* <EvilIcons name="calendar" size={25} color={Colors.primaryColor} /> */}
                    <GCalendar
                      width={switchMerchant ? 15 : 20}
                      height={switchMerchant ? 15 : 20}
                    />
                  </View>

                  <TouchableOpacity
                    onPress={() => {
                      setShowDateTimePicker(true);
                      setShowDateTimePicker1(false);

                      // logEventAnalytics({
                      //   eventName: ANALYTICS.MODULE_REPORT_ADN_C_CAL_START,
                      //   eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_ADN_C_CAL_START
                      // })
                    }}
                    style={{
                      marginHorizontal: 4,
                    }}>
                    <Text
                      style={
                        switchMerchant
                          ? { fontSize: 10, fontFamily: 'NunitoSans-Regular' }
                          : { fontFamily: 'NunitoSans-Regular' }
                      }>
                      {moment(historyStartDate).format('DD MMM yyyy')}
                    </Text>
                  </TouchableOpacity>

                  <Text
                    style={
                      switchMerchant
                        ? { fontSize: 10, fontFamily: 'NunitoSans-Regular' }
                        : { fontFamily: 'NunitoSans-Regular' }
                    }>
                    -
                  </Text>

                  <TouchableOpacity
                    onPress={() => {
                      setShowDateTimePicker(false);
                      setShowDateTimePicker1(true);

                      // logEventAnalytics({
                      //   eventName: ANALYTICS.MODULE_REPORT_ADN_C_CAL_END,
                      //   eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_ADN_C_CAL_END
                      // })
                    }}
                    style={{
                      marginHorizontal: 4,
                    }}>
                    <Text
                      style={
                        switchMerchant
                          ? { fontSize: 10, fontFamily: 'NunitoSans-Regular' }
                          : { fontFamily: 'NunitoSans-Regular' }
                      }>
                      {moment(historyEndDate).format('DD MMM yyyy')}
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>

              <View
                style={{
                  flexDirection: 'row',
                  width: windowWidth * 0.9,
                  marginBottom: 10,
                  justifyContent: 'center',
                }}>
                <View
                  style={{
                    width: switchMerchant ? '35%' : '40%',
                    height: switchMerchant ? 60 : 100,
                    backgroundColor: Colors.tabCyan,
                    borderRadius: 10,
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    paddingHorizontal: switchMerchant ? 25 : 30,
                    paddingVertical: 15,
                    shadowColor: '#000',
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 3,
                    marginRight: 10,
                  }}>
                  <View
                    style={{
                      justifyContent: 'center',
                      height: 60,
                    }}>
                    <Text
                      style={{
                        fontFamily: 'NunitoSans-Bold',
                        color: Colors.whiteColor,
                        fontSize: switchMerchant ? 20 : 28,
                      }}>
                      {overviewCustomersPast30Days}
                    </Text>
                    <Text
                      style={{
                        fontFamily: 'NunitoSans-Regular',
                        color: Colors.whiteColor,
                        fontSize: switchMerchant ? 11 : 13,
                      }}>
                      Past 30 Days Customers
                    </Text>
                  </View>
                  <View>
                    <Ionicon
                      name="people-outline"
                      color={'#F7F7F7'}
                      size={switchMerchant ? 30 : 60}
                      style={{ opacity: 0.6, paddingLeft: 2 }}
                    />
                  </View>
                </View>

                <View
                  style={{
                    width: switchMerchant ? '35%' : '40%',
                    height: switchMerchant ? 60 : 100,
                    backgroundColor: Colors.tabGold,
                    borderRadius: 10,
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    paddingHorizontal: switchMerchant ? 25 : 30,
                    paddingVertical: 15,
                    shadowColor: '#000',
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 3,
                  }}>
                  <View
                    style={{
                      justifyContent: 'center',
                      height: 60,
                    }}>
                    <Text
                      style={{
                        fontFamily: 'NunitoSans-Bold',
                        color: Colors.whiteColor,
                        fontSize: switchMerchant ? 20 : 28,
                      }}>
                      RM{' '}
                      {(!isNaN(totalCustomersSpending)
                        ? totalCustomersSpending
                        : 0
                      )
                        .toFixed(2)
                        .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
                    </Text>
                    <Text
                      style={{
                        fontFamily: 'NunitoSans-Regular',
                        color: Colors.whiteColor,
                        fontSize: switchMerchant ? 11 : 13,
                      }}>
                      Total Customers Spending
                    </Text>
                  </View>
                  <View>
                    <Coins
                      height={switchMerchant ? 30 : 55}
                      width={switchMerchant ? 30 : 55}
                    />
                  </View>
                </View>

                {/* <View style={{
              backgroundColor: Colors.tabCyan, padding: 20, paddingRight: 30, borderRadius: 5,
              shadowColor: '#000',
              shadowOffset: {
                width: 0,
                height: 2,
              },
              shadowOpacity: 0.22,
              shadowRadius: 1.22,
              elevation: 1,
              width: '40%'
            }}>
              <View style={{ flexDirection: 'row' }}>
                <View style={{ flexDirection: 'column', flex: 2.5 }}>
                  <View style={{ flexDirection: 'row', height: 20, }}>
                    <View style={{ backgroundColor: Colors.primaryColor, width: 4, }} />
                    <Text style={{ fontWeight: '400', fontSize: 15, marginLeft: 5, alignSelf: 'center' }}>
                      Past 30 Days Customers
                    </Text>
                  </View>
                  <View style={{ marginTop: 10 }}>
                    <Text style={{ fontSize: 28, fontWeight: '600' }}>
                      {overviewCustomersPast30Days}
                    </Text>
                  </View>
                </View>
                <View style={{ flexDirection: 'column', flex: 1 }}>
                  <Ionicon name="people-outline" color={'#F7F7F7'} size={65} style={{ opacity: 0.6, paddingLeft: 2 }} />
                </View>
              </View>
            </View> */}
              </View>

              <View style={{
                flexDirection: "row",
                width: windowWidth * 0.87,
                alignSelf: 'center',
                marginTop: 10,
                gap: 15,
                paddingHorizontal: 20,
              }}>
                <View style={{ flex: 1, flexDirection: "column", justifyContent: 'center', gap: 10 }}>
                  <View style={{ flexDirection: "row", alignItems: 'center' }}>
                    <View style={{ width: 100 }}>
                      <Text style={{
                        fontSize: switchMerchant ? 10 : 14,
                        fontFamily: 'NunitoSans-Bold',
                        marginLeft: Platform.OS == 'ios' ? 5 : 0,
                      }}>
                        {'Purchase\nAmount(Min): '}
                      </Text>
                    </View>

                    <TextInput
                      style={{
                        height: 40,
                        marginLeft: 10,
                        paddingHorizontal: 15,
                        textAlign: 'center',
                        borderRadius: 10,
                        backgroundColor: Colors.whiteColor,
                        shadowColor: '#000',
                        shadowOffset: {
                          width: 0,
                          height: 2,
                        },
                        width: 100,
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 1,
                        zIndex: 1,
                      }}
                      keyboardType="decimal-pad"
                      placeholder="0"
                      placeholderStyle={{
                        fontFamily: 'NunitoSans-Regular',
                        fontSize: switchMerchant ? 10 : 14,
                      }}
                      placeholderTextColor={Platform.select({
                        ios: '#a9a9a9',
                      })}
                      onChangeText={(text) => {
                        setPurchaseAmountMin(text);
                      }}
                      defaultValue={purchaseAmountMin}
                      selectTextOnFocus
                    />
                  </View>

                  <View style={{ flexDirection: "row", alignItems: 'center' }}>
                    <View style={{ width: 100 }}>
                      <Text style={{
                        fontSize: switchMerchant ? 10 : 14,
                        fontFamily: 'NunitoSans-Bold',
                        marginLeft: Platform.OS == 'ios' ? 5 : 0,
                      }}>
                        {'Purchase\nAmount(Max): '}
                      </Text>
                    </View>

                    <TextInput
                      style={{
                        height: 40,
                        marginLeft: 10,
                        paddingHorizontal: 15,
                        textAlign: 'center',
                        borderRadius: 10,
                        backgroundColor: Colors.whiteColor,
                        shadowColor: '#000',
                        shadowOffset: {
                          width: 0,
                          height: 2,
                        },
                        width: 100,
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 1,
                        zIndex: 1,
                      }}
                      keyboardType="decimal-pad"
                      placeholder="0"
                      placeholderStyle={{
                        fontFamily: 'NunitoSans-Regular',
                        fontSize: switchMerchant ? 10 : 14,
                      }}
                      placeholderTextColor={Platform.select({
                        ios: '#a9a9a9',
                      })}
                      onChangeText={(text) => {
                        setPurchaseAmountMax(text);
                      }}
                      defaultValue={purchaseAmountMax}
                      selectTextOnFocus
                    />
                  </View>
                </View>

                <View style={{ flex: 1, flexDirection: "column", justifyContent: 'center', gap: 10 }}>
                  <View style={{ flexDirection: "row", alignItems: 'center' }}>
                    <View style={{ width: 80 }}>
                      <Text style={{
                        fontSize: switchMerchant ? 10 : 14,
                        fontFamily: 'NunitoSans-Bold',
                        marginLeft: Platform.OS == 'ios' ? 5 : 0,
                      }}>
                        {'Last Visit\nDate(Min): '}
                      </Text>
                    </View>

                    <TouchableOpacity
                      onPress={() => {
                        setShowLastVisitMin(true);
                        setShowLastVisitMax(false);
                      }}
                      style={{
                        height: 40,
                        marginLeft: 10,
                        paddingHorizontal: 15,
                        flexDirection: 'row',
                        alignItems: 'center',
                        borderRadius: 10,
                        paddingVertical: 10,
                        justifyContent: 'center',
                        backgroundColor: Colors.whiteColor,
                        shadowColor: '#000',
                        shadowOffset: {
                          width: 0,
                          height: 2,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 1,
                        zIndex: 1,
                      }}>
                      <GCalendar
                        width={switchMerchant ? 10 : 20}
                        height={switchMerchant ? 10 : 20}
                        style={{ marginRight: 5 }}
                      />
                      <Text
                        style={
                          switchMerchant
                            ? {
                              fontSize: 10,
                              fontFamily: 'NunitoSans-Regular',
                            }
                            : { fontFamily: 'NunitoSans-Regular' }
                        }>
                        {moment(lastVisitMin).format('DD MMM yyyy')}
                      </Text>
                    </TouchableOpacity>
                  </View>

                  <View style={{ flexDirection: "row", alignItems: 'center' }}>
                    <View style={{ width: 80 }}>
                      <Text style={{
                        fontSize: switchMerchant ? 10 : 14,
                        fontFamily: 'NunitoSans-Bold',
                        marginLeft: Platform.OS == 'ios' ? 5 : 0,
                      }}>
                        {'Last Visit\nDate(Max): '}
                      </Text>
                    </View>

                    <TouchableOpacity
                      onPress={() => {
                        setShowLastVisitMin(false);
                        setShowLastVisitMax(true);
                      }}
                      style={{
                        height: 40,
                        marginLeft: 10,
                        paddingHorizontal: 15,
                        flexDirection: 'row',
                        alignItems: 'center',
                        borderRadius: 10,
                        paddingVertical: 10,
                        justifyContent: 'center',
                        backgroundColor: Colors.whiteColor,
                        shadowColor: '#000',
                        shadowOffset: {
                          width: 0,
                          height: 2,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 1,
                        zIndex: 1,
                      }}>
                      <GCalendar
                        width={switchMerchant ? 10 : 20}
                        height={switchMerchant ? 10 : 20}
                        style={{ marginRight: 5 }}
                      />
                      <Text
                        style={
                          switchMerchant
                            ? {
                              fontSize: 10,
                              fontFamily: 'NunitoSans-Regular',
                            }
                            : { fontFamily: 'NunitoSans-Regular' }
                        }>
                        {moment(lastVisitMax).format('DD MMM yyyy')}
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>

                <View style={{ flex: 1, flexDirection: "column", justifyContent: 'center', gap: 10 }}>
                  <View style={{ flexDirection: "row", alignItems: 'center' }}>
                    <View style={{ width: 110 }}>
                      <Text style={{
                        fontSize: switchMerchant ? 10 : 14,
                        fontFamily: 'NunitoSans-Bold',
                        marginLeft: Platform.OS == 'ios' ? 5 : 0,
                      }}>
                        {'Segment Name:'}
                      </Text>
                    </View>

                    <TextInput
                      style={{
                        height: 40,
                        marginLeft: 10,
                        paddingHorizontal: 15,
                        textAlign: 'center',
                        borderRadius: 10,
                        backgroundColor: Colors.whiteColor,
                        shadowColor: '#000',
                        shadowOffset: {
                          width: 0,
                          height: 2,
                        },
                        width: 130,
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 1,
                        zIndex: 1,
                      }}
                      placeholder="Segment"
                      placeholderStyle={{
                        fontFamily: 'NunitoSans-Regular',
                        fontSize: switchMerchant ? 10 : 14,
                      }}
                      placeholderTextColor={Platform.select({
                        ios: '#a9a9a9',
                      })}
                      onChangeText={(text) => {
                        setSegmentName(text);
                      }}
                      defaultValue={segmentName}
                      selectTextOnFocus
                    />
                  </View>

                  <View style={{ flexDirection: "row", alignItems: 'center' }}>
                    <View style={{ width: 110 }}>
                      <Text style={{
                        fontSize: switchMerchant ? 10 : 14,
                        fontFamily: 'NunitoSans-Bold',
                        marginLeft: Platform.OS == 'ios' ? 5 : 0,
                      }}>
                        {'Min. Point(s) Earned:'}
                      </Text>
                    </View>

                    <TextInput
                      style={{
                        height: 40,
                        marginLeft: 10,
                        paddingHorizontal: 15,
                        textAlign: 'center',
                        borderRadius: 10,
                        backgroundColor: Colors.whiteColor,
                        shadowColor: '#000',
                        shadowOffset: {
                          width: 0,
                          height: 2,
                        },
                        width: 130,
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 1,
                        zIndex: 1,
                      }}
                      keyboardType="decimal-pad"
                      placeholder="0"
                      placeholderStyle={{
                        fontFamily: 'NunitoSans-Regular',
                        fontSize: switchMerchant ? 10 : 14,
                      }}
                      placeholderTextColor={Platform.select({
                        ios: '#a9a9a9',
                      })}
                      onChangeText={(text) => {
                        setMinPointEarned(parseFloat(text));
                      }}
                      defaultValue={minPointEarned}
                      selectTextOnFocus
                    />
                  </View>
                </View>

                {/* For spacing */}
                <View style={{ flex: 0.5, flexDirection: "column", gap: 10 }} />

                <View style={{
                  flexDirection: "column",
                  alignItems: 'center',
                  marginTop: 10,
                  justifyContent: 'flex-end',
                  gap: 10,
                }}>
                  {isFiltered && (
                    <TouchableOpacity
                      disabled={isLoading}
                      style={{
                        justifyContent: 'center',
                        flexDirection: 'row',
                        borderWidth: 1,
                        borderColor: Colors.primaryColor,
                        backgroundColor: '#4E9F7D',
                        borderRadius: 5,
                        height: switchMerchant ? 35 : 40,
                        width: 150,
                        paddingHorizontal: 15,
                        alignItems: 'center',
                        shadowOffset: {
                          width: 0,
                          height: 2,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 1,
                        zIndex: -1,
                      }}
                      onPress={() => {
                        if (filteredCRMList.length > 0) {
                          const checkedUsersList = filteredCRMList.filter(user => user.isChecked);
                          if (checkedUsersList.length > 0) {
                            if (checkedUsersList.length > 1000) {
                              Alert.alert('Info', 'Each tag can have a maximum of 1000 customers. Please create another tag if more are needed.');
                            } else {
                              if (segmentName !== '') {
                                Alert.alert(
                                  'Confirmation',
                                  `Are you sure you want to create the segment '${segmentName}' with ${checkedUsersList.length} customers?`,
                                  [
                                    { text: 'Cancel', style: 'cancel' },
                                    {
                                      text: 'OK',
                                      onPress: () => createFilteredCRMSegment()
                                    }
                                  ]
                                );
                              } else {
                                Alert.alert('Info', 'Please fill in segment name first');
                              }
                            }
                          } else {
                            Alert.alert('Info', 'Please select at least one user');
                          }
                        }
                        else {
                          Alert.alert('Info', 'No CRM available')
                        }
                      }}>
                      <AntDesign
                        name="pluscircle"
                        size={switchMerchant ? 10 : 20}
                        color="#FFFFFF"
                      />
                      <Text
                        style={{
                          marginLeft: 5,
                          fontSize: switchMerchant ? 10 : 16,
                          color: '#FFFFFF',
                          fontFamily: 'NunitoSans-Bold',
                          textAlign: 'center',
                        }}>
                        SEGMENT
                      </Text>
                    </TouchableOpacity>
                  )}

                  <TouchableOpacity
                    style={{
                      justifyContent: 'center',
                      flexDirection: 'row',
                      borderWidth: 1,
                      borderColor: isFiltered ? Colors.tabRed : Colors.primaryColor,
                      backgroundColor: isFiltered ? Colors.tabRed : Colors.primaryColor,
                      borderRadius: 5,
                      height: switchMerchant ? 35 : 40,
                      width: 150,
                      paddingHorizontal: 15,
                      alignItems: 'center',
                      shadowOffset: {
                        width: 0,
                        height: 2,
                      },
                      shadowOpacity: 0.22,
                      shadowRadius: 3.22,
                      elevation: 1,
                      zIndex: -1,
                    }}
                    onPress={() => {
                      setIsFiltered(!isFiltered);
                      filterCRMUsers();
                    }}>
                    <Text
                      style={{
                        fontSize: switchMerchant ? 10 : 16,
                        color: '#FFFFFF',
                        fontFamily: 'NunitoSans-Bold',
                        textAlign: 'center',
                      }}>
                      {isFiltered ? 'CANCEL' : 'FILTER'}
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>

              <View style={[styles.list1, {
                backgroundColor: Colors.whiteColor,
                width: Dimensions.get('window').width * 0.87,
                height: Dimensions.get('window').height * 0.75,
                marginTop: 15,
                marginHorizontal: 35,
                marginBottom: 20,
                alignSelf: 'center',
                borderRadius: 5,
                shadowOpacity: 0,
                shadowColor: '#000',
                shadowOffset: {
                  width: 0,
                  height: 2,
                },
                shadowOpacity: 0.22,
                shadowRadius: 3.22,
                elevation: 3,
              }]}>
                <View
                  style={{
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    margin: 10,
                    marginBottom: 10,
                    marginTop: 12,
                    //marginLeft: 20,
                    //marginRight: 20,
                    //backgroundColor: 'red',
                    //height: '10%'
                  }}>
                  <View style={{ width: '55%', justifyContent: 'center' }}>
                    <Text
                      style={{
                        fontFamily: 'NunitoSans-Bold',
                        fontSize: switchMerchant ? 14 : 18,
                        letterSpacing: 0.4,
                      }}>
                      Customer List
                    </Text>
                  </View>
                  <View
                    style={{
                      width: '40%',
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                    }}>
                    {/* Filter bar  */}
                    {/* <TouchableOpacity style={{

                justifyContent: 'center',
                width: '38%'
              }}
                onPress={() => {
                  setVisible(true);
                }}>
                <View style={{ justifyContent: 'center', width: '100%', flexDirection: 'row', borderWidth: 1, borderColor: '#4E9F7D', borderRadius: 3, height: 35, alignItems: 'center' }}>
                  <Feather name='filter' size={18} color='#4E9F7D' />
                  <Text style={{
                    color: '#4E9F7D',
                    fontSize: 14,
                    fontFamily: 'Nunitosans-Bold',
                    marginLeft: 7
                  }}>
                    Manage Filter
                  </Text>
                </View>
              </TouchableOpacity> */}
                    <View style={{}} />
                    {/* Type 1 Search Bar */}
                    {/* <View style={{ flexDirection: 'row', justifyContent: 'center', width: '53%', height: 40, alignItems: 'center', borderWidth: 1, borderColor: '#E5E5E5', borderRadius: 5, alignSelf: 'center' }}>
                <View style={{ flex: 3 }}>
                  <TextInput
                    placeholderTextColor='#737373'
                    style={{ marginLeft: 10, color: '#737373' }}
                    onChangeText={(text) => {
                      setSearch(text);
                    }}
                    defaultValue={search}
                  />
                </View>
                <View style={{ flex: 1, height: '100%', justifyContent: 'center', alignItems: 'center', backgroundColor: Colors.primaryColor, borderWidth: 1, borderColor: '#E5E5E5', borderRadius: 5 }}>
                  <Icon name='search' size={20} color={Colors.whiteColor} />
                </View>
              </View> */}

                    {/* Type 2 Search Bar */}
                    {/* <View style={{
                  marginTop: 10,
                  width: 250,
                  height: 40,
                  backgroundColor: 'white',
                  borderRadius: 5,
                  flexDirection: 'row',
                  alignContent: 'center',
                  alignItems: 'center',
                  shadowColor: '#000',
                  shadowOffset: {
                    width: 0,
                    height: 2,
                  },
                  shadowOpacity: 0.22,
                  shadowRadius: 3.22,
                  elevation: 3,
                }}>
                  <Icon name="search" size={18} color={Colors.primaryColor} style={{ marginLeft: 15 }} />

                  <View style={{ flex: 4 }}>
                    <TextInput
                      underlineColorAndroid={Colors.whiteColor}
                      style={{
                        width: 220,
                        fontSize: 15,
                        fontFamily: 'NunitoSans-Regular',
                        paddingLeft: 5,
                      }}
                      clearButtonMode="while-editing"
                      placeholder=" Search"
                      onChangeText={(text) => {
                        setSearch(text);
                        // setSearch(text.trim());
                      }}
                    // value={search}
                    />
                  </View>
                </View> */}
                  </View>
                </View>

                {/****************List View Here****************/}

                <View style={{ width: '100%', marginTop: 0, borderRadius: 5 }}>
                  <View
                    style={{
                      backgroundColor: Colors.whiteColor,
                      padding: 5,
                      paddingTop: 0,
                      height: '95%',
                      borderRadius: 5,
                      // shadowOffset: {
                      // width: 0,
                      // height: 1,
                      // },
                      // shadowOpacity: 0.22,
                      // shadowRadius: 2.22,
                      // elevation: 1,
                    }}>
                    <View
                      style={{
                        marginTop: 0,
                        flexDirection: 'row',
                        borderBottomWidth: 1,
                        borderColor: '#E5E5E5',
                        height: 57,
                      }}>
                      {isFiltered ?
                        <View style={{ width: '3%', alignItems: 'center', }}>
                          <CheckBox
                            value={isSelectedAll} // 'Select All' checkbox state
                            onValueChange={(newValue) => {
                              setIsSelectedAll(newValue);
                              const updatedUsers = filteredCRMList.map((user) => ({
                                ...user,
                                isChecked: newValue, // Select or deselect all users
                              }));
                              OutletStore.update((s) => {
                                s.filteredCRMList = updatedUsers;
                              })
                            }}
                          />
                        </View>
                        : <></>}
                      <View
                        style={{
                          flexDirection: 'row',
                          width: isFiltered ? '17%' : '19%',
                          borderRightWidth: 0,
                          borderRightColor: 'lightgrey',
                          alignItems: 'center',
                          justifyContent: 'flex-start',
                          marginLeft: 0,
                        }}>
                        <TouchableOpacity
                          onPress={() => {
                            if (
                              sort ===
                              USER_SORT_FIELD_TYPE.NAME_ASC
                            ) {
                              setSort(
                                USER_SORT_FIELD_TYPE.NAME_DESC,
                              );
                            } else {
                              setSort(
                                USER_SORT_FIELD_TYPE.NAME_ASC,
                              );
                            }
                            logEventAnalytics({
                              eventName: ANALYTICS.MODULE_CRM_CUSTOMERS_LIST_C_NAME_ID,
                              eventNameParsed: ANALYTICS_PARSED.MODULE_CRM_CUSTOMERS_LIST_C_NAME_ID
                            })
                          }}>
                          <View style={{ flexDirection: 'row' }}>
                            <Text
                              numberOfLines={2}
                              style={{
                                paddingLeft: 5,
                                fontSize: switchMerchant ? 10 : 14,
                                fontFamily: 'NunitoSans-Bold',
                                marginLeft: Platform.OS == 'ios' ? 5 : 0,
                              }}>
                              {'Name & User ID\n'}
                            </Text>
                            <View style={{ marginLeft: '3%' }}>
                              <Entypo
                                name="triangle-up"
                                size={switchMerchant ? 8 : 12}
                                color={
                                  sort ===
                                    USER_SORT_FIELD_TYPE.NAME_ASC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor} />

                              <Entypo
                                name="triangle-down"
                                size={switchMerchant ? 8 : 12}
                                color={
                                  sort ===
                                    USER_SORT_FIELD_TYPE.NAME_DESC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor} />
                            </View>
                          </View>
                        </TouchableOpacity>
                      </View>

                      <View
                        style={{
                          flexDirection: 'row',
                          width: '20%',
                          borderRightWidth: 0,
                          borderRightColor: 'lightgrey',
                          alignItems: 'center',
                          justifyContent: 'flex-start',
                          marginLeft: 0,
                        }}>
                        <View style={{ flexDirection: 'column' }}>
                          <Text
                            numberOfLines={2}
                            style={{
                              fontSize: switchMerchant ? 10 : 14,
                              fontFamily: 'NunitoSans-Bold',
                            }}>
                            {'Contact Info\n'}
                          </Text>
                        </View>
                        <View style={{ marginLeft: '3%' }}>
                          {/* <TouchableOpacity onPress = {() => { 
                      setSort(USER_SORT_FIELD_TYPE.NUMBER_ASC)
                    }}>
                    <Entypo name='triangle-up' size={12} color={Colors.descriptionColor}></Entypo>
                    </TouchableOpacity> 
                    <TouchableOpacity onPress = {() => { 
                       setSort(USER_SORT_FIELD_TYPE.NUMBER_DESC)
                    }}>
                    <Entypo name='triangle-down' size={12} color={Colors.descriptionColor} ></Entypo>
                    </TouchableOpacity>  */}
                        </View>
                      </View>

                      <View
                        style={{
                          flexDirection: 'row',
                          width: '11%',
                          borderRightWidth: 0,
                          borderRightColor: 'lightgrey',
                          alignItems: 'center',
                          justifyContent: 'flex-start',
                          marginLeft: 0,
                        }}>
                        <TouchableOpacity
                          onPress={() => {
                            if (
                              sort ===
                              USER_SORT_FIELD_TYPE.GENDER_ASC
                            ) {
                              setSort(
                                USER_SORT_FIELD_TYPE.GENDER_DESC,
                              );
                            } else {
                              setSort(
                                USER_SORT_FIELD_TYPE.GENDER_ASC,
                              );
                            }

                            logEventAnalytics({
                              eventName: ANALYTICS.MODULE_CRM_CUSTOMERS_LIST_C_GENDER,
                              eventNameParsed: ANALYTICS_PARSED.MODULE_CRM_CUSTOMERS_LIST_C_GENDER
                            })
                          }}>
                          <View style={{ flexDirection: 'row' }}>
                            <View style={{ flexDirection: 'column' }}>
                              <Text
                                numberOfLines={2}
                                style={{
                                  fontSize: switchMerchant ? 10 : 14,
                                  fontFamily: 'NunitoSans-Bold',
                                }}>
                                {'Gender\n'}
                              </Text>
                            </View>
                            <View style={{ marginLeft: '3%' }}>
                              <Entypo
                                name="triangle-up"
                                size={switchMerchant ? 8 : 12}
                                color={
                                  sort ===
                                    USER_SORT_FIELD_TYPE.GENDER_ASC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor} />

                              <Entypo
                                name="triangle-down"
                                size={switchMerchant ? 8 : 12}
                                color={
                                  sort ===
                                    USER_SORT_FIELD_TYPE.GENDER_DESC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor} />
                            </View>
                          </View>
                        </TouchableOpacity>
                      </View>

                      <View
                        style={{
                          flexDirection: 'row',
                          width: '12%',
                          borderRightWidth: 0,
                          borderRightColor: 'lightgrey',
                          alignItems: 'center',
                          justifyContent: 'flex-start',
                          marginLeft: 0,
                        }}>
                        <TouchableOpacity
                          onPress={() => {
                            if (
                              sort ===
                              USER_SORT_FIELD_TYPE.DOB_ASC
                            ) {
                              setSort(
                                USER_SORT_FIELD_TYPE.DOB_DESC,
                              );
                            } else {
                              setSort(
                                USER_SORT_FIELD_TYPE.DOB_ASC,
                              );
                            }

                            logEventAnalytics({
                              eventName: ANALYTICS.MODULE_CRM_CUSTOMERS_LIST_C_DOB,
                              eventNameParsed: ANALYTICS_PARSED.MODULE_CRM_CUSTOMER_LIST_C_DOB
                            })
                          }}>
                          <View style={{ flexDirection: 'row' }}>
                            <View style={{ flexDirection: 'column' }}>
                              <Text
                                numberOfLines={2}
                                style={{
                                  fontSize: switchMerchant ? 10 : 14,
                                  fontFamily: 'NunitoSans-Bold',
                                }}>
                                {'DOB\n'}
                              </Text>
                            </View>
                            <View style={{ marginLeft: '3%' }}>
                              <Entypo
                                name="triangle-up"
                                size={switchMerchant ? 8 : 12}
                                color={
                                  sort ===
                                    USER_SORT_FIELD_TYPE.DOB_ASC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor} />

                              <Entypo
                                name="triangle-down"
                                size={switchMerchant ? 8 : 12}
                                color={
                                  sort ===
                                    USER_SORT_FIELD_TYPE.DOB_DESC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor} />
                            </View>
                          </View>
                        </TouchableOpacity>
                      </View>

                      <View
                        style={{
                          flexDirection: 'row',
                          width: '10%',
                          borderRightWidth: 0,
                          borderRightColor: 'lightgrey',
                          alignItems: 'center',
                          justifyContent: 'flex-start',
                          marginLeft: 0,
                        }}>
                        <TouchableOpacity
                          onPress={() => {
                            if (
                              sort ===
                              USER_SORT_FIELD_TYPE.RACE_ASC
                            ) {
                              setSort(
                                USER_SORT_FIELD_TYPE.RACE_DESC,
                              );
                            } else {
                              setSort(
                                USER_SORT_FIELD_TYPE.RACE_ASC,
                              );
                            }
                            logEventAnalytics({
                              eventName: ANALYTICS.MODULE_CRM_CUSTOMERS_LIST_C_RACE,
                              eventNameParsed: ANALYTICS_PARSED.MODULE_CRM_CUSTOMERS_LIST_C_RACE
                            })
                          }}>
                          <View style={{ flexDirection: 'row' }}>
                            <View style={{ flexDirection: 'column' }}>
                              <Text
                                numberOfLines={2}
                                style={{
                                  fontSize: switchMerchant ? 10 : 14,
                                  fontFamily: 'NunitoSans-Bold',
                                }}>
                                {'Race\n'}
                              </Text>
                            </View>
                            <View style={{ marginLeft: '3%' }}>
                              <Entypo
                                name="triangle-up"
                                size={switchMerchant ? 8 : 12}
                                color={
                                  sort ===
                                    USER_SORT_FIELD_TYPE.RACE_ASC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor} />

                              <Entypo
                                name="triangle-down"
                                size={switchMerchant ? 8 : 12}
                                color={
                                  sort ===
                                    USER_SORT_FIELD_TYPE.RACE_DESC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor} />
                            </View>
                          </View>
                        </TouchableOpacity>
                      </View>
                      <View
                        style={{
                          flexDirection: 'row',
                          width: '9%',
                          borderRightWidth: 0,
                          borderRightColor: 'lightgrey',
                          alignItems: 'center',
                          justifyContent: 'flex-start',
                          marginLeft: 0,
                        }}>
                        <TouchableOpacity
                          onPress={() => {
                            if (
                              sort ===
                              USER_SORT_FIELD_TYPE.TIER_ASC
                            ) {
                              setSort(
                                USER_SORT_FIELD_TYPE.TIER_DESC,
                              );
                            } else {
                              setSort(
                                USER_SORT_FIELD_TYPE.TIER_ASC,
                              );
                            }
                          }}>
                          <View style={{ flexDirection: 'row' }}>
                            <View style={{ flexDirection: 'column' }}>
                              <Text
                                numberOfLines={2}
                                style={{
                                  fontSize: switchMerchant ? 10 : 14,
                                  fontFamily: 'NunitoSans-Bold',
                                }}>
                                {'Tier\n'}
                              </Text>
                            </View>
                            <View style={{ marginLeft: '3%' }}>
                              <Entypo
                                name="triangle-up"
                                size={switchMerchant ? 8 : 12}
                                color={
                                  sort ===
                                    USER_SORT_FIELD_TYPE.TIER_ASC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor} />

                              <Entypo
                                name="triangle-down"
                                size={switchMerchant ? 8 : 12}
                                color={
                                  sort ===
                                    USER_SORT_FIELD_TYPE.TIER_DESC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor} />
                            </View>
                          </View>
                        </TouchableOpacity>
                      </View>

                      <View
                        style={{
                          flexDirection: 'row',
                          width: '13%',
                          borderRightWidth: 0,
                          borderRightColor: 'lightgrey',
                          alignItems: 'center',
                          justifyContent: 'flex-start',
                          marginLeft: 0,
                        }}>
                        <TouchableOpacity
                          onPress={() => {
                            if (
                              sort ===
                              // USER_SORT_FIELD_TYPE.STATUS_ASC
                              USER_SORT_FIELD_TYPE.PAST_SPENT_ASC
                            ) {
                              setSort(
                                USER_SORT_FIELD_TYPE.PAST_SPENT_DESC,
                              );
                            } else {
                              setSort(
                                USER_SORT_FIELD_TYPE.PAST_SPENT_ASC,
                              );
                            }

                            logEventAnalytics({
                              eventName: ANALYTICS.MODULE_CRM_CUSTOMERS_LIST_C_AVG_SPENDING,
                              eventNameParsed: ANALYTICS_PARSED.MODULE_CRM_CUSTOMERS_LIST_C_AVG_SPENDING
                            })
                          }}>
                          <View style={{ flexDirection: 'row' }}>
                            <View style={{ flexDirection: 'column' }}>
                              <Text
                                numberOfLines={2}
                                style={{
                                  fontSize: switchMerchant ? 10 : 14,
                                  fontFamily: 'NunitoSans-Bold',
                                }}>
                                {'Avg Spending\nPer 30days'}
                              </Text>
                            </View>
                            <View style={{ marginLeft: '3%' }}>
                              <Entypo
                                name="triangle-up"
                                size={switchMerchant ? 8 : 12}
                                color={
                                  sort ===
                                    USER_SORT_FIELD_TYPE.PAST_SPENT_ASC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor} />

                              <Entypo
                                name="triangle-down"
                                size={switchMerchant ? 8 : 12}
                                color={
                                  sort ===
                                    USER_SORT_FIELD_TYPE.PAST_SPENT_DESC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor} />
                            </View>
                          </View>
                        </TouchableOpacity>
                      </View>
                      <View style={{ flex: 0.4 }} />
                    </View>
                    {!isLoading ?
                      <>
                        {searchList.length > 0 ?
                          <FlatList
                            ////THIS IS FOR SEARCH////
                            showsVerticalScrollIndicator={false}
                            data={sortCRMUsers(searchList)}
                            // extraData={renderItem}
                            renderItem={renderItem}
                            keyExtractor={(item, index) => String(index)}
                            style={{ marginTop: 0 }}
                          // initialNumToRender={4}
                          />
                          :
                          isFiltered ?
                            (filteredCRMList.length > 0 ?
                              <FlatList
                                ////THIS IS FOR SEARCH////
                                showsVerticalScrollIndicator={false}
                                data={sortCRMUsers(filteredCRMList
                                ).slice(
                                  (currentPage - 1) * perPage,
                                  currentPage * perPage,
                                )}
                                // extraData={renderItem}
                                renderItem={renderItem}
                                keyExtractor={(item, index) => String(index)}
                                style={{ marginTop: 0 }}
                              // initialNumToRender={4}
                              />
                              :
                              <View
                                style={{
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                  height: '71%',
                                }}>
                                <Text style={{ color: Colors.descriptionColor }}>
                                  - No Data Available -
                                </Text>
                              </View>
                            )
                            : (
                              lastDocArr.length <= 0 ? (
                                // <View style={{ borderTopWidth: 1 }}>
                                (<FlatList
                                  ////THIS IS FOR SEARCH////
                                  showsVerticalScrollIndicator={false}
                                  data={sortCRMUsers(
                                    headerSorting.filter((item) => {
                                      // if (search !== '') {
                                      //   const searchLowerCase = search.toLowerCase();

                                      //   if (
                                      //     (item.email || '')
                                      //       .toLowerCase()
                                      //       .includes(searchLowerCase) ||
                                      //     (item.name || '')
                                      //       .toLowerCase()
                                      //       .includes(searchLowerCase) ||
                                      //     (item.uniqueName || '')
                                      //       .toLowerCase()
                                      //       .includes(searchLowerCase) ||
                                      //     (item.number || '')
                                      //       .toLowerCase()
                                      //       .includes(searchLowerCase)
                                      //   ) {
                                      //     return true;
                                      //   }

                                      //   return false;
                                      // } else {
                                      return true;
                                      // }
                                    }),
                                    sort,
                                  ).slice(
                                    (currentPage - 1) * perPage,
                                    currentPage * perPage,
                                  )}
                                  // extraData={renderItem}
                                  renderItem={renderItem}
                                  keyExtractor={(item, index) => String(index)}
                                  style={{ marginTop: 0 }}
                                // initialNumToRender={4}
                                />)
                              ) : // </View>

                                <FlatList
                                  ////THIS IS FOR SEARCH////
                                  showsVerticalScrollIndicator={false}
                                  data={sortCRMUsers(
                                    crmNextBatch.filter((item) => {
                                      // if (search !== '') {
                                      //   const searchLowerCase = search.toLowerCase();

                                      //   if (
                                      //     (item.email || '')
                                      //       .toLowerCase()
                                      //       .includes(searchLowerCase) ||
                                      //     (item.name || '')
                                      //       .toLowerCase()
                                      //       .includes(searchLowerCase) ||
                                      //     (item.uniqueName || '')
                                      //       .toLowerCase()
                                      //       .includes(searchLowerCase) ||
                                      //     (item.number || '')
                                      //       .toLowerCase()
                                      //       .includes(searchLowerCase)
                                      //   ) {
                                      //     return true;
                                      //   }

                                      //   return false;
                                      // } else {
                                      return true;
                                      // }
                                    }),
                                    sort,
                                  ).slice(
                                    (currentPage - 1) * perPage,
                                    currentPage * perPage,
                                  )}
                                  // extraData={renderItem}
                                  renderItem={renderItem}
                                  keyExtractor={(item, index) => String(index)}
                                  style={{ marginTop: 0 }}
                                // initialNumToRender={4}
                                />
                            )}
                      </>
                      :
                      <View style={{ justifyContent: 'center', alignItems: 'center', marginTop: windowHeight * 0.3 }}>
                        <ActivityIndicator size='large' color={Colors.primaryColor} />
                      </View>
                    }
                    {/* {searchList ? (

                                <FlatList
                                    data={lists}
                                    extraData={lists}
                                    renderItem={renderSearchItem}
                                    keyExtractor={(item, index) => String(index)}
                                    initialNumToRender={8}
                                />

                            ) : null} */}
                  </View>
                </View>
              </View>
              {searchList.length <= 0 ?
                <View
                  style={{
                    flexDirection: 'row',
                    //marginTop: 10,
                    width: windowWidth * 0.87,
                    alignItems: 'center',
                    alignSelf: 'center',
                    justifyContent: 'space-between',
                    top:
                      Platform.OS == 'ios'
                        ? pushPagingToTop && keyboardHeight > 0
                          ? -keyboardHeight * 0.94
                          : 0
                        : 0,
                    // backgroundColor: pushPagingToTop && keyboardHeight > 0 ? Colors.highlightColor : null,
                    // borderWidth: pushPagingToTop && keyboardHeight > 0 ? 1 : 0,
                    // borderColor: pushPagingToTop && keyboardHeight > 0 ? '#E5E5E5' : null,
                    borderRadius: pushPagingToTop && keyboardHeight > 0 ? 8 : 0,
                    paddingHorizontal:
                      pushPagingToTop && keyboardHeight > 0 ? 10 : 0,
                    // shadowOffset: {
                    //   width: 0,
                    //   height: 1,
                    // },
                    // shadowOpacity: 0.22,
                    // shadowRadius: 3.22,
                    // elevation: 1,
                    marginHorizontal: 25,
                    marginBottom: 20,
                    marginTop: switchMerchant ? 5 : 0,
                  }}>

                  <View style={{ flexDirection: 'row', alignItems: 'center', }}>
                    <TouchableOpacity
                      disabled={isLoading || search.length > 0}
                      style={{
                        width: switchMerchant ? 30 : 45,
                        height: switchMerchant ? 20 : 28,
                        backgroundColor: Colors.primaryColor,
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                      onPress={async () => {
                        CommonStore.update((s) => {
                          s.isLoading = true;
                        })
                        //////////////////////////////////////

                        if (lastDocArr.length <= 0) {
                          // can proceed, from page 0, start fresh

                          CommonStore.update((s) => {
                            s.isLoading = false;
                          })
                          Alert.alert('Info', 'Already showing the first page')
                        }
                        else {
                          // can proceed

                          await fetchNextBatchCRMV2(currOutletId, lastDocArr, true, global.defaultLastUser, crmUsers);
                        }
                      }}>
                      <MaterialIcons
                        name="keyboard-arrow-left"
                        size={switchMerchant ? 20 : 25}
                        style={{ color: Colors.whiteColor }}
                      />
                    </TouchableOpacity>
                    <Text
                      style={{
                        fontSize: switchMerchant ? 10 : 14,
                        fontFamily: 'NunitoSans-Bold',
                        marginHorizontal: 10,
                      }}>
                      Current showing {lastDocArr.length * 500}-{(lastDocArr.length + 1) * 500} customers
                    </Text>
                    {currOutlet.cuc ?
                      <View>
                        <Text
                          style={{
                            fontSize: switchMerchant ? 10 : 14,
                            fontFamily: 'NunitoSans-Bold',
                            marginRight: 10,
                          }}>
                          {`(${currOutlet.cuc} customers)`}
                        </Text>
                      </View>
                      : <></>}
                    <TouchableOpacity
                      disabled={isLoading || search.length > 0}
                      style={{
                        width: switchMerchant ? 30 : 45,
                        height: switchMerchant ? 20 : 28,
                        backgroundColor: Colors.primaryColor,
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                      onPress={async () => {
                        CommonStore.update((s) => {
                          s.isLoading = true;
                        })
                        ////////////////////////////

                        if (lastDocArr.length <= 0) {
                          // can proceed, from page 0, start fresh

                          if (crmUsers.length < 500) {
                            // means no need fetch at all

                            Alert.alert('Info', 'No more CRM users found.');
                            CommonStore.update((s) => {
                              s.isLoading = false;
                            })
                          }
                          else {
                            await fetchNextBatchCRMV2(currOutletId, lastDocArr, false, global.defaultLastUser);
                          }
                        }
                        else {
                          // can proceed, from page 1
                          await fetchNextBatchCRMV2(currOutletId, lastDocArr, false, global.defaultLastUser);
                        }
                      }}>
                      <MaterialIcons
                        name="keyboard-arrow-right"
                        size={switchMerchant ? 20 : 25}
                        style={{ color: Colors.whiteColor }}
                      />
                    </TouchableOpacity>
                  </View>

                  <View style={{ justifyContent: 'flex-end', flexDirection: 'row', alignItems: 'center', }}>
                    <Text
                      style={{
                        fontSize: switchMerchant ? 10 : 14,
                        fontFamily: 'NunitoSans-Bold',
                        marginRight: 10,
                      }}>
                      Page
                    </Text>
                    <View
                      style={{
                        width: switchMerchant ? 65 : 70,
                        height: switchMerchant ? 20 : 35,
                        backgroundColor: Colors.whiteColor,
                        borderRadius: 10,
                        justifyContent: 'center',
                        paddingHorizontal: 22,
                        borderWidth: 1,
                        borderColor: '#E5E5E5',
                      }}>
                      {console.log('currentPage')}
                      {console.log(currentPage)}
                      {console.log('----------------------------------------------------')}
                      {console.log('list of crm', crmNextBatch)}

                      <TextInput
                        onChangeText={(text) => {
                          var currentPageTemp = text.length > 0 ? parseInt(text) : 1;

                          setCurrentPage(
                            currentPageTemp > pageCount
                              ? pageCount
                              : currentPageTemp < 1
                                ? 1
                                : currentPageTemp,
                          );

                          logEventAnalytics({
                            eventName: ANALYTICS.MODULE_CRM_CUSTOMERS_LIST_TB_PAGE,
                            eventNameParsed: ANALYTICS_PARSED.MODULE_CRM_CUSTOMERS_LIST_TB_PAGE
                          })
                        }}
                        placeholder={currentPage.toString()}
                        placeholderStyle={{
                          fontSize: switchMerchant ? 10 : 14,
                          fontFamily: 'NunitoSans-Regular',
                        }}
                        placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                        style={{
                          color: 'black',
                          // fontFamily: 'NunitoSans-Regular',
                          fontSize: switchMerchant ? 10 : 14,
                          fontFamily: 'NunitoSans-Regular',
                          marginTop: Platform.OS === 'ios' ? 0 : -15,
                          marginBottom: Platform.OS === 'ios' ? 0 : -15,
                          textAlign: 'center',
                          width: '100%',
                        }}
                        value={currentPage.toString()}
                        defaultValue={currentPage.toString()}
                        keyboardType={'numeric'}
                        onFocus={() => {
                          setPushPagingToTop(true);
                        }}
                      />
                    </View>
                    <Text
                      style={{
                        fontSize: switchMerchant ? 10 : 14,
                        fontFamily: 'NunitoSans-Regular',
                        marginHorizontal: 10,
                      }}>
                      of {pageCount}
                    </Text>
                    <TouchableOpacity
                      disabled={isLoading || search.length > 0}
                      style={{
                        width: switchMerchant ? 30 : 45,
                        height: switchMerchant ? 20 : 28,
                        backgroundColor: Colors.primaryColor,
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                      onPress={() => {
                        prevPage();

                        logEventAnalytics({
                          eventName: ANALYTICS.MODULE_CRM_CUSTOMERS_LIST_C_PREV_BUTTON,
                          eventNameParsed: ANALYTICS_PARSED.MODULE_CRM_CUSTOMERS_LIST_C_PREV_BUTTON
                        })
                      }}>
                      <MaterialIcons
                        name="keyboard-arrow-left"
                        size={switchMerchant ? 20 : 25}
                        style={{ color: Colors.whiteColor }}
                      />
                    </TouchableOpacity>
                    <TouchableOpacity
                      disabled={isLoading || search.length > 0}
                      style={{
                        width: switchMerchant ? 30 : 45,
                        height: switchMerchant ? 20 : 28,
                        backgroundColor: Colors.primaryColor,
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                      onPress={() => {
                        nextPage();

                        logEventAnalytics({
                          eventName: ANALYTICS.MODULE_CRM_CUSTOMERS_LIST_C_NEXT_BUTTON,
                          eventNameParsed: ANALYTICS_PARSED.MODULE_CRM_CUSTOMERS_LIST_C_NEXT_BUTTON
                        })
                      }}>
                      <MaterialIcons
                        name="keyboard-arrow-right"
                        size={switchMerchant ? 20 : 25}
                        style={{ color: Colors.whiteColor }}
                      />
                    </TouchableOpacity>
                  </View>
                </View>
                : <></>}
            </KeyboardAvoidingView>
          </ScrollView>
        </ScrollView>
      </View>
    </UserIdleWrapper>)
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.highlightColor,
    flexDirection: 'row',
  },
  modalViewUploadOptions: {
    height: Dimensions.get('window').width * 0.2,
    width: Dimensions.get('window').width * 0.3,
    backgroundColor: Colors.whiteColor,
    borderRadius: 12,
    padding: Dimensions.get('window').width * 0.03,
    alignItems: 'center',
    justifyContent: 'center',
  },
  list: {
    backgroundColor: Colors.whiteColor,
    width: Dimensions.get('window').width * 0.85,
    height: Dimensions.get('window').height * 0.8,
    marginTop: 5,
    marginHorizontal: 20,
    alignSelf: 'center',
    borderRadius: 5,
    shadowOpacity: 0,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 3,
    marginBottom: 20,
  },
  list1: {
    backgroundColor: Colors.whiteColor,
    width: Dimensions.get('window').width * 0.87,
    height: Dimensions.get('window').height * 0.75,
    marginTop: 15,
    marginHorizontal: 35,
    marginBottom: 20,
    alignSelf: 'center',
    borderRadius: 5,
    shadowOpacity: 0,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 3,
  },
  listItem: {
    marginLeft: 20,
    color: Colors.descriptionColor,
    fontSize: 16,
  },
  sidebar: {
    //width: windowWidth * Styles.sideBarWidth,
    // shadowColor: '#000',
    // shadowOffset: {
    //   width: 0,
    //   height: 8,
    // },
    // shadowOpacity: 0.44,
    // shadowRadius: 10.32,

    // elevation: 16,
  },
  content: {
    padding: 16,
  },
  circleIcon: {
    width: 30,
    height: 30,
    // resizeMode: 'contain',
    marginRight: 10,
    alignSelf: 'center',
  },
  titleList: {
    backgroundColor: '#ffffff',
    flexDirection: 'row',
    paddingVertical: 20,
    paddingHorizontal: 20,
    //marginTop: 20,
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,

    alignItems: 'center',

    // shadowOpacity: 0,
    // shadowColor: '#000',
    // shadowOffset: {
    //   width: 0,
    //   height: 2,
    // },
    // shadowOpacity: 0.22,
    // shadowRadius: 3.22,
    // elevation: 3,
  },
  textTitle: {
    fontSize: 16,
    fontFamily: 'NunitoSans-Bold',
  },
  textItem: {
    fontSize: 14,
    fontFamily: 'NunitoSans-Regular',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'center',
    // alignContent: 'center',
    alignItems: 'center',
    backgroundColor: 'white',
    padding: 20,
    paddingTop: 15,
    marginTop: 0,
    width: '100%',

    shadowOpacity: 0,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 7,
  },
  addVoucher: {
    marginTop: 0,
    //justifyContent: 'center',
    alignItems: 'center',
    //alignContent: 'center',
    width: Dimensions.get('window').width * 0.78,
    backgroundColor: Colors.whiteColor,
    // marginRight: 100,

    borderRadius: 4,

    shadowOpacity: 0,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 1,
  },
  textInput: {
    height: 50,
    paddingHorizontal: 20,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    marginBottom: 20,
    width: 300,
  },
  textInput1: {
    width: 250,
    height: 40,
    backgroundColor: 'white',
    borderRadius: 10,
    // marginLeft: '53%',
    flexDirection: 'row',
    alignContent: 'center',
    alignItems: 'center',

    marginRight: Dimensions.get('window').width * Styles.sideBarWidth,

    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 3,
  },
  headerLeftStyle: {
    width: Dimensions.get('window').width * 0.17,
    justifyContent: 'center',
    alignItems: 'center',
  },
  addNewView: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginBottom: -10,
    marginTop: 5,
    width: '97%',
    alignSelf: 'flex-end',
  },
  addButtonView: {
    justifyContent: 'center',
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: Colors.primaryColor,
    backgroundColor: '#4E9F7D',
    borderRadius: 5,
    width: 155,
    height: 40,
    alignItems: 'center',
  },
  editButtonView: {
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: Colors.primaryColor,
    backgroundColor: '#4E9F7D',
    borderRadius: 5,
    width: 74,
    height: 35,
    alignItems: 'center',
  },
  ManageFilterBox: {
    //width: Dimensions.get('window').width * 0.4,
    //height: windowHeight * 0.7,
    //borderRadius: 10,
    backgroundColor: Colors.whiteColor,
    //justifyContent: 'space-between'
  },
  ManageFilterBox1: {
    width: Dimensions.get('window').width * 0.35,
    height: Dimensions.get('window').height * 0.55,
    //borderRadius: 10,
    backgroundColor: Colors.whiteColor,
    justifyContent: 'center',
    borderColor: '#E5E5E5',
    borderWidth: 1,
    alignItems: 'center',
    alignSelf: 'center',
    //padding: 10,
    margin: 15,
  },

  modalContainer: {
    flex: 1,
    backgroundColor: Colors.modalBgColor,
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalView: {
    height: Dimensions.get('window').width * 0.35,
    width: Dimensions.get('window').width * 0.4,
    backgroundColor: Colors.whiteColor,
    borderRadius: 12,
    padding: Dimensions.get('window').width * 0.03,
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalView1: {
    height: Dimensions.get('window').width * 0.3,
    width: Dimensions.get('window').width * 0.4,
    backgroundColor: Colors.whiteColor,
    borderRadius: 12,
    padding: Dimensions.get('window').width * 0.03,
    alignItems: 'center',
    justifyContent: 'center',
  },
  closeButton: {
    position: 'absolute',
    right: Dimensions.get('window').width * 0.04,
    top: Dimensions.get('window').width * 0.04,

    elevation: 1000,
    zIndex: 1000,
  },
  modalTitle: {
    alignItems: 'center',
    top: '20%',
    position: 'absolute',
  },
  modalBody: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalTitleText: {
    fontFamily: 'NunitoSans-Bold',
    textAlign: 'center',
    fontSize: 20,
  },
  modalTitleText1: {
    fontFamily: 'NunitoSans-Bold',
    textAlign: 'center',
    fontSize: 18,
  },
  modalDescText: {
    fontFamily: 'NunitoSans-SemiBold',
    fontSize: 18,
    color: Colors.fieldtTxtColor,
  },
  modalBodyText: {
    flex: 1,
    fontFamily: 'NunitoSans-SemiBold',
    fontSize: 25,
    width: '20%',
  },
  modalSaveButton: {
    width: Dimensions.get('window').width * 0.18,
    backgroundColor: Colors.fieldtBgColor,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 8,

    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 1,

    marginVertical: 10,
  },

  submitText: {
    height: 40,
    paddingHorizontal: 20,
    flexDirection: 'row',
    color: '#4cd964',
    textAlign: 'center',
    borderRadius: 10,
    borderWidth: 1,
    borderColor: Colors.primaryColor,
    justifyContent: 'center',
    alignContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  textInput9: {
    fontFamily: 'NunitoSans-Regular',
    width: 430,
    height: 50,
    //flex: 1,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
  },
});

export default CrmScreen;
